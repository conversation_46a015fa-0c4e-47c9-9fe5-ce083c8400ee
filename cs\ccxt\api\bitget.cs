// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bitget : Exchange
{
    public bitget (object args = null): base(args) {}

    public async Task<object> publicCommonGetV2PublicAnnoucements (object parameters = null)
    {
        return await this.callAsync ("publicCommonGetV2PublicAnnoucements",parameters);
    }

    public async Task<object> publicCommonGetV2PublicTime (object parameters = null)
    {
        return await this.callAsync ("publicCommonGetV2PublicTime",parameters);
    }

    public async Task<object> publicSpotGetSpotV1NoticeQueryAllNotices (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1NoticeQueryAllNotices",parameters);
    }

    public async Task<object> publicSpotGetSpotV1PublicTime (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1PublicTime",parameters);
    }

    public async Task<object> publicSpotGetSpotV1PublicCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1PublicCurrencies",parameters);
    }

    public async Task<object> publicSpotGetSpotV1PublicProducts (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1PublicProducts",parameters);
    }

    public async Task<object> publicSpotGetSpotV1PublicProduct (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1PublicProduct",parameters);
    }

    public async Task<object> publicSpotGetSpotV1MarketTicker (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1MarketTicker",parameters);
    }

    public async Task<object> publicSpotGetSpotV1MarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1MarketTickers",parameters);
    }

    public async Task<object> publicSpotGetSpotV1MarketFills (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1MarketFills",parameters);
    }

    public async Task<object> publicSpotGetSpotV1MarketFillsHistory (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1MarketFillsHistory",parameters);
    }

    public async Task<object> publicSpotGetSpotV1MarketCandles (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1MarketCandles",parameters);
    }

    public async Task<object> publicSpotGetSpotV1MarketDepth (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1MarketDepth",parameters);
    }

    public async Task<object> publicSpotGetSpotV1MarketSpotVipLevel (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1MarketSpotVipLevel",parameters);
    }

    public async Task<object> publicSpotGetSpotV1MarketMergeDepth (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1MarketMergeDepth",parameters);
    }

    public async Task<object> publicSpotGetSpotV1MarketHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1MarketHistoryCandles",parameters);
    }

    public async Task<object> publicSpotGetSpotV1PublicLoanCoinInfos (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1PublicLoanCoinInfos",parameters);
    }

    public async Task<object> publicSpotGetSpotV1PublicLoanHourInterest (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetSpotV1PublicLoanHourInterest",parameters);
    }

    public async Task<object> publicSpotGetV2SpotPublicCoins (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotPublicCoins",parameters);
    }

    public async Task<object> publicSpotGetV2SpotPublicSymbols (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotPublicSymbols",parameters);
    }

    public async Task<object> publicSpotGetV2SpotMarketVipFeeRate (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotMarketVipFeeRate",parameters);
    }

    public async Task<object> publicSpotGetV2SpotMarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotMarketTickers",parameters);
    }

    public async Task<object> publicSpotGetV2SpotMarketMergeDepth (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotMarketMergeDepth",parameters);
    }

    public async Task<object> publicSpotGetV2SpotMarketOrderbook (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotMarketOrderbook",parameters);
    }

    public async Task<object> publicSpotGetV2SpotMarketCandles (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotMarketCandles",parameters);
    }

    public async Task<object> publicSpotGetV2SpotMarketHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotMarketHistoryCandles",parameters);
    }

    public async Task<object> publicSpotGetV2SpotMarketFills (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotMarketFills",parameters);
    }

    public async Task<object> publicSpotGetV2SpotMarketFillsHistory (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetV2SpotMarketFillsHistory",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketContracts (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketContracts",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketDepth (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketDepth",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketTicker (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketTicker",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketTickers",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketContractVipLevel (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketContractVipLevel",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketFills (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketFills",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketFillsHistory (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketFillsHistory",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketCandles (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketCandles",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketIndex (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketIndex",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketFundingTime (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketFundingTime",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketHistoryFundRate (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketHistoryFundRate",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketCurrentFundRate (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketCurrentFundRate",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketOpenInterest (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketOpenInterest",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketMarkPrice (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketMarkPrice",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketSymbolLeverage (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketSymbolLeverage",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketQueryPositionLever (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketQueryPositionLever",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketOpenLimit (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketOpenLimit",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketHistoryCandles",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketHistoryIndexCandles (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketHistoryIndexCandles",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketHistoryMarkCandles (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketHistoryMarkCandles",parameters);
    }

    public async Task<object> publicMixGetMixV1MarketMergeDepth (object parameters = null)
    {
        return await this.callAsync ("publicMixGetMixV1MarketMergeDepth",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketVipFeeRate (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketVipFeeRate",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketMergeDepth (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketMergeDepth",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketTicker (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketTicker",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketTickers",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketFills (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketFills",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketFillsHistory (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketFillsHistory",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketCandles (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketCandles",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketHistoryCandles",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketHistoryIndexCandles (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketHistoryIndexCandles",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketHistoryMarkCandles (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketHistoryMarkCandles",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketOpenInterest (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketOpenInterest",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketFundingTime (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketFundingTime",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketSymbolPrice (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketSymbolPrice",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketHistoryFundRate (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketHistoryFundRate",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketCurrentFundRate (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketCurrentFundRate",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketContracts (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketContracts",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketQueryPositionLever (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketQueryPositionLever",parameters);
    }

    public async Task<object> publicMixGetV2MixMarketAccountLongShort (object parameters = null)
    {
        return await this.callAsync ("publicMixGetV2MixMarketAccountLongShort",parameters);
    }

    public async Task<object> publicMarginGetMarginV1CrossPublicInterestRateAndLimit (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetMarginV1CrossPublicInterestRateAndLimit",parameters);
    }

    public async Task<object> publicMarginGetMarginV1IsolatedPublicInterestRateAndLimit (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetMarginV1IsolatedPublicInterestRateAndLimit",parameters);
    }

    public async Task<object> publicMarginGetMarginV1CrossPublicTierData (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetMarginV1CrossPublicTierData",parameters);
    }

    public async Task<object> publicMarginGetMarginV1IsolatedPublicTierData (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetMarginV1IsolatedPublicTierData",parameters);
    }

    public async Task<object> publicMarginGetMarginV1PublicCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetMarginV1PublicCurrencies",parameters);
    }

    public async Task<object> publicMarginGetV2MarginCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetV2MarginCurrencies",parameters);
    }

    public async Task<object> publicMarginGetV2MarginMarketLongShortRatio (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetV2MarginMarketLongShortRatio",parameters);
    }

    public async Task<object> publicEarnGetV2EarnLoanPublicCoinInfos (object parameters = null)
    {
        return await this.callAsync ("publicEarnGetV2EarnLoanPublicCoinInfos",parameters);
    }

    public async Task<object> publicEarnGetV2EarnLoanPublicHourInterest (object parameters = null)
    {
        return await this.callAsync ("publicEarnGetV2EarnLoanPublicHourInterest",parameters);
    }

    public async Task<object> publicUtaGetV3MarketInstruments (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketInstruments",parameters);
    }

    public async Task<object> publicUtaGetV3MarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketTickers",parameters);
    }

    public async Task<object> publicUtaGetV3MarketOrderbook (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketOrderbook",parameters);
    }

    public async Task<object> publicUtaGetV3MarketFills (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketFills",parameters);
    }

    public async Task<object> publicUtaGetV3MarketOpenInterest (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketOpenInterest",parameters);
    }

    public async Task<object> publicUtaGetV3MarketCandles (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketCandles",parameters);
    }

    public async Task<object> publicUtaGetV3MarketHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketHistoryCandles",parameters);
    }

    public async Task<object> publicUtaGetV3MarketCurrentFundRate (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketCurrentFundRate",parameters);
    }

    public async Task<object> publicUtaGetV3MarketHistoryFundRate (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketHistoryFundRate",parameters);
    }

    public async Task<object> publicUtaGetV3MarketRiskReserve (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketRiskReserve",parameters);
    }

    public async Task<object> publicUtaGetV3MarketDiscountRate (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketDiscountRate",parameters);
    }

    public async Task<object> publicUtaGetV3MarketMarginLoans (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketMarginLoans",parameters);
    }

    public async Task<object> publicUtaGetV3MarketPositionTier (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketPositionTier",parameters);
    }

    public async Task<object> publicUtaGetV3MarketOiLimit (object parameters = null)
    {
        return await this.callAsync ("publicUtaGetV3MarketOiLimit",parameters);
    }

    public async Task<object> privateSpotGetSpotV1WalletDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1WalletDepositAddress",parameters);
    }

    public async Task<object> privateSpotGetSpotV1WalletWithdrawalList (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1WalletWithdrawalList",parameters);
    }

    public async Task<object> privateSpotGetSpotV1WalletDepositList (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1WalletDepositList",parameters);
    }

    public async Task<object> privateSpotGetSpotV1AccountGetInfo (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1AccountGetInfo",parameters);
    }

    public async Task<object> privateSpotGetSpotV1AccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1AccountAssets",parameters);
    }

    public async Task<object> privateSpotGetSpotV1AccountAssetsLite (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1AccountAssetsLite",parameters);
    }

    public async Task<object> privateSpotGetSpotV1AccountTransferRecords (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1AccountTransferRecords",parameters);
    }

    public async Task<object> privateSpotGetSpotV1ConvertCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1ConvertCurrencies",parameters);
    }

    public async Task<object> privateSpotGetSpotV1ConvertConvertRecord (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1ConvertConvertRecord",parameters);
    }

    public async Task<object> privateSpotGetSpotV1LoanOngoingOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1LoanOngoingOrders",parameters);
    }

    public async Task<object> privateSpotGetSpotV1LoanRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1LoanRepayHistory",parameters);
    }

    public async Task<object> privateSpotGetSpotV1LoanReviseHistory (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1LoanReviseHistory",parameters);
    }

    public async Task<object> privateSpotGetSpotV1LoanBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1LoanBorrowHistory",parameters);
    }

    public async Task<object> privateSpotGetSpotV1LoanDebts (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetSpotV1LoanDebts",parameters);
    }

    public async Task<object> privateSpotGetV2SpotTradeOrderInfo (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotTradeOrderInfo",parameters);
    }

    public async Task<object> privateSpotGetV2SpotTradeUnfilledOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotTradeUnfilledOrders",parameters);
    }

    public async Task<object> privateSpotGetV2SpotTradeHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotTradeHistoryOrders",parameters);
    }

    public async Task<object> privateSpotGetV2SpotTradeFills (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotTradeFills",parameters);
    }

    public async Task<object> privateSpotGetV2SpotTradeCurrentPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotTradeCurrentPlanOrder",parameters);
    }

    public async Task<object> privateSpotGetV2SpotTradeHistoryPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotTradeHistoryPlanOrder",parameters);
    }

    public async Task<object> privateSpotGetV2SpotAccountInfo (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotAccountInfo",parameters);
    }

    public async Task<object> privateSpotGetV2SpotAccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotAccountAssets",parameters);
    }

    public async Task<object> privateSpotGetV2SpotAccountSubaccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotAccountSubaccountAssets",parameters);
    }

    public async Task<object> privateSpotGetV2SpotAccountBills (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotAccountBills",parameters);
    }

    public async Task<object> privateSpotGetV2SpotAccountTransferRecords (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotAccountTransferRecords",parameters);
    }

    public async Task<object> privateSpotGetV2AccountFundingAssets (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2AccountFundingAssets",parameters);
    }

    public async Task<object> privateSpotGetV2AccountBotAssets (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2AccountBotAssets",parameters);
    }

    public async Task<object> privateSpotGetV2AccountAllAccountBalance (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2AccountAllAccountBalance",parameters);
    }

    public async Task<object> privateSpotGetV2SpotWalletDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotWalletDepositAddress",parameters);
    }

    public async Task<object> privateSpotGetV2SpotWalletDepositRecords (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotWalletDepositRecords",parameters);
    }

    public async Task<object> privateSpotGetV2SpotWalletWithdrawalRecords (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetV2SpotWalletWithdrawalRecords",parameters);
    }

    public async Task<object> privateSpotPostSpotV1WalletTransfer (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1WalletTransfer",parameters);
    }

    public async Task<object> privateSpotPostSpotV1WalletTransferV2 (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1WalletTransferV2",parameters);
    }

    public async Task<object> privateSpotPostSpotV1WalletSubTransfer (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1WalletSubTransfer",parameters);
    }

    public async Task<object> privateSpotPostSpotV1WalletWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1WalletWithdrawal",parameters);
    }

    public async Task<object> privateSpotPostSpotV1WalletWithdrawalV2 (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1WalletWithdrawalV2",parameters);
    }

    public async Task<object> privateSpotPostSpotV1WalletWithdrawalInner (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1WalletWithdrawalInner",parameters);
    }

    public async Task<object> privateSpotPostSpotV1WalletWithdrawalInnerV2 (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1WalletWithdrawalInnerV2",parameters);
    }

    public async Task<object> privateSpotPostSpotV1AccountSubAccountSpotAssets (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1AccountSubAccountSpotAssets",parameters);
    }

    public async Task<object> privateSpotPostSpotV1AccountBills (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1AccountBills",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeOrders",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeBatchOrders",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeCancelOrder",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeCancelOrderV2 (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeCancelOrderV2",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeCancelSymbolOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeCancelSymbolOrder",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeCancelBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeCancelBatchOrders",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeCancelBatchOrdersV2 (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeCancelBatchOrdersV2",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeOrderInfo (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeOrderInfo",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeOpenOrders",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeHistory (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeHistory",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TradeFills (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TradeFills",parameters);
    }

    public async Task<object> privateSpotPostSpotV1PlanPlacePlan (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1PlanPlacePlan",parameters);
    }

    public async Task<object> privateSpotPostSpotV1PlanModifyPlan (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1PlanModifyPlan",parameters);
    }

    public async Task<object> privateSpotPostSpotV1PlanCancelPlan (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1PlanCancelPlan",parameters);
    }

    public async Task<object> privateSpotPostSpotV1PlanCurrentPlan (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1PlanCurrentPlan",parameters);
    }

    public async Task<object> privateSpotPostSpotV1PlanHistoryPlan (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1PlanHistoryPlan",parameters);
    }

    public async Task<object> privateSpotPostSpotV1PlanBatchCancelPlan (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1PlanBatchCancelPlan",parameters);
    }

    public async Task<object> privateSpotPostSpotV1ConvertQuotedPrice (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1ConvertQuotedPrice",parameters);
    }

    public async Task<object> privateSpotPostSpotV1ConvertTrade (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1ConvertTrade",parameters);
    }

    public async Task<object> privateSpotPostSpotV1LoanBorrow (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1LoanBorrow",parameters);
    }

    public async Task<object> privateSpotPostSpotV1LoanRepay (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1LoanRepay",parameters);
    }

    public async Task<object> privateSpotPostSpotV1LoanRevisePledge (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1LoanRevisePledge",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceOrderOrderCurrentList (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceOrderOrderCurrentList",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceOrderOrderHistoryList (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceOrderOrderHistoryList",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceOrderCloseTrackingOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceOrderCloseTrackingOrder",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceOrderUpdateTpsl (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceOrderUpdateTpsl",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceOrderFollowerEndOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceOrderFollowerEndOrder",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceOrderSpotInfoList (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceOrderSpotInfoList",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceConfigGetTraderSettings (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceConfigGetTraderSettings",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceConfigGetFollowerSettings (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceConfigGetFollowerSettings",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceUserMyTraders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceUserMyTraders",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceConfigSetFollowerConfig (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceConfigSetFollowerConfig",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceUserMyFollowers (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceUserMyFollowers",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceConfigSetProductCode (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceConfigSetProductCode",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceUserRemoveTrader (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceUserRemoveTrader",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceGetRemovableFollower (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceGetRemovableFollower",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceUserRemoveFollower (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceUserRemoveFollower",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceProfitTotalProfitInfo (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceProfitTotalProfitInfo",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceProfitTotalProfitList (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceProfitTotalProfitList",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceProfitProfitHisList (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceProfitProfitHisList",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceProfitProfitHisDetailList (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceProfitProfitHisDetailList",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceProfitWaitProfitDetailList (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceProfitWaitProfitDetailList",parameters);
    }

    public async Task<object> privateSpotPostSpotV1TraceUserGetTraderInfo (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostSpotV1TraceUserGetTraderInfo",parameters);
    }

    public async Task<object> privateSpotPostV2SpotTradePlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotTradePlaceOrder",parameters);
    }

    public async Task<object> privateSpotPostV2SpotTradeCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotTradeCancelOrder",parameters);
    }

    public async Task<object> privateSpotPostV2SpotTradeBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotTradeBatchOrders",parameters);
    }

    public async Task<object> privateSpotPostV2SpotTradeBatchCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotTradeBatchCancelOrder",parameters);
    }

    public async Task<object> privateSpotPostV2SpotTradeCancelSymbolOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotTradeCancelSymbolOrder",parameters);
    }

    public async Task<object> privateSpotPostV2SpotTradePlacePlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotTradePlacePlanOrder",parameters);
    }

    public async Task<object> privateSpotPostV2SpotTradeModifyPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotTradeModifyPlanOrder",parameters);
    }

    public async Task<object> privateSpotPostV2SpotTradeCancelPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotTradeCancelPlanOrder",parameters);
    }

    public async Task<object> privateSpotPostV2SpotTradeBatchCancelPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotTradeBatchCancelPlanOrder",parameters);
    }

    public async Task<object> privateSpotPostV2SpotWalletTransfer (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotWalletTransfer",parameters);
    }

    public async Task<object> privateSpotPostV2SpotWalletSubaccountTransfer (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotWalletSubaccountTransfer",parameters);
    }

    public async Task<object> privateSpotPostV2SpotWalletWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotWalletWithdrawal",parameters);
    }

    public async Task<object> privateSpotPostV2SpotWalletCancelWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotWalletCancelWithdrawal",parameters);
    }

    public async Task<object> privateSpotPostV2SpotWalletModifyDepositAccount (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostV2SpotWalletModifyDepositAccount",parameters);
    }

    public async Task<object> privateMixGetMixV1AccountAccount (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1AccountAccount",parameters);
    }

    public async Task<object> privateMixGetMixV1AccountAccounts (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1AccountAccounts",parameters);
    }

    public async Task<object> privateMixGetMixV1PositionSinglePosition (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1PositionSinglePosition",parameters);
    }

    public async Task<object> privateMixGetMixV1PositionSinglePositionV2 (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1PositionSinglePositionV2",parameters);
    }

    public async Task<object> privateMixGetMixV1PositionAllPosition (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1PositionAllPosition",parameters);
    }

    public async Task<object> privateMixGetMixV1PositionAllPositionV2 (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1PositionAllPositionV2",parameters);
    }

    public async Task<object> privateMixGetMixV1PositionHistoryPosition (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1PositionHistoryPosition",parameters);
    }

    public async Task<object> privateMixGetMixV1AccountAccountBill (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1AccountAccountBill",parameters);
    }

    public async Task<object> privateMixGetMixV1AccountAccountBusinessBill (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1AccountAccountBusinessBill",parameters);
    }

    public async Task<object> privateMixGetMixV1OrderCurrent (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1OrderCurrent",parameters);
    }

    public async Task<object> privateMixGetMixV1OrderMarginCoinCurrent (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1OrderMarginCoinCurrent",parameters);
    }

    public async Task<object> privateMixGetMixV1OrderHistory (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1OrderHistory",parameters);
    }

    public async Task<object> privateMixGetMixV1OrderHistoryProductType (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1OrderHistoryProductType",parameters);
    }

    public async Task<object> privateMixGetMixV1OrderDetail (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1OrderDetail",parameters);
    }

    public async Task<object> privateMixGetMixV1OrderFills (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1OrderFills",parameters);
    }

    public async Task<object> privateMixGetMixV1OrderAllFills (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1OrderAllFills",parameters);
    }

    public async Task<object> privateMixGetMixV1PlanCurrentPlan (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1PlanCurrentPlan",parameters);
    }

    public async Task<object> privateMixGetMixV1PlanHistoryPlan (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1PlanHistoryPlan",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceCurrentTrack (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceCurrentTrack",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceFollowerOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceFollowerOrder",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceFollowerHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceFollowerHistoryOrders",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceHistoryTrack (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceHistoryTrack",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceSummary (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceSummary",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceProfitSettleTokenIdGroup (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceProfitSettleTokenIdGroup",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceProfitDateGroupList (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceProfitDateGroupList",parameters);
    }

    public async Task<object> privateMixGetMixV1TradeProfitDateList (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TradeProfitDateList",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceWaitProfitDateList (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceWaitProfitDateList",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceTraderSymbols (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceTraderSymbols",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceTraderList (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceTraderList",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceTraderDetail (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceTraderDetail",parameters);
    }

    public async Task<object> privateMixGetMixV1TraceQueryTraceConfig (object parameters = null)
    {
        return await this.callAsync ("privateMixGetMixV1TraceQueryTraceConfig",parameters);
    }

    public async Task<object> privateMixGetV2MixAccountAccount (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixAccountAccount",parameters);
    }

    public async Task<object> privateMixGetV2MixAccountAccounts (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixAccountAccounts",parameters);
    }

    public async Task<object> privateMixGetV2MixAccountSubAccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixAccountSubAccountAssets",parameters);
    }

    public async Task<object> privateMixGetV2MixAccountOpenCount (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixAccountOpenCount",parameters);
    }

    public async Task<object> privateMixGetV2MixAccountBill (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixAccountBill",parameters);
    }

    public async Task<object> privateMixGetV2MixMarketQueryPositionLever (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixMarketQueryPositionLever",parameters);
    }

    public async Task<object> privateMixGetV2MixPositionSinglePosition (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixPositionSinglePosition",parameters);
    }

    public async Task<object> privateMixGetV2MixPositionAllPosition (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixPositionAllPosition",parameters);
    }

    public async Task<object> privateMixGetV2MixPositionHistoryPosition (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixPositionHistoryPosition",parameters);
    }

    public async Task<object> privateMixGetV2MixOrderDetail (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixOrderDetail",parameters);
    }

    public async Task<object> privateMixGetV2MixOrderFills (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixOrderFills",parameters);
    }

    public async Task<object> privateMixGetV2MixOrderFillHistory (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixOrderFillHistory",parameters);
    }

    public async Task<object> privateMixGetV2MixOrderOrdersPending (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixOrderOrdersPending",parameters);
    }

    public async Task<object> privateMixGetV2MixOrderOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixOrderOrdersHistory",parameters);
    }

    public async Task<object> privateMixGetV2MixOrderOrdersPlanPending (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixOrderOrdersPlanPending",parameters);
    }

    public async Task<object> privateMixGetV2MixOrderOrdersPlanHistory (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixOrderOrdersPlanHistory",parameters);
    }

    public async Task<object> privateMixGetV2MixMarketPositionLongShort (object parameters = null)
    {
        return await this.callAsync ("privateMixGetV2MixMarketPositionLongShort",parameters);
    }

    public async Task<object> privateMixPostMixV1AccountSubAccountContractAssets (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1AccountSubAccountContractAssets",parameters);
    }

    public async Task<object> privateMixPostMixV1AccountOpenCount (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1AccountOpenCount",parameters);
    }

    public async Task<object> privateMixPostMixV1AccountSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1AccountSetLeverage",parameters);
    }

    public async Task<object> privateMixPostMixV1AccountSetMargin (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1AccountSetMargin",parameters);
    }

    public async Task<object> privateMixPostMixV1AccountSetMarginMode (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1AccountSetMarginMode",parameters);
    }

    public async Task<object> privateMixPostMixV1AccountSetPositionMode (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1AccountSetPositionMode",parameters);
    }

    public async Task<object> privateMixPostMixV1OrderPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1OrderPlaceOrder",parameters);
    }

    public async Task<object> privateMixPostMixV1OrderBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1OrderBatchOrders",parameters);
    }

    public async Task<object> privateMixPostMixV1OrderCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1OrderCancelOrder",parameters);
    }

    public async Task<object> privateMixPostMixV1OrderCancelBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1OrderCancelBatchOrders",parameters);
    }

    public async Task<object> privateMixPostMixV1OrderModifyOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1OrderModifyOrder",parameters);
    }

    public async Task<object> privateMixPostMixV1OrderCancelSymbolOrders (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1OrderCancelSymbolOrders",parameters);
    }

    public async Task<object> privateMixPostMixV1OrderCancelAllOrders (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1OrderCancelAllOrders",parameters);
    }

    public async Task<object> privateMixPostMixV1OrderCloseAllPositions (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1OrderCloseAllPositions",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanPlacePlan (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanPlacePlan",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanModifyPlan (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanModifyPlan",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanModifyPlanPreset (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanModifyPlanPreset",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanPlaceTPSL (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanPlaceTPSL",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanPlaceTrailStop (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanPlaceTrailStop",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanPlacePositionsTPSL (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanPlacePositionsTPSL",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanModifyTPSLPlan (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanModifyTPSLPlan",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanCancelPlan (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanCancelPlan",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanCancelSymbolPlan (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanCancelSymbolPlan",parameters);
    }

    public async Task<object> privateMixPostMixV1PlanCancelAllPlan (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1PlanCancelAllPlan",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceCloseTrackOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceCloseTrackOrder",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceModifyTPSL (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceModifyTPSL",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceCloseTrackOrderBySymbol (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceCloseTrackOrderBySymbol",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceSetUpCopySymbols (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceSetUpCopySymbols",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceFollowerSetBatchTraceConfig (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceFollowerSetBatchTraceConfig",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceFollowerCloseByTrackingNo (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceFollowerCloseByTrackingNo",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceFollowerCloseByAll (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceFollowerCloseByAll",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceFollowerSetTpsl (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceFollowerSetTpsl",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceCancelCopyTrader (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceCancelCopyTrader",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceTraderUpdateConfig (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceTraderUpdateConfig",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceMyTraderList (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceMyTraderList",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceMyFollowerList (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceMyFollowerList",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceRemoveFollower (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceRemoveFollower",parameters);
    }

    public async Task<object> privateMixPostMixV1TracePublicGetFollowerConfig (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TracePublicGetFollowerConfig",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceReportOrderHistoryList (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceReportOrderHistoryList",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceReportOrderCurrentList (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceReportOrderCurrentList",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceQueryTraderTpslRatioConfig (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceQueryTraderTpslRatioConfig",parameters);
    }

    public async Task<object> privateMixPostMixV1TraceTraderUpdateTpslRatioConfig (object parameters = null)
    {
        return await this.callAsync ("privateMixPostMixV1TraceTraderUpdateTpslRatioConfig",parameters);
    }

    public async Task<object> privateMixPostV2MixAccountSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixAccountSetLeverage",parameters);
    }

    public async Task<object> privateMixPostV2MixAccountSetMargin (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixAccountSetMargin",parameters);
    }

    public async Task<object> privateMixPostV2MixAccountSetMarginMode (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixAccountSetMarginMode",parameters);
    }

    public async Task<object> privateMixPostV2MixAccountSetPositionMode (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixAccountSetPositionMode",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderPlaceOrder",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderClickBackhand (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderClickBackhand",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderBatchPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderBatchPlaceOrder",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderModifyOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderModifyOrder",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderCancelOrder",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderBatchCancelOrders (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderBatchCancelOrders",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderClosePositions (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderClosePositions",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderPlaceTpslOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderPlaceTpslOrder",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderPlacePlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderPlacePlanOrder",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderModifyTpslOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderModifyTpslOrder",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderModifyPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderModifyPlanOrder",parameters);
    }

    public async Task<object> privateMixPostV2MixOrderCancelPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateMixPostV2MixOrderCancelPlanOrder",parameters);
    }

    public async Task<object> privateUserGetUserV1FeeQuery (object parameters = null)
    {
        return await this.callAsync ("privateUserGetUserV1FeeQuery",parameters);
    }

    public async Task<object> privateUserGetUserV1SubVirtualList (object parameters = null)
    {
        return await this.callAsync ("privateUserGetUserV1SubVirtualList",parameters);
    }

    public async Task<object> privateUserGetUserV1SubVirtualApiList (object parameters = null)
    {
        return await this.callAsync ("privateUserGetUserV1SubVirtualApiList",parameters);
    }

    public async Task<object> privateUserGetUserV1TaxSpotRecord (object parameters = null)
    {
        return await this.callAsync ("privateUserGetUserV1TaxSpotRecord",parameters);
    }

    public async Task<object> privateUserGetUserV1TaxFutureRecord (object parameters = null)
    {
        return await this.callAsync ("privateUserGetUserV1TaxFutureRecord",parameters);
    }

    public async Task<object> privateUserGetUserV1TaxMarginRecord (object parameters = null)
    {
        return await this.callAsync ("privateUserGetUserV1TaxMarginRecord",parameters);
    }

    public async Task<object> privateUserGetUserV1TaxP2pRecord (object parameters = null)
    {
        return await this.callAsync ("privateUserGetUserV1TaxP2pRecord",parameters);
    }

    public async Task<object> privateUserGetV2UserVirtualSubaccountList (object parameters = null)
    {
        return await this.callAsync ("privateUserGetV2UserVirtualSubaccountList",parameters);
    }

    public async Task<object> privateUserGetV2UserVirtualSubaccountApikeyList (object parameters = null)
    {
        return await this.callAsync ("privateUserGetV2UserVirtualSubaccountApikeyList",parameters);
    }

    public async Task<object> privateUserPostUserV1SubVirtualCreate (object parameters = null)
    {
        return await this.callAsync ("privateUserPostUserV1SubVirtualCreate",parameters);
    }

    public async Task<object> privateUserPostUserV1SubVirtualModify (object parameters = null)
    {
        return await this.callAsync ("privateUserPostUserV1SubVirtualModify",parameters);
    }

    public async Task<object> privateUserPostUserV1SubVirtualApiBatchCreate (object parameters = null)
    {
        return await this.callAsync ("privateUserPostUserV1SubVirtualApiBatchCreate",parameters);
    }

    public async Task<object> privateUserPostUserV1SubVirtualApiCreate (object parameters = null)
    {
        return await this.callAsync ("privateUserPostUserV1SubVirtualApiCreate",parameters);
    }

    public async Task<object> privateUserPostUserV1SubVirtualApiModify (object parameters = null)
    {
        return await this.callAsync ("privateUserPostUserV1SubVirtualApiModify",parameters);
    }

    public async Task<object> privateUserPostV2UserCreateVirtualSubaccount (object parameters = null)
    {
        return await this.callAsync ("privateUserPostV2UserCreateVirtualSubaccount",parameters);
    }

    public async Task<object> privateUserPostV2UserModifyVirtualSubaccount (object parameters = null)
    {
        return await this.callAsync ("privateUserPostV2UserModifyVirtualSubaccount",parameters);
    }

    public async Task<object> privateUserPostV2UserBatchCreateSubaccountAndApikey (object parameters = null)
    {
        return await this.callAsync ("privateUserPostV2UserBatchCreateSubaccountAndApikey",parameters);
    }

    public async Task<object> privateUserPostV2UserCreateVirtualSubaccountApikey (object parameters = null)
    {
        return await this.callAsync ("privateUserPostV2UserCreateVirtualSubaccountApikey",parameters);
    }

    public async Task<object> privateUserPostV2UserModifyVirtualSubaccountApikey (object parameters = null)
    {
        return await this.callAsync ("privateUserPostV2UserModifyVirtualSubaccountApikey",parameters);
    }

    public async Task<object> privateP2pGetP2pV1MerchantMerchantList (object parameters = null)
    {
        return await this.callAsync ("privateP2pGetP2pV1MerchantMerchantList",parameters);
    }

    public async Task<object> privateP2pGetP2pV1MerchantMerchantInfo (object parameters = null)
    {
        return await this.callAsync ("privateP2pGetP2pV1MerchantMerchantInfo",parameters);
    }

    public async Task<object> privateP2pGetP2pV1MerchantAdvList (object parameters = null)
    {
        return await this.callAsync ("privateP2pGetP2pV1MerchantAdvList",parameters);
    }

    public async Task<object> privateP2pGetP2pV1MerchantOrderList (object parameters = null)
    {
        return await this.callAsync ("privateP2pGetP2pV1MerchantOrderList",parameters);
    }

    public async Task<object> privateP2pGetV2P2pMerchantList (object parameters = null)
    {
        return await this.callAsync ("privateP2pGetV2P2pMerchantList",parameters);
    }

    public async Task<object> privateP2pGetV2P2pMerchantInfo (object parameters = null)
    {
        return await this.callAsync ("privateP2pGetV2P2pMerchantInfo",parameters);
    }

    public async Task<object> privateP2pGetV2P2pOrderList (object parameters = null)
    {
        return await this.callAsync ("privateP2pGetV2P2pOrderList",parameters);
    }

    public async Task<object> privateP2pGetV2P2pAdvList (object parameters = null)
    {
        return await this.callAsync ("privateP2pGetV2P2pAdvList",parameters);
    }

    public async Task<object> privateBrokerGetBrokerV1AccountInfo (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetBrokerV1AccountInfo",parameters);
    }

    public async Task<object> privateBrokerGetBrokerV1AccountSubList (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetBrokerV1AccountSubList",parameters);
    }

    public async Task<object> privateBrokerGetBrokerV1AccountSubEmail (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetBrokerV1AccountSubEmail",parameters);
    }

    public async Task<object> privateBrokerGetBrokerV1AccountSubSpotAssets (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetBrokerV1AccountSubSpotAssets",parameters);
    }

    public async Task<object> privateBrokerGetBrokerV1AccountSubFutureAssets (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetBrokerV1AccountSubFutureAssets",parameters);
    }

    public async Task<object> privateBrokerGetBrokerV1AccountSubaccountTransfer (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetBrokerV1AccountSubaccountTransfer",parameters);
    }

    public async Task<object> privateBrokerGetBrokerV1AccountSubaccountDeposit (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetBrokerV1AccountSubaccountDeposit",parameters);
    }

    public async Task<object> privateBrokerGetBrokerV1AccountSubaccountWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetBrokerV1AccountSubaccountWithdrawal",parameters);
    }

    public async Task<object> privateBrokerGetBrokerV1AccountSubApiList (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetBrokerV1AccountSubApiList",parameters);
    }

    public async Task<object> privateBrokerGetV2BrokerAccountInfo (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetV2BrokerAccountInfo",parameters);
    }

    public async Task<object> privateBrokerGetV2BrokerAccountSubaccountList (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetV2BrokerAccountSubaccountList",parameters);
    }

    public async Task<object> privateBrokerGetV2BrokerAccountSubaccountEmail (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetV2BrokerAccountSubaccountEmail",parameters);
    }

    public async Task<object> privateBrokerGetV2BrokerAccountSubaccountSpotAssets (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetV2BrokerAccountSubaccountSpotAssets",parameters);
    }

    public async Task<object> privateBrokerGetV2BrokerAccountSubaccountFutureAssets (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetV2BrokerAccountSubaccountFutureAssets",parameters);
    }

    public async Task<object> privateBrokerGetV2BrokerManageSubaccountApikeyList (object parameters = null)
    {
        return await this.callAsync ("privateBrokerGetV2BrokerManageSubaccountApikeyList",parameters);
    }

    public async Task<object> privateBrokerPostBrokerV1AccountSubCreate (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostBrokerV1AccountSubCreate",parameters);
    }

    public async Task<object> privateBrokerPostBrokerV1AccountSubModify (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostBrokerV1AccountSubModify",parameters);
    }

    public async Task<object> privateBrokerPostBrokerV1AccountSubModifyEmail (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostBrokerV1AccountSubModifyEmail",parameters);
    }

    public async Task<object> privateBrokerPostBrokerV1AccountSubAddress (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostBrokerV1AccountSubAddress",parameters);
    }

    public async Task<object> privateBrokerPostBrokerV1AccountSubWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostBrokerV1AccountSubWithdrawal",parameters);
    }

    public async Task<object> privateBrokerPostBrokerV1AccountSubAutoTransfer (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostBrokerV1AccountSubAutoTransfer",parameters);
    }

    public async Task<object> privateBrokerPostBrokerV1AccountSubApiCreate (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostBrokerV1AccountSubApiCreate",parameters);
    }

    public async Task<object> privateBrokerPostBrokerV1AccountSubApiModify (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostBrokerV1AccountSubApiModify",parameters);
    }

    public async Task<object> privateBrokerPostV2BrokerAccountModifySubaccountEmail (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostV2BrokerAccountModifySubaccountEmail",parameters);
    }

    public async Task<object> privateBrokerPostV2BrokerAccountCreateSubaccount (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostV2BrokerAccountCreateSubaccount",parameters);
    }

    public async Task<object> privateBrokerPostV2BrokerAccountModifySubaccount (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostV2BrokerAccountModifySubaccount",parameters);
    }

    public async Task<object> privateBrokerPostV2BrokerAccountSubaccountAddress (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostV2BrokerAccountSubaccountAddress",parameters);
    }

    public async Task<object> privateBrokerPostV2BrokerAccountSubaccountWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostV2BrokerAccountSubaccountWithdrawal",parameters);
    }

    public async Task<object> privateBrokerPostV2BrokerAccountSetSubaccountAutotransfer (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostV2BrokerAccountSetSubaccountAutotransfer",parameters);
    }

    public async Task<object> privateBrokerPostV2BrokerManageCreateSubaccountApikey (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostV2BrokerManageCreateSubaccountApikey",parameters);
    }

    public async Task<object> privateBrokerPostV2BrokerManageModifySubaccountApikey (object parameters = null)
    {
        return await this.callAsync ("privateBrokerPostV2BrokerManageModifySubaccountApikey",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossAccountRiskRate (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossAccountRiskRate",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossAccountMaxTransferOutAmount (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossAccountMaxTransferOutAmount",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedAccountMaxTransferOutAmount (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedAccountMaxTransferOutAmount",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedOrderOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedOrderOpenOrders",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedOrderHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedOrderHistory",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedOrderFills (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedOrderFills",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedLoanList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedLoanList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedRepayList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedRepayList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedInterestList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedInterestList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedLiquidationList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedLiquidationList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedFinList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedFinList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossOrderOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossOrderOpenOrders",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossOrderHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossOrderHistory",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossOrderFills (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossOrderFills",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossLoanList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossLoanList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossRepayList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossRepayList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossInterestList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossInterestList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossLiquidationList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossLiquidationList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossFinList (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossFinList",parameters);
    }

    public async Task<object> privateMarginGetMarginV1CrossAccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1CrossAccountAssets",parameters);
    }

    public async Task<object> privateMarginGetMarginV1IsolatedAccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetMarginV1IsolatedAccountAssets",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedBorrowHistory",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedRepayHistory",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedInterestHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedInterestHistory",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedLiquidationHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedLiquidationHistory",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedFinancialRecords (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedFinancialRecords",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedAccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedAccountAssets",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedAccountRiskRate (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedAccountRiskRate",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedAccountMaxBorrowableAmount (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedAccountMaxBorrowableAmount",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedAccountMaxTransferOutAmount (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedAccountMaxTransferOutAmount",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedInterestRateAndLimit (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedInterestRateAndLimit",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedTierData (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedTierData",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedOpenOrders",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedHistoryOrders",parameters);
    }

    public async Task<object> privateMarginGetV2MarginCrossedFills (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginCrossedFills",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedBorrowHistory",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedRepayHistory",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedInterestHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedInterestHistory",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedLiquidationHistory (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedLiquidationHistory",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedFinancialRecords (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedFinancialRecords",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedAccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedAccountAssets",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedAccountRiskRate (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedAccountRiskRate",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedAccountMaxBorrowableAmount (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedAccountMaxBorrowableAmount",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedAccountMaxTransferOutAmount (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedAccountMaxTransferOutAmount",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedInterestRateAndLimit (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedInterestRateAndLimit",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedTierData (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedTierData",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedOpenOrders",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedHistoryOrders",parameters);
    }

    public async Task<object> privateMarginGetV2MarginIsolatedFills (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetV2MarginIsolatedFills",parameters);
    }

    public async Task<object> privateMarginPostMarginV1CrossAccountBorrow (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1CrossAccountBorrow",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedAccountBorrow (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedAccountBorrow",parameters);
    }

    public async Task<object> privateMarginPostMarginV1CrossAccountRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1CrossAccountRepay",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedAccountRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedAccountRepay",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedAccountRiskRate (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedAccountRiskRate",parameters);
    }

    public async Task<object> privateMarginPostMarginV1CrossAccountMaxBorrowableAmount (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1CrossAccountMaxBorrowableAmount",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedAccountMaxBorrowableAmount (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedAccountMaxBorrowableAmount",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedAccountFlashRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedAccountFlashRepay",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedAccountQueryFlashRepayStatus (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedAccountQueryFlashRepayStatus",parameters);
    }

    public async Task<object> privateMarginPostMarginV1CrossAccountFlashRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1CrossAccountFlashRepay",parameters);
    }

    public async Task<object> privateMarginPostMarginV1CrossAccountQueryFlashRepayStatus (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1CrossAccountQueryFlashRepayStatus",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedOrderPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedOrderPlaceOrder",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedOrderBatchPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedOrderBatchPlaceOrder",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedOrderCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedOrderCancelOrder",parameters);
    }

    public async Task<object> privateMarginPostMarginV1IsolatedOrderBatchCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1IsolatedOrderBatchCancelOrder",parameters);
    }

    public async Task<object> privateMarginPostMarginV1CrossOrderPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1CrossOrderPlaceOrder",parameters);
    }

    public async Task<object> privateMarginPostMarginV1CrossOrderBatchPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1CrossOrderBatchPlaceOrder",parameters);
    }

    public async Task<object> privateMarginPostMarginV1CrossOrderCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1CrossOrderCancelOrder",parameters);
    }

    public async Task<object> privateMarginPostMarginV1CrossOrderBatchCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMarginV1CrossOrderBatchCancelOrder",parameters);
    }

    public async Task<object> privateMarginPostV2MarginCrossedAccountBorrow (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginCrossedAccountBorrow",parameters);
    }

    public async Task<object> privateMarginPostV2MarginCrossedAccountRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginCrossedAccountRepay",parameters);
    }

    public async Task<object> privateMarginPostV2MarginCrossedAccountFlashRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginCrossedAccountFlashRepay",parameters);
    }

    public async Task<object> privateMarginPostV2MarginCrossedAccountQueryFlashRepayStatus (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginCrossedAccountQueryFlashRepayStatus",parameters);
    }

    public async Task<object> privateMarginPostV2MarginCrossedPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginCrossedPlaceOrder",parameters);
    }

    public async Task<object> privateMarginPostV2MarginCrossedBatchPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginCrossedBatchPlaceOrder",parameters);
    }

    public async Task<object> privateMarginPostV2MarginCrossedCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginCrossedCancelOrder",parameters);
    }

    public async Task<object> privateMarginPostV2MarginCrossedBatchCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginCrossedBatchCancelOrder",parameters);
    }

    public async Task<object> privateMarginPostV2MarginIsolatedAccountBorrow (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginIsolatedAccountBorrow",parameters);
    }

    public async Task<object> privateMarginPostV2MarginIsolatedAccountRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginIsolatedAccountRepay",parameters);
    }

    public async Task<object> privateMarginPostV2MarginIsolatedAccountFlashRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginIsolatedAccountFlashRepay",parameters);
    }

    public async Task<object> privateMarginPostV2MarginIsolatedAccountQueryFlashRepayStatus (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginIsolatedAccountQueryFlashRepayStatus",parameters);
    }

    public async Task<object> privateMarginPostV2MarginIsolatedPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginIsolatedPlaceOrder",parameters);
    }

    public async Task<object> privateMarginPostV2MarginIsolatedBatchPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginIsolatedBatchPlaceOrder",parameters);
    }

    public async Task<object> privateMarginPostV2MarginIsolatedCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginIsolatedCancelOrder",parameters);
    }

    public async Task<object> privateMarginPostV2MarginIsolatedBatchCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostV2MarginIsolatedBatchCancelOrder",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixTraderOrderCurrentTrack (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixTraderOrderCurrentTrack",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixTraderOrderHistoryTrack (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixTraderOrderHistoryTrack",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixTraderOrderTotalDetail (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixTraderOrderTotalDetail",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixTraderProfitHistorySummarys (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixTraderProfitHistorySummarys",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixTraderProfitHistoryDetails (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixTraderProfitHistoryDetails",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixTraderProfitDetails (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixTraderProfitDetails",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixTraderProfitsGroupCoinDate (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixTraderProfitsGroupCoinDate",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixTraderConfigQuerySymbols (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixTraderConfigQuerySymbols",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixTraderConfigQueryFollowers (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixTraderConfigQueryFollowers",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixFollowerQueryCurrentOrders (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixFollowerQueryCurrentOrders",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixFollowerQueryHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixFollowerQueryHistoryOrders",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixFollowerQuerySettings (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixFollowerQuerySettings",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixFollowerQueryTraders (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixFollowerQueryTraders",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixFollowerQueryQuantityLimit (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixFollowerQueryQuantityLimit",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixBrokerQueryTraders (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixBrokerQueryTraders",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixBrokerQueryHistoryTraces (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixBrokerQueryHistoryTraces",parameters);
    }

    public async Task<object> privateCopyGetV2CopyMixBrokerQueryCurrentTraces (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopyMixBrokerQueryCurrentTraces",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotTraderProfitSummarys (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotTraderProfitSummarys",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotTraderProfitHistoryDetails (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotTraderProfitHistoryDetails",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotTraderProfitDetails (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotTraderProfitDetails",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotTraderOrderTotalDetail (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotTraderOrderTotalDetail",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotTraderOrderHistoryTrack (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotTraderOrderHistoryTrack",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotTraderOrderCurrentTrack (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotTraderOrderCurrentTrack",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotTraderConfigQuerySettings (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotTraderConfigQuerySettings",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotTraderConfigQueryFollowers (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotTraderConfigQueryFollowers",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotFollowerQueryTraders (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotFollowerQueryTraders",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotFollowerQueryTraderSymbols (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotFollowerQueryTraderSymbols",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotFollowerQuerySettings (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotFollowerQuerySettings",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotFollowerQueryHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotFollowerQueryHistoryOrders",parameters);
    }

    public async Task<object> privateCopyGetV2CopySpotFollowerQueryCurrentOrders (object parameters = null)
    {
        return await this.callAsync ("privateCopyGetV2CopySpotFollowerQueryCurrentOrders",parameters);
    }

    public async Task<object> privateCopyPostV2CopyMixTraderOrderModifyTpsl (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopyMixTraderOrderModifyTpsl",parameters);
    }

    public async Task<object> privateCopyPostV2CopyMixTraderOrderClosePositions (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopyMixTraderOrderClosePositions",parameters);
    }

    public async Task<object> privateCopyPostV2CopyMixTraderConfigSettingSymbols (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopyMixTraderConfigSettingSymbols",parameters);
    }

    public async Task<object> privateCopyPostV2CopyMixTraderConfigSettingBase (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopyMixTraderConfigSettingBase",parameters);
    }

    public async Task<object> privateCopyPostV2CopyMixTraderConfigRemoveFollower (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopyMixTraderConfigRemoveFollower",parameters);
    }

    public async Task<object> privateCopyPostV2CopyMixFollowerSettingTpsl (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopyMixFollowerSettingTpsl",parameters);
    }

    public async Task<object> privateCopyPostV2CopyMixFollowerSettings (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopyMixFollowerSettings",parameters);
    }

    public async Task<object> privateCopyPostV2CopyMixFollowerClosePositions (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopyMixFollowerClosePositions",parameters);
    }

    public async Task<object> privateCopyPostV2CopyMixFollowerCancelTrader (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopyMixFollowerCancelTrader",parameters);
    }

    public async Task<object> privateCopyPostV2CopySpotTraderOrderModifyTpsl (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopySpotTraderOrderModifyTpsl",parameters);
    }

    public async Task<object> privateCopyPostV2CopySpotTraderOrderCloseTracking (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopySpotTraderOrderCloseTracking",parameters);
    }

    public async Task<object> privateCopyPostV2CopySpotTraderConfigSettingSymbols (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopySpotTraderConfigSettingSymbols",parameters);
    }

    public async Task<object> privateCopyPostV2CopySpotTraderConfigRemoveFollower (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopySpotTraderConfigRemoveFollower",parameters);
    }

    public async Task<object> privateCopyPostV2CopySpotFollowerStopOrder (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopySpotFollowerStopOrder",parameters);
    }

    public async Task<object> privateCopyPostV2CopySpotFollowerSettings (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopySpotFollowerSettings",parameters);
    }

    public async Task<object> privateCopyPostV2CopySpotFollowerSettingTpsl (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopySpotFollowerSettingTpsl",parameters);
    }

    public async Task<object> privateCopyPostV2CopySpotFollowerOrderCloseTracking (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopySpotFollowerOrderCloseTracking",parameters);
    }

    public async Task<object> privateCopyPostV2CopySpotFollowerCancelTrader (object parameters = null)
    {
        return await this.callAsync ("privateCopyPostV2CopySpotFollowerCancelTrader",parameters);
    }

    public async Task<object> privateTaxGetV2TaxSpotRecord (object parameters = null)
    {
        return await this.callAsync ("privateTaxGetV2TaxSpotRecord",parameters);
    }

    public async Task<object> privateTaxGetV2TaxFutureRecord (object parameters = null)
    {
        return await this.callAsync ("privateTaxGetV2TaxFutureRecord",parameters);
    }

    public async Task<object> privateTaxGetV2TaxMarginRecord (object parameters = null)
    {
        return await this.callAsync ("privateTaxGetV2TaxMarginRecord",parameters);
    }

    public async Task<object> privateTaxGetV2TaxP2pRecord (object parameters = null)
    {
        return await this.callAsync ("privateTaxGetV2TaxP2pRecord",parameters);
    }

    public async Task<object> privateConvertGetV2ConvertCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateConvertGetV2ConvertCurrencies",parameters);
    }

    public async Task<object> privateConvertGetV2ConvertQuotedPrice (object parameters = null)
    {
        return await this.callAsync ("privateConvertGetV2ConvertQuotedPrice",parameters);
    }

    public async Task<object> privateConvertGetV2ConvertConvertRecord (object parameters = null)
    {
        return await this.callAsync ("privateConvertGetV2ConvertConvertRecord",parameters);
    }

    public async Task<object> privateConvertGetV2ConvertBgbConvertCoinList (object parameters = null)
    {
        return await this.callAsync ("privateConvertGetV2ConvertBgbConvertCoinList",parameters);
    }

    public async Task<object> privateConvertGetV2ConvertBgbConvertRecords (object parameters = null)
    {
        return await this.callAsync ("privateConvertGetV2ConvertBgbConvertRecords",parameters);
    }

    public async Task<object> privateConvertPostV2ConvertTrade (object parameters = null)
    {
        return await this.callAsync ("privateConvertPostV2ConvertTrade",parameters);
    }

    public async Task<object> privateConvertPostV2ConvertBgbConvert (object parameters = null)
    {
        return await this.callAsync ("privateConvertPostV2ConvertBgbConvert",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSavingsProduct (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSavingsProduct",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSavingsAccount (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSavingsAccount",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSavingsAssets (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSavingsAssets",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSavingsRecords (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSavingsRecords",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSavingsSubscribeInfo (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSavingsSubscribeInfo",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSavingsSubscribeResult (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSavingsSubscribeResult",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSavingsRedeemResult (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSavingsRedeemResult",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSharkfinProduct (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSharkfinProduct",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSharkfinAccount (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSharkfinAccount",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSharkfinAssets (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSharkfinAssets",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSharkfinRecords (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSharkfinRecords",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSharkfinSubscribeInfo (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSharkfinSubscribeInfo",parameters);
    }

    public async Task<object> privateEarnGetV2EarnSharkfinSubscribeResult (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnSharkfinSubscribeResult",parameters);
    }

    public async Task<object> privateEarnGetV2EarnLoanOngoingOrders (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnLoanOngoingOrders",parameters);
    }

    public async Task<object> privateEarnGetV2EarnLoanRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnLoanRepayHistory",parameters);
    }

    public async Task<object> privateEarnGetV2EarnLoanReviseHistory (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnLoanReviseHistory",parameters);
    }

    public async Task<object> privateEarnGetV2EarnLoanBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnLoanBorrowHistory",parameters);
    }

    public async Task<object> privateEarnGetV2EarnLoanDebts (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnLoanDebts",parameters);
    }

    public async Task<object> privateEarnGetV2EarnLoanReduces (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnLoanReduces",parameters);
    }

    public async Task<object> privateEarnGetV2EarnAccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetV2EarnAccountAssets",parameters);
    }

    public async Task<object> privateEarnPostV2EarnSavingsSubscribe (object parameters = null)
    {
        return await this.callAsync ("privateEarnPostV2EarnSavingsSubscribe",parameters);
    }

    public async Task<object> privateEarnPostV2EarnSavingsRedeem (object parameters = null)
    {
        return await this.callAsync ("privateEarnPostV2EarnSavingsRedeem",parameters);
    }

    public async Task<object> privateEarnPostV2EarnSharkfinSubscribe (object parameters = null)
    {
        return await this.callAsync ("privateEarnPostV2EarnSharkfinSubscribe",parameters);
    }

    public async Task<object> privateEarnPostV2EarnLoanBorrow (object parameters = null)
    {
        return await this.callAsync ("privateEarnPostV2EarnLoanBorrow",parameters);
    }

    public async Task<object> privateEarnPostV2EarnLoanRepay (object parameters = null)
    {
        return await this.callAsync ("privateEarnPostV2EarnLoanRepay",parameters);
    }

    public async Task<object> privateEarnPostV2EarnLoanRevisePledge (object parameters = null)
    {
        return await this.callAsync ("privateEarnPostV2EarnLoanRevisePledge",parameters);
    }

    public async Task<object> privateCommonGetV2CommonTradeRate (object parameters = null)
    {
        return await this.callAsync ("privateCommonGetV2CommonTradeRate",parameters);
    }

    public async Task<object> privateUtaGetV3AccountAssets (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3AccountAssets",parameters);
    }

    public async Task<object> privateUtaGetV3AccountSettings (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3AccountSettings",parameters);
    }

    public async Task<object> privateUtaGetV3AccountFinancialRecords (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3AccountFinancialRecords",parameters);
    }

    public async Task<object> privateUtaGetV3AccountRepayableCoins (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3AccountRepayableCoins",parameters);
    }

    public async Task<object> privateUtaGetV3AccountPaymentCoins (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3AccountPaymentCoins",parameters);
    }

    public async Task<object> privateUtaGetV3AccountConvertRecords (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3AccountConvertRecords",parameters);
    }

    public async Task<object> privateUtaGetV3AccountTransferableCoins (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3AccountTransferableCoins",parameters);
    }

    public async Task<object> privateUtaGetV3AccountSubTransferRecord (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3AccountSubTransferRecord",parameters);
    }

    public async Task<object> privateUtaGetV3InsLoanTransfered (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3InsLoanTransfered",parameters);
    }

    public async Task<object> privateUtaGetV3InsLoanSymbols (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3InsLoanSymbols",parameters);
    }

    public async Task<object> privateUtaGetV3InsLoanRiskUnit (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3InsLoanRiskUnit",parameters);
    }

    public async Task<object> privateUtaGetV3InsLoanRepaidHistory (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3InsLoanRepaidHistory",parameters);
    }

    public async Task<object> privateUtaGetV3InsLoanProductInfos (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3InsLoanProductInfos",parameters);
    }

    public async Task<object> privateUtaGetV3InsLoanLoanOrder (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3InsLoanLoanOrder",parameters);
    }

    public async Task<object> privateUtaGetV3InsLoanLtvConvert (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3InsLoanLtvConvert",parameters);
    }

    public async Task<object> privateUtaGetV3InsLoanEnsureCoinsConvert (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3InsLoanEnsureCoinsConvert",parameters);
    }

    public async Task<object> privateUtaGetV3PositionCurrentPosition (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3PositionCurrentPosition",parameters);
    }

    public async Task<object> privateUtaGetV3PositionHistoryPosition (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3PositionHistoryPosition",parameters);
    }

    public async Task<object> privateUtaGetV3TradeOrderInfo (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3TradeOrderInfo",parameters);
    }

    public async Task<object> privateUtaGetV3TradeUnfilledOrders (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3TradeUnfilledOrders",parameters);
    }

    public async Task<object> privateUtaGetV3TradeHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3TradeHistoryOrders",parameters);
    }

    public async Task<object> privateUtaGetV3TradeFills (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3TradeFills",parameters);
    }

    public async Task<object> privateUtaGetV3UserSubList (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3UserSubList",parameters);
    }

    public async Task<object> privateUtaGetV3UserSubApiList (object parameters = null)
    {
        return await this.callAsync ("privateUtaGetV3UserSubApiList",parameters);
    }

    public async Task<object> privateUtaPostV3AccountSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3AccountSetLeverage",parameters);
    }

    public async Task<object> privateUtaPostV3AccountSetHoldMode (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3AccountSetHoldMode",parameters);
    }

    public async Task<object> privateUtaPostV3AccountRepay (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3AccountRepay",parameters);
    }

    public async Task<object> privateUtaPostV3AccountTransfer (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3AccountTransfer",parameters);
    }

    public async Task<object> privateUtaPostV3AccountSubTransfer (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3AccountSubTransfer",parameters);
    }

    public async Task<object> privateUtaPostV3AccountMaxOpenAvailable (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3AccountMaxOpenAvailable",parameters);
    }

    public async Task<object> privateUtaPostV3InsLoanBindUid (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3InsLoanBindUid",parameters);
    }

    public async Task<object> privateUtaPostV3TradePlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3TradePlaceOrder",parameters);
    }

    public async Task<object> privateUtaPostV3TradeModifyOrder (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3TradeModifyOrder",parameters);
    }

    public async Task<object> privateUtaPostV3TradeCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3TradeCancelOrder",parameters);
    }

    public async Task<object> privateUtaPostV3TradePlaceBatch (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3TradePlaceBatch",parameters);
    }

    public async Task<object> privateUtaPostV3TradeBatchModifyOrder (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3TradeBatchModifyOrder",parameters);
    }

    public async Task<object> privateUtaPostV3TradeCancelBatch (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3TradeCancelBatch",parameters);
    }

    public async Task<object> privateUtaPostV3TradeCancelSymbolOrder (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3TradeCancelSymbolOrder",parameters);
    }

    public async Task<object> privateUtaPostV3TradeClosePositions (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3TradeClosePositions",parameters);
    }

    public async Task<object> privateUtaPostV3UserCreateSub (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3UserCreateSub",parameters);
    }

    public async Task<object> privateUtaPostV3UserFreezeSub (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3UserFreezeSub",parameters);
    }

    public async Task<object> privateUtaPostV3UserCreateSubApi (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3UserCreateSubApi",parameters);
    }

    public async Task<object> privateUtaPostV3UserUpdateSubApi (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3UserUpdateSubApi",parameters);
    }

    public async Task<object> privateUtaPostV3UserDeleteSubApi (object parameters = null)
    {
        return await this.callAsync ("privateUtaPostV3UserDeleteSubApi",parameters);
    }

}