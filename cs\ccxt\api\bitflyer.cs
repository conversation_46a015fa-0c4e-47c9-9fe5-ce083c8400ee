// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bitflyer : Exchange
{
    public bitflyer (object args = null): base(args) {}

    public async Task<object> publicGetGetmarketsUsa (object parameters = null)
    {
        return await this.callAsync ("publicGetGetmarketsUsa",parameters);
    }

    public async Task<object> publicGetGetmarketsEu (object parameters = null)
    {
        return await this.callAsync ("publicGetGetmarketsEu",parameters);
    }

    public async Task<object> publicGetGetmarkets (object parameters = null)
    {
        return await this.callAsync ("publicGetGetmarkets",parameters);
    }

    public async Task<object> publicGetGetboard (object parameters = null)
    {
        return await this.callAsync ("publicGetGetboard",parameters);
    }

    public async Task<object> publicGetGetticker (object parameters = null)
    {
        return await this.callAsync ("publicGetGetticker",parameters);
    }

    public async Task<object> publicGetGetexecutions (object parameters = null)
    {
        return await this.callAsync ("publicGetGetexecutions",parameters);
    }

    public async Task<object> publicGetGethealth (object parameters = null)
    {
        return await this.callAsync ("publicGetGethealth",parameters);
    }

    public async Task<object> publicGetGetboardstate (object parameters = null)
    {
        return await this.callAsync ("publicGetGetboardstate",parameters);
    }

    public async Task<object> publicGetGetchats (object parameters = null)
    {
        return await this.callAsync ("publicGetGetchats",parameters);
    }

    public async Task<object> publicGetGetfundingrate (object parameters = null)
    {
        return await this.callAsync ("publicGetGetfundingrate",parameters);
    }

    public async Task<object> privateGetGetpermissions (object parameters = null)
    {
        return await this.callAsync ("privateGetGetpermissions",parameters);
    }

    public async Task<object> privateGetGetbalance (object parameters = null)
    {
        return await this.callAsync ("privateGetGetbalance",parameters);
    }

    public async Task<object> privateGetGetbalancehistory (object parameters = null)
    {
        return await this.callAsync ("privateGetGetbalancehistory",parameters);
    }

    public async Task<object> privateGetGetcollateral (object parameters = null)
    {
        return await this.callAsync ("privateGetGetcollateral",parameters);
    }

    public async Task<object> privateGetGetcollateralhistory (object parameters = null)
    {
        return await this.callAsync ("privateGetGetcollateralhistory",parameters);
    }

    public async Task<object> privateGetGetcollateralaccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetGetcollateralaccounts",parameters);
    }

    public async Task<object> privateGetGetaddresses (object parameters = null)
    {
        return await this.callAsync ("privateGetGetaddresses",parameters);
    }

    public async Task<object> privateGetGetcoinins (object parameters = null)
    {
        return await this.callAsync ("privateGetGetcoinins",parameters);
    }

    public async Task<object> privateGetGetcoinouts (object parameters = null)
    {
        return await this.callAsync ("privateGetGetcoinouts",parameters);
    }

    public async Task<object> privateGetGetbankaccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetGetbankaccounts",parameters);
    }

    public async Task<object> privateGetGetdeposits (object parameters = null)
    {
        return await this.callAsync ("privateGetGetdeposits",parameters);
    }

    public async Task<object> privateGetGetwithdrawals (object parameters = null)
    {
        return await this.callAsync ("privateGetGetwithdrawals",parameters);
    }

    public async Task<object> privateGetGetchildorders (object parameters = null)
    {
        return await this.callAsync ("privateGetGetchildorders",parameters);
    }

    public async Task<object> privateGetGetparentorders (object parameters = null)
    {
        return await this.callAsync ("privateGetGetparentorders",parameters);
    }

    public async Task<object> privateGetGetparentorder (object parameters = null)
    {
        return await this.callAsync ("privateGetGetparentorder",parameters);
    }

    public async Task<object> privateGetGetexecutions (object parameters = null)
    {
        return await this.callAsync ("privateGetGetexecutions",parameters);
    }

    public async Task<object> privateGetGetpositions (object parameters = null)
    {
        return await this.callAsync ("privateGetGetpositions",parameters);
    }

    public async Task<object> privateGetGettradingcommission (object parameters = null)
    {
        return await this.callAsync ("privateGetGettradingcommission",parameters);
    }

    public async Task<object> privatePostSendcoin (object parameters = null)
    {
        return await this.callAsync ("privatePostSendcoin",parameters);
    }

    public async Task<object> privatePostWithdraw (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdraw",parameters);
    }

    public async Task<object> privatePostSendchildorder (object parameters = null)
    {
        return await this.callAsync ("privatePostSendchildorder",parameters);
    }

    public async Task<object> privatePostCancelchildorder (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelchildorder",parameters);
    }

    public async Task<object> privatePostSendparentorder (object parameters = null)
    {
        return await this.callAsync ("privatePostSendparentorder",parameters);
    }

    public async Task<object> privatePostCancelparentorder (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelparentorder",parameters);
    }

    public async Task<object> privatePostCancelallchildorders (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelallchildorders",parameters);
    }

}