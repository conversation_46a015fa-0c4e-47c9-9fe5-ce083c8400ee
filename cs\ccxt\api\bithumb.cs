// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bithumb : Exchange
{
    public bithumb (object args = null): base(args) {}

    public async Task<object> publicGetTickerALLQuoteId (object parameters = null)
    {
        return await this.callAsync ("publicGetTickerALLQuoteId",parameters);
    }

    public async Task<object> publicGetTickerBaseIdQuoteId (object parameters = null)
    {
        return await this.callAsync ("publicGetTickerBaseIdQuoteId",parameters);
    }

    public async Task<object> publicGetOrderbookALLQuoteId (object parameters = null)
    {
        return await this.callAsync ("publicGetOrderbookALLQuoteId",parameters);
    }

    public async Task<object> publicGetOrderbookBaseIdQuoteId (object parameters = null)
    {
        return await this.callAsync ("publicGetOrderbookBaseIdQuoteId",parameters);
    }

    public async Task<object> publicGetTransactionHistoryBaseIdQuoteId (object parameters = null)
    {
        return await this.callAsync ("publicGetTransactionHistoryBaseIdQuoteId",parameters);
    }

    public async Task<object> publicGetNetworkInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetNetworkInfo",parameters);
    }

    public async Task<object> publicGetAssetsstatusMultichainALL (object parameters = null)
    {
        return await this.callAsync ("publicGetAssetsstatusMultichainALL",parameters);
    }

    public async Task<object> publicGetAssetsstatusMultichainCurrency (object parameters = null)
    {
        return await this.callAsync ("publicGetAssetsstatusMultichainCurrency",parameters);
    }

    public async Task<object> publicGetWithdrawMinimumALL (object parameters = null)
    {
        return await this.callAsync ("publicGetWithdrawMinimumALL",parameters);
    }

    public async Task<object> publicGetWithdrawMinimumCurrency (object parameters = null)
    {
        return await this.callAsync ("publicGetWithdrawMinimumCurrency",parameters);
    }

    public async Task<object> publicGetAssetsstatusALL (object parameters = null)
    {
        return await this.callAsync ("publicGetAssetsstatusALL",parameters);
    }

    public async Task<object> publicGetAssetsstatusBaseId (object parameters = null)
    {
        return await this.callAsync ("publicGetAssetsstatusBaseId",parameters);
    }

    public async Task<object> publicGetCandlestickBaseIdQuoteIdInterval (object parameters = null)
    {
        return await this.callAsync ("publicGetCandlestickBaseIdQuoteIdInterval",parameters);
    }

    public async Task<object> privatePostInfoAccount (object parameters = null)
    {
        return await this.callAsync ("privatePostInfoAccount",parameters);
    }

    public async Task<object> privatePostInfoBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostInfoBalance",parameters);
    }

    public async Task<object> privatePostInfoWalletAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostInfoWalletAddress",parameters);
    }

    public async Task<object> privatePostInfoTicker (object parameters = null)
    {
        return await this.callAsync ("privatePostInfoTicker",parameters);
    }

    public async Task<object> privatePostInfoOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostInfoOrders",parameters);
    }

    public async Task<object> privatePostInfoUserTransactions (object parameters = null)
    {
        return await this.callAsync ("privatePostInfoUserTransactions",parameters);
    }

    public async Task<object> privatePostInfoOrderDetail (object parameters = null)
    {
        return await this.callAsync ("privatePostInfoOrderDetail",parameters);
    }

    public async Task<object> privatePostTradePlace (object parameters = null)
    {
        return await this.callAsync ("privatePostTradePlace",parameters);
    }

    public async Task<object> privatePostTradeCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancel",parameters);
    }

    public async Task<object> privatePostTradeBtcWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeBtcWithdrawal",parameters);
    }

    public async Task<object> privatePostTradeKrwDeposit (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeKrwDeposit",parameters);
    }

    public async Task<object> privatePostTradeKrwWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeKrwWithdrawal",parameters);
    }

    public async Task<object> privatePostTradeMarketBuy (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeMarketBuy",parameters);
    }

    public async Task<object> privatePostTradeMarketSell (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeMarketSell",parameters);
    }

    public async Task<object> privatePostTradeStopLimit (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeStopLimit",parameters);
    }

}