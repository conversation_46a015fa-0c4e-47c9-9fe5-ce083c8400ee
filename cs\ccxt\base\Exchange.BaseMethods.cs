namespace ccxt;
// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class Exchange
{


    public virtual object describe()
    {
        return new Dictionary<string, object>() {
            { "id", null },
            { "name", null },
            { "countries", null },
            { "enableRateLimit", true },
            { "rateLimit", 2000 },
            { "timeout", this.timeout },
            { "certified", false },
            { "pro", false },
            { "alias", false },
            { "dex", false },
            { "has", new Dictionary<string, object>() {
                { "publicAPI", true },
                { "privateAPI", true },
                { "CORS", null },
                { "sandbox", null },
                { "spot", null },
                { "margin", null },
                { "swap", null },
                { "future", null },
                { "option", null },
                { "addMargin", null },
                { "borrowCrossMargin", null },
                { "borrowIsolatedMargin", null },
                { "borrowMargin", null },
                { "cancelAllOrders", null },
                { "cancelAllOrdersWs", null },
                { "cancelOrder", true },
                { "cancelOrderWs", null },
                { "cancelOrders", null },
                { "cancelOrdersWs", null },
                { "closeAllPositions", null },
                { "closePosition", null },
                { "createDepositAddress", null },
                { "createLimitBuyOrder", null },
                { "createLimitBuyOrderWs", null },
                { "createLimitOrder", true },
                { "createLimitOrderWs", null },
                { "createLimitSellOrder", null },
                { "createLimitSellOrderWs", null },
                { "createMarketBuyOrder", null },
                { "createMarketBuyOrderWs", null },
                { "createMarketBuyOrderWithCost", null },
                { "createMarketBuyOrderWithCostWs", null },
                { "createMarketOrder", true },
                { "createMarketOrderWs", true },
                { "createMarketOrderWithCost", null },
                { "createMarketOrderWithCostWs", null },
                { "createMarketSellOrder", null },
                { "createMarketSellOrderWs", null },
                { "createMarketSellOrderWithCost", null },
                { "createMarketSellOrderWithCostWs", null },
                { "createOrder", true },
                { "createOrderWs", null },
                { "createOrders", null },
                { "createOrderWithTakeProfitAndStopLoss", null },
                { "createOrderWithTakeProfitAndStopLossWs", null },
                { "createPostOnlyOrder", null },
                { "createPostOnlyOrderWs", null },
                { "createReduceOnlyOrder", null },
                { "createReduceOnlyOrderWs", null },
                { "createStopLimitOrder", null },
                { "createStopLimitOrderWs", null },
                { "createStopLossOrder", null },
                { "createStopLossOrderWs", null },
                { "createStopMarketOrder", null },
                { "createStopMarketOrderWs", null },
                { "createStopOrder", null },
                { "createStopOrderWs", null },
                { "createTakeProfitOrder", null },
                { "createTakeProfitOrderWs", null },
                { "createTrailingAmountOrder", null },
                { "createTrailingAmountOrderWs", null },
                { "createTrailingPercentOrder", null },
                { "createTrailingPercentOrderWs", null },
                { "createTriggerOrder", null },
                { "createTriggerOrderWs", null },
                { "deposit", null },
                { "editOrder", "emulated" },
                { "editOrders", null },
                { "editOrderWs", null },
                { "fetchAccounts", null },
                { "fetchBalance", true },
                { "fetchBalanceWs", null },
                { "fetchBidsAsks", null },
                { "fetchBorrowInterest", null },
                { "fetchBorrowRate", null },
                { "fetchBorrowRateHistories", null },
                { "fetchBorrowRateHistory", null },
                { "fetchBorrowRates", null },
                { "fetchBorrowRatesPerSymbol", null },
                { "fetchCanceledAndClosedOrders", null },
                { "fetchCanceledOrders", null },
                { "fetchClosedOrder", null },
                { "fetchClosedOrders", null },
                { "fetchClosedOrdersWs", null },
                { "fetchConvertCurrencies", null },
                { "fetchConvertQuote", null },
                { "fetchConvertTrade", null },
                { "fetchConvertTradeHistory", null },
                { "fetchCrossBorrowRate", null },
                { "fetchCrossBorrowRates", null },
                { "fetchCurrencies", "emulated" },
                { "fetchCurrenciesWs", "emulated" },
                { "fetchDeposit", null },
                { "fetchDepositAddress", null },
                { "fetchDepositAddresses", null },
                { "fetchDepositAddressesByNetwork", null },
                { "fetchDeposits", null },
                { "fetchDepositsWithdrawals", null },
                { "fetchDepositsWs", null },
                { "fetchDepositWithdrawFee", null },
                { "fetchDepositWithdrawFees", null },
                { "fetchFundingHistory", null },
                { "fetchFundingRate", null },
                { "fetchFundingRateHistory", null },
                { "fetchFundingInterval", null },
                { "fetchFundingIntervals", null },
                { "fetchFundingRates", null },
                { "fetchGreeks", null },
                { "fetchIndexOHLCV", null },
                { "fetchIsolatedBorrowRate", null },
                { "fetchIsolatedBorrowRates", null },
                { "fetchMarginAdjustmentHistory", null },
                { "fetchIsolatedPositions", null },
                { "fetchL2OrderBook", true },
                { "fetchL3OrderBook", null },
                { "fetchLastPrices", null },
                { "fetchLedger", null },
                { "fetchLedgerEntry", null },
                { "fetchLeverage", null },
                { "fetchLeverages", null },
                { "fetchLeverageTiers", null },
                { "fetchLiquidations", null },
                { "fetchLongShortRatio", null },
                { "fetchLongShortRatioHistory", null },
                { "fetchMarginMode", null },
                { "fetchMarginModes", null },
                { "fetchMarketLeverageTiers", null },
                { "fetchMarkets", true },
                { "fetchMarketsWs", null },
                { "fetchMarkOHLCV", null },
                { "fetchMyLiquidations", null },
                { "fetchMySettlementHistory", null },
                { "fetchMyTrades", null },
                { "fetchMyTradesWs", null },
                { "fetchOHLCV", null },
                { "fetchOHLCVWs", null },
                { "fetchOpenInterest", null },
                { "fetchOpenInterests", null },
                { "fetchOpenInterestHistory", null },
                { "fetchOpenOrder", null },
                { "fetchOpenOrders", null },
                { "fetchOpenOrdersWs", null },
                { "fetchOption", null },
                { "fetchOptionChain", null },
                { "fetchOrder", null },
                { "fetchOrderBook", true },
                { "fetchOrderBooks", null },
                { "fetchOrderBookWs", null },
                { "fetchOrders", null },
                { "fetchOrdersByStatus", null },
                { "fetchOrdersWs", null },
                { "fetchOrderTrades", null },
                { "fetchOrderWs", null },
                { "fetchPosition", null },
                { "fetchPositionHistory", null },
                { "fetchPositionsHistory", null },
                { "fetchPositionWs", null },
                { "fetchPositionMode", null },
                { "fetchPositions", null },
                { "fetchPositionsWs", null },
                { "fetchPositionsForSymbol", null },
                { "fetchPositionsForSymbolWs", null },
                { "fetchPositionsRisk", null },
                { "fetchPremiumIndexOHLCV", null },
                { "fetchSettlementHistory", null },
                { "fetchStatus", null },
                { "fetchTicker", true },
                { "fetchTickerWs", null },
                { "fetchTickers", null },
                { "fetchMarkPrices", null },
                { "fetchTickersWs", null },
                { "fetchTime", null },
                { "fetchTrades", true },
                { "fetchTradesWs", null },
                { "fetchTradingFee", null },
                { "fetchTradingFees", null },
                { "fetchTradingFeesWs", null },
                { "fetchTradingLimits", null },
                { "fetchTransactionFee", null },
                { "fetchTransactionFees", null },
                { "fetchTransactions", null },
                { "fetchTransfer", null },
                { "fetchTransfers", null },
                { "fetchUnderlyingAssets", null },
                { "fetchVolatilityHistory", null },
                { "fetchWithdrawAddresses", null },
                { "fetchWithdrawal", null },
                { "fetchWithdrawals", null },
                { "fetchWithdrawalsWs", null },
                { "fetchWithdrawalWhitelist", null },
                { "reduceMargin", null },
                { "repayCrossMargin", null },
                { "repayIsolatedMargin", null },
                { "setLeverage", null },
                { "setMargin", null },
                { "setMarginMode", null },
                { "setPositionMode", null },
                { "signIn", null },
                { "transfer", null },
                { "watchBalance", null },
                { "watchMyTrades", null },
                { "watchOHLCV", null },
                { "watchOHLCVForSymbols", null },
                { "watchOrderBook", null },
                { "watchBidsAsks", null },
                { "watchOrderBookForSymbols", null },
                { "watchOrders", null },
                { "watchOrdersForSymbols", null },
                { "watchPosition", null },
                { "watchPositions", null },
                { "watchStatus", null },
                { "watchTicker", null },
                { "watchTickers", null },
                { "watchTrades", null },
                { "watchTradesForSymbols", null },
                { "watchLiquidations", null },
                { "watchLiquidationsForSymbols", null },
                { "watchMyLiquidations", null },
                { "watchMyLiquidationsForSymbols", null },
                { "withdraw", null },
                { "ws", null },
            } },
            { "urls", new Dictionary<string, object>() {
                { "logo", null },
                { "api", null },
                { "www", null },
                { "doc", null },
                { "fees", null },
            } },
            { "api", null },
            { "requiredCredentials", new Dictionary<string, object>() {
                { "apiKey", true },
                { "secret", true },
                { "uid", false },
                { "accountId", false },
                { "login", false },
                { "password", false },
                { "twofa", false },
                { "privateKey", false },
                { "walletAddress", false },
                { "token", false },
            } },
            { "markets", null },
            { "currencies", new Dictionary<string, object>() {} },
            { "timeframes", null },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "tierBased", null },
                    { "percentage", null },
                    { "taker", null },
                    { "maker", null },
                } },
                { "funding", new Dictionary<string, object>() {
                    { "tierBased", null },
                    { "percentage", null },
                    { "withdraw", new Dictionary<string, object>() {} },
                    { "deposit", new Dictionary<string, object>() {} },
                } },
            } },
            { "status", new Dictionary<string, object>() {
                { "status", "ok" },
                { "updated", null },
                { "eta", null },
                { "url", null },
            } },
            { "exceptions", null },
            { "httpExceptions", new Dictionary<string, object>() {
                { "422", typeof(ExchangeError) },
                { "418", typeof(DDoSProtection) },
                { "429", typeof(RateLimitExceeded) },
                { "404", typeof(ExchangeNotAvailable) },
                { "409", typeof(ExchangeNotAvailable) },
                { "410", typeof(ExchangeNotAvailable) },
                { "451", typeof(ExchangeNotAvailable) },
                { "500", typeof(ExchangeNotAvailable) },
                { "501", typeof(ExchangeNotAvailable) },
                { "502", typeof(ExchangeNotAvailable) },
                { "520", typeof(ExchangeNotAvailable) },
                { "521", typeof(ExchangeNotAvailable) },
                { "522", typeof(ExchangeNotAvailable) },
                { "525", typeof(ExchangeNotAvailable) },
                { "526", typeof(ExchangeNotAvailable) },
                { "400", typeof(ExchangeNotAvailable) },
                { "403", typeof(ExchangeNotAvailable) },
                { "405", typeof(ExchangeNotAvailable) },
                { "503", typeof(ExchangeNotAvailable) },
                { "530", typeof(ExchangeNotAvailable) },
                { "408", typeof(RequestTimeout) },
                { "504", typeof(RequestTimeout) },
                { "401", typeof(AuthenticationError) },
                { "407", typeof(AuthenticationError) },
                { "511", typeof(AuthenticationError) },
            } },
            { "commonCurrencies", new Dictionary<string, object>() {
                { "XBT", "BTC" },
                { "BCHSV", "BSV" },
            } },
            { "precisionMode", TICK_SIZE },
            { "paddingMode", NO_PADDING },
            { "limits", new Dictionary<string, object>() {
                { "leverage", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "amount", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "price", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "cost", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
            } },
        };
    }

    public virtual object safeBoolN(object dictionaryOrList, object keys, object defaultValue = null)
    {
        /**
         * @ignore
         * @method
         * @description safely extract boolean value from dictionary or list
         * @returns {bool | undefined}
         */
        object value = this.safeValueN(dictionaryOrList, keys, defaultValue);
        if (isTrue((value is bool)))
        {
            return value;
        }
        return defaultValue;
    }

    public virtual object safeBool2(object dictionary, object key1, object key2, object defaultValue = null)
    {
        /**
         * @ignore
         * @method
         * @description safely extract boolean value from dictionary or list
         * @returns {bool | undefined}
         */
        return this.safeBoolN(dictionary, new List<object>() {key1, key2}, defaultValue);
    }

    public virtual object safeBool(object dictionary, object key, object defaultValue = null)
    {
        /**
         * @ignore
         * @method
         * @description safely extract boolean value from dictionary or list
         * @returns {bool | undefined}
         */
        return this.safeBoolN(dictionary, new List<object>() {key}, defaultValue);
    }

    public virtual object safeDictN(object dictionaryOrList, object keys, object defaultValue = null)
    {
        /**
         * @ignore
         * @method
         * @description safely extract a dictionary from dictionary or list
         * @returns {object | undefined}
         */
        object value = this.safeValueN(dictionaryOrList, keys, defaultValue);
        if (isTrue(isEqual(value, null)))
        {
            return defaultValue;
        }
        if (isTrue(((value is IDictionary<string, object>))))
        {
            if (!isTrue(((value is IList<object>) || (value.GetType().IsGenericType && value.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
            {
                return value;
            }
        }
        return defaultValue;
    }

    public virtual object safeDict(object dictionary, object key, object defaultValue = null)
    {
        /**
         * @ignore
         * @method
         * @description safely extract a dictionary from dictionary or list
         * @returns {object | undefined}
         */
        return this.safeDictN(dictionary, new List<object>() {key}, defaultValue);
    }

    public virtual object safeDict2(object dictionary, object key1, object key2, object defaultValue = null)
    {
        /**
         * @ignore
         * @method
         * @description safely extract a dictionary from dictionary or list
         * @returns {object | undefined}
         */
        return this.safeDictN(dictionary, new List<object>() {key1, key2}, defaultValue);
    }

    public virtual object safeListN(object dictionaryOrList, object keys, object defaultValue = null)
    {
        /**
         * @ignore
         * @method
         * @description safely extract an Array from dictionary or list
         * @returns {Array | undefined}
         */
        object value = this.safeValueN(dictionaryOrList, keys, defaultValue);
        if (isTrue(isEqual(value, null)))
        {
            return defaultValue;
        }
        if (isTrue(((value is IList<object>) || (value.GetType().IsGenericType && value.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            return value;
        }
        return defaultValue;
    }

    public virtual object safeList2(object dictionaryOrList, object key1, object key2, object defaultValue = null)
    {
        /**
         * @ignore
         * @method
         * @description safely extract an Array from dictionary or list
         * @returns {Array | undefined}
         */
        return this.safeListN(dictionaryOrList, new List<object>() {key1, key2}, defaultValue);
    }

    public virtual object safeList(object dictionaryOrList, object key, object defaultValue = null)
    {
        /**
         * @ignore
         * @method
         * @description safely extract an Array from dictionary or list
         * @returns {Array | undefined}
         */
        return this.safeListN(dictionaryOrList, new List<object>() {key}, defaultValue);
    }

    public virtual void handleDeltas(object orderbook, object deltas)
    {
        for (object i = 0; isLessThan(i, getArrayLength(deltas)); postFixIncrement(ref i))
        {
            this.handleDelta(orderbook, getValue(deltas, i));
        }
    }

    public virtual void handleDelta(object bookside, object delta)
    {
        throw new NotSupported ((string)add(this.id, " handleDelta not supported yet")) ;
    }

    public virtual void handleDeltasWithKeys(object bookSide, object deltas, object priceKey = null, object amountKey = null, object countOrIdKey = null)
    {
        priceKey ??= 0;
        amountKey ??= 1;
        countOrIdKey ??= 2;
        for (object i = 0; isLessThan(i, getArrayLength(deltas)); postFixIncrement(ref i))
        {
            object bidAsk = this.parseBidAsk(getValue(deltas, i), priceKey, amountKey, countOrIdKey);
            (bookSide as ccxt.pro.IOrderBookSide).storeArray(bidAsk);
        }
    }

    public virtual object getCacheIndex(object orderbook, object deltas)
    {
        // return the first index of the cache that can be applied to the orderbook or -1 if not possible
        return -1;
    }

    public virtual object findTimeframe(object timeframe, object timeframes = null)
    {
        if (isTrue(isEqual(timeframes, null)))
        {
            timeframes = this.timeframes;
        }
        object keys = new List<object>(((IDictionary<string,object>)timeframes).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
        {
            object key = getValue(keys, i);
            if (isTrue(isEqual(getValue(timeframes, key), timeframe)))
            {
                return key;
            }
        }
        return null;
    }

    public virtual object checkProxyUrlSettings(object url = null, object method = null, object headers = null, object body = null)
    {
        object usedProxies = new List<object>() {};
        object proxyUrl = null;
        if (isTrue(!isEqual(this.proxyUrl, null)))
        {
            ((IList<object>)usedProxies).Add("proxyUrl");
            proxyUrl = this.proxyUrl;
        }
        if (isTrue(!isEqual(this.proxy_url, null)))
        {
            ((IList<object>)usedProxies).Add("proxy_url");
            proxyUrl = this.proxy_url;
        }
        if (isTrue(!isEqual(this.proxyUrlCallback, null)))
        {
            ((IList<object>)usedProxies).Add("proxyUrlCallback");
            proxyUrl = callDynamically(this, "proxyUrlCallback", new object[] { url, method, headers, body });
        }
        if (isTrue(!isEqual(this.proxy_url_callback, null)))
        {
            ((IList<object>)usedProxies).Add("proxy_url_callback");
            proxyUrl = callDynamically(this, "proxy_url_callback", new object[] { url, method, headers, body });
        }
        // backwards-compatibility
        if (isTrue(!isEqual(this.proxy, null)))
        {
            ((IList<object>)usedProxies).Add("proxy");
            if (isTrue((this.proxy is Delegate)))
            {
                proxyUrl = callDynamically(this, "proxy", new object[] { url, method, headers, body });
            } else
            {
                proxyUrl = this.proxy;
            }
        }
        object length = getArrayLength(usedProxies);
        if (isTrue(isGreaterThan(length, 1)))
        {
            object joinedProxyNames = String.Join(",", ((IList<object>)usedProxies).ToArray());
            throw new InvalidProxySettings ((string)add(add(add(this.id, " you have multiple conflicting proxy settings ("), joinedProxyNames), "), please use only one from : proxyUrl, proxy_url, proxyUrlCallback, proxy_url_callback")) ;
        }
        return proxyUrl;
    }

    public virtual object urlEncoderForProxyUrl(object targetUrl)
    {
        // to be overriden
        object includesQuery = isGreaterThanOrEqual(getIndexOf(targetUrl, "?"), 0);
        object finalUrl = ((bool) isTrue(includesQuery)) ? this.encodeURIComponent(targetUrl) : targetUrl;
        return finalUrl;
    }

    public virtual object checkProxySettings(object url = null, object method = null, object headers = null, object body = null)
    {
        object usedProxies = new List<object>() {};
        object httpProxy = null;
        object httpsProxy = null;
        object socksProxy = null;
        // httpProxy
        object isHttpProxyDefined = this.valueIsDefined(this.httpProxy);
        object isHttp_proxy_defined = this.valueIsDefined(this.http_proxy);
        if (isTrue(isTrue(isHttpProxyDefined) || isTrue(isHttp_proxy_defined)))
        {
            ((IList<object>)usedProxies).Add("httpProxy");
            httpProxy = ((bool) isTrue(isHttpProxyDefined)) ? this.httpProxy : this.http_proxy;
        }
        object ishttpProxyCallbackDefined = this.valueIsDefined(this.httpProxyCallback);
        object ishttp_proxy_callback_defined = this.valueIsDefined(this.http_proxy_callback);
        if (isTrue(isTrue(ishttpProxyCallbackDefined) || isTrue(ishttp_proxy_callback_defined)))
        {
            ((IList<object>)usedProxies).Add("httpProxyCallback");
            httpProxy = ((bool) isTrue(ishttpProxyCallbackDefined)) ? callDynamically(this, "httpProxyCallback", new object[] { url, method, headers, body }) : callDynamically(this, "http_proxy_callback", new object[] { url, method, headers, body });
        }
        // httpsProxy
        object isHttpsProxyDefined = this.valueIsDefined(this.httpsProxy);
        object isHttps_proxy_defined = this.valueIsDefined(this.https_proxy);
        if (isTrue(isTrue(isHttpsProxyDefined) || isTrue(isHttps_proxy_defined)))
        {
            ((IList<object>)usedProxies).Add("httpsProxy");
            httpsProxy = ((bool) isTrue(isHttpsProxyDefined)) ? this.httpsProxy : this.https_proxy;
        }
        object ishttpsProxyCallbackDefined = this.valueIsDefined(this.httpsProxyCallback);
        object ishttps_proxy_callback_defined = this.valueIsDefined(this.https_proxy_callback);
        if (isTrue(isTrue(ishttpsProxyCallbackDefined) || isTrue(ishttps_proxy_callback_defined)))
        {
            ((IList<object>)usedProxies).Add("httpsProxyCallback");
            httpsProxy = ((bool) isTrue(ishttpsProxyCallbackDefined)) ? callDynamically(this, "httpsProxyCallback", new object[] { url, method, headers, body }) : callDynamically(this, "https_proxy_callback", new object[] { url, method, headers, body });
        }
        // socksProxy
        object isSocksProxyDefined = this.valueIsDefined(this.socksProxy);
        object isSocks_proxy_defined = this.valueIsDefined(this.socks_proxy);
        if (isTrue(isTrue(isSocksProxyDefined) || isTrue(isSocks_proxy_defined)))
        {
            ((IList<object>)usedProxies).Add("socksProxy");
            socksProxy = ((bool) isTrue(isSocksProxyDefined)) ? this.socksProxy : this.socks_proxy;
        }
        object issocksProxyCallbackDefined = this.valueIsDefined(this.socksProxyCallback);
        object issocks_proxy_callback_defined = this.valueIsDefined(this.socks_proxy_callback);
        if (isTrue(isTrue(issocksProxyCallbackDefined) || isTrue(issocks_proxy_callback_defined)))
        {
            ((IList<object>)usedProxies).Add("socksProxyCallback");
            socksProxy = ((bool) isTrue(issocksProxyCallbackDefined)) ? callDynamically(this, "socksProxyCallback", new object[] { url, method, headers, body }) : callDynamically(this, "socks_proxy_callback", new object[] { url, method, headers, body });
        }
        // check
        object length = getArrayLength(usedProxies);
        if (isTrue(isGreaterThan(length, 1)))
        {
            object joinedProxyNames = String.Join(",", ((IList<object>)usedProxies).ToArray());
            throw new InvalidProxySettings ((string)add(add(add(this.id, " you have multiple conflicting proxy settings ("), joinedProxyNames), "), please use only one from: httpProxy, httpsProxy, httpProxyCallback, httpsProxyCallback, socksProxy, socksProxyCallback")) ;
        }
        return new List<object>() {httpProxy, httpsProxy, socksProxy};
    }

    public virtual object checkWsProxySettings()
    {
        object usedProxies = new List<object>() {};
        object wsProxy = null;
        object wssProxy = null;
        object wsSocksProxy = null;
        // ws proxy
        object isWsProxyDefined = this.valueIsDefined(this.wsProxy);
        object is_ws_proxy_defined = this.valueIsDefined(this.ws_proxy);
        if (isTrue(isTrue(isWsProxyDefined) || isTrue(is_ws_proxy_defined)))
        {
            ((IList<object>)usedProxies).Add("wsProxy");
            wsProxy = ((bool) isTrue((isWsProxyDefined))) ? this.wsProxy : this.ws_proxy;
        }
        // wss proxy
        object isWssProxyDefined = this.valueIsDefined(this.wssProxy);
        object is_wss_proxy_defined = this.valueIsDefined(this.wss_proxy);
        if (isTrue(isTrue(isWssProxyDefined) || isTrue(is_wss_proxy_defined)))
        {
            ((IList<object>)usedProxies).Add("wssProxy");
            wssProxy = ((bool) isTrue((isWssProxyDefined))) ? this.wssProxy : this.wss_proxy;
        }
        // ws socks proxy
        object isWsSocksProxyDefined = this.valueIsDefined(this.wsSocksProxy);
        object is_ws_socks_proxy_defined = this.valueIsDefined(this.ws_socks_proxy);
        if (isTrue(isTrue(isWsSocksProxyDefined) || isTrue(is_ws_socks_proxy_defined)))
        {
            ((IList<object>)usedProxies).Add("wsSocksProxy");
            wsSocksProxy = ((bool) isTrue((isWsSocksProxyDefined))) ? this.wsSocksProxy : this.ws_socks_proxy;
        }
        // check
        object length = getArrayLength(usedProxies);
        if (isTrue(isGreaterThan(length, 1)))
        {
            object joinedProxyNames = String.Join(",", ((IList<object>)usedProxies).ToArray());
            throw new InvalidProxySettings ((string)add(add(add(this.id, " you have multiple conflicting proxy settings ("), joinedProxyNames), "), please use only one from: wsProxy, wssProxy, wsSocksProxy")) ;
        }
        return new List<object>() {wsProxy, wssProxy, wsSocksProxy};
    }

    public virtual void checkConflictingProxies(object proxyAgentSet, object proxyUrlSet)
    {
        if (isTrue(isTrue(proxyAgentSet) && isTrue(proxyUrlSet)))
        {
            throw new InvalidProxySettings ((string)add(this.id, " you have multiple conflicting proxy settings, please use only one from : proxyUrl, httpProxy, httpsProxy, socksProxy")) ;
        }
    }

    public virtual object checkAddress(object address = null)
    {
        if (isTrue(isEqual(address, null)))
        {
            throw new InvalidAddress ((string)add(this.id, " address is undefined")) ;
        }
        // check the address is not the same letter like 'aaaaa' nor too short nor has a space
        object uniqChars = (this.unique(this.stringToCharsArray(address)));
        object length = getArrayLength(uniqChars); // py transpiler trick
        if (isTrue(isTrue(isTrue(isEqual(length, 1)) || isTrue(isLessThan(((string)address).Length, this.minFundingAddressLength))) || isTrue(isGreaterThan(getIndexOf(address, " "), -1))))
        {
            throw new InvalidAddress ((string)add(add(add(add(add(this.id, " address is invalid or has less than "), ((object)this.minFundingAddressLength).ToString()), " characters: \""), ((object)address).ToString()), "\"")) ;
        }
        return address;
    }

    public virtual object findMessageHashes(WebSocketClient client, object element)
    {
        object result = new List<object>() {};
        object messageHashes = new List<object>(((IDictionary<string, ccxt.Exchange.Future>)client.futures).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(messageHashes)); postFixIncrement(ref i))
        {
            object messageHash = getValue(messageHashes, i);
            if (isTrue(isGreaterThanOrEqual(getIndexOf(messageHash, element), 0)))
            {
                ((IList<object>)result).Add(messageHash);
            }
        }
        return result;
    }

    public virtual object filterByLimit(object array, object limit = null, object key = null, object fromStart = null)
    {
        // array = ascending ? this.arraySlice (array, 0, limit) : this.arraySlice (array, -limit);
        // array = ascending ? this.arraySlice (array, -limit) : this.arraySlice (array, 0, limit);
        key ??= "timestamp";
        fromStart ??= false;
        if (isTrue(this.valueIsDefined(limit)))
        {
            object arrayLength = getArrayLength(array);
            if (isTrue(isGreaterThan(arrayLength, 0)))
            {
                object ascending = true;
                if (isTrue((inOp(getValue(array, 0), key))))
                {
                    object first = getValue(getValue(array, 0), key);
                    object last = getValue(getValue(array, subtract(arrayLength, 1)), key);
                    if (isTrue(isTrue(!isEqual(first, null)) && isTrue(!isEqual(last, null))))
                    {
                        ascending = isLessThanOrEqual(first, last); // true if array is sorted in ascending order based on 'timestamp'
                    }
                }
                if (isTrue(fromStart))
                {
                    if (isTrue(isGreaterThan(limit, arrayLength)))
                    {
                        limit = arrayLength;
                    }
                    if (isTrue(ascending))
                    {
                        array = this.arraySlice(array, 0, limit);
                    } else
                    {
                        array = this.arraySlice(array, prefixUnaryNeg(ref limit));
                    }
                } else
                {
                    if (isTrue(ascending))
                    {
                        array = this.arraySlice(array, prefixUnaryNeg(ref limit));
                    } else
                    {
                        array = this.arraySlice(array, 0, limit);
                    }
                }
            }
        }
        return array;
    }

    public virtual object filterBySinceLimit(object array, object since = null, object limit = null, object key = null, object tail = null)
    {
        key ??= "timestamp";
        tail ??= false;
        object sinceIsDefined = this.valueIsDefined(since);
        object parsedArray = ((object)this.toArray(array));
        object result = parsedArray;
        if (isTrue(sinceIsDefined))
        {
            result = new List<object>() {};
            for (object i = 0; isLessThan(i, getArrayLength(parsedArray)); postFixIncrement(ref i))
            {
                object entry = getValue(parsedArray, i);
                object value = this.safeValue(entry, key);
                if (isTrue(isTrue(value) && isTrue((isGreaterThanOrEqual(value, since)))))
                {
                    ((IList<object>)result).Add(entry);
                }
            }
        }
        if (isTrue(isTrue(tail) && isTrue(!isEqual(limit, null))))
        {
            return this.arraySlice(result, prefixUnaryNeg(ref limit));
        }
        // if the user provided a 'since' argument
        // we want to limit the result starting from the 'since'
        object shouldFilterFromStart = !isTrue(tail) && isTrue(sinceIsDefined);
        return this.filterByLimit(result, limit, key, shouldFilterFromStart);
    }

    public virtual object filterByValueSinceLimit(object array, object field, object value = null, object since = null, object limit = null, object key = null, object tail = null)
    {
        key ??= "timestamp";
        tail ??= false;
        object valueIsDefined = this.valueIsDefined(value);
        object sinceIsDefined = this.valueIsDefined(since);
        object parsedArray = ((object)this.toArray(array));
        object result = parsedArray;
        // single-pass filter for both symbol and since
        if (isTrue(isTrue(valueIsDefined) || isTrue(sinceIsDefined)))
        {
            result = new List<object>() {};
            for (object i = 0; isLessThan(i, getArrayLength(parsedArray)); postFixIncrement(ref i))
            {
                object entry = getValue(parsedArray, i);
                object entryFiledEqualValue = isEqual(getValue(entry, field), value);
                object firstCondition = ((bool) isTrue(valueIsDefined)) ? entryFiledEqualValue : true;
                object entryKeyValue = this.safeValue(entry, key);
                object entryKeyGESince = isTrue(isTrue((entryKeyValue)) && isTrue((!isEqual(since, null)))) && isTrue((isGreaterThanOrEqual(entryKeyValue, since)));
                object secondCondition = ((bool) isTrue(sinceIsDefined)) ? entryKeyGESince : true;
                if (isTrue(isTrue(firstCondition) && isTrue(secondCondition)))
                {
                    ((IList<object>)result).Add(entry);
                }
            }
        }
        if (isTrue(isTrue(tail) && isTrue(!isEqual(limit, null))))
        {
            return this.arraySlice(result, prefixUnaryNeg(ref limit));
        }
        return this.filterByLimit(result, limit, key, sinceIsDefined);
    }

    /**
     * @method
     * @name Exchange#setSandboxMode
     * @description set the sandbox mode for the exchange
     * @param {boolean} enabled true to enable sandbox mode, false to disable it
     */
    public virtual void setSandboxMode(object enabled)
    {
        if (isTrue(enabled))
        {
            if (isTrue(inOp(this.urls, "test")))
            {
                if (isTrue((getValue(this.urls, "api") is string)))
                {
                    ((IDictionary<string,object>)this.urls)["apiBackup"] = getValue(this.urls, "api");
                    ((IDictionary<string,object>)this.urls)["api"] = getValue(this.urls, "test");
                } else
                {
                    ((IDictionary<string,object>)this.urls)["apiBackup"] = this.clone(getValue(this.urls, "api"));
                    ((IDictionary<string,object>)this.urls)["api"] = this.clone(getValue(this.urls, "test"));
                }
            } else
            {
                throw new NotSupported ((string)add(this.id, " does not have a sandbox URL")) ;
            }
            // set flag
            this.isSandboxModeEnabled = true;
        } else if (isTrue(inOp(this.urls, "apiBackup")))
        {
            if (isTrue((getValue(this.urls, "api") is string)))
            {
                ((IDictionary<string,object>)this.urls)["api"] = ((object)getValue(this.urls, "apiBackup"));
            } else
            {
                ((IDictionary<string,object>)this.urls)["api"] = this.clone(getValue(this.urls, "apiBackup"));
            }
            object newUrls = this.omit(this.urls, "apiBackup");
            this.urls = newUrls;
            // set flag
            this.isSandboxModeEnabled = false;
        }
    }

    public virtual object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        return new Dictionary<string, object>() {};
    }

    public async virtual Task<object> fetchAccounts(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchAccounts() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTrades() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTradesWs(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTradesWs() is not supported yet")) ;
    }

    public async virtual Task<object> watchLiquidations(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "watchLiquidationsForSymbols")))
        {
            return await this.watchLiquidationsForSymbols(new List<object>() {symbol}, since, limit, parameters);
        }
        throw new NotSupported ((string)add(this.id, " watchLiquidations() is not supported yet")) ;
    }

    public async virtual Task<object> watchLiquidationsForSymbols(object symbols, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchLiquidationsForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> watchMyLiquidations(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "watchMyLiquidationsForSymbols")))
        {
            return this.watchMyLiquidationsForSymbols(new List<object>() {symbol}, since, limit, parameters);
        }
        throw new NotSupported ((string)add(this.id, " watchMyLiquidations() is not supported yet")) ;
    }

    public async virtual Task<object> watchMyLiquidationsForSymbols(object symbols, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchMyLiquidationsForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> watchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchTrades() is not supported yet")) ;
    }

    public async virtual Task<object> unWatchOrders(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " unWatchOrders() is not supported yet")) ;
    }

    public async virtual Task<object> unWatchTrades(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " unWatchTrades() is not supported yet")) ;
    }

    public async virtual Task<object> watchTradesForSymbols(object symbols, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchTradesForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> unWatchTradesForSymbols(object symbols, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " unWatchTradesForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> watchMyTradesForSymbols(object symbols, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchMyTradesForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> watchOrdersForSymbols(object symbols, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchOrdersForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> watchOHLCVForSymbols(object symbolsAndTimeframes, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchOHLCVForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> unWatchOHLCVForSymbols(object symbolsAndTimeframes, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " unWatchOHLCVForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> watchOrderBookForSymbols(object symbols, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchOrderBookForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> unWatchOrderBookForSymbols(object symbols, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " unWatchOrderBookForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> fetchDepositAddresses(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchDepositAddresses() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOrderBook() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOrderBookWs(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOrderBookWs() is not supported yet")) ;
    }

    public async virtual Task<object> fetchMarginMode(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchMarginModes")))
        {
            object marginModes = await this.fetchMarginModes(new List<object>() {symbol}, parameters);
            return this.safeDict(marginModes, symbol);
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchMarginMode() is not supported yet")) ;
        }
    }

    public async virtual Task<object> fetchMarginModes(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchMarginModes () is not supported yet")) ;
    }

    public async virtual Task<object> fetchRestOrderBookSafe(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object fetchSnapshotMaxRetries = this.handleOption("watchOrderBook", "maxRetries", 3);
        for (object i = 0; isLessThan(i, fetchSnapshotMaxRetries); postFixIncrement(ref i))
        {
            try
            {
                object orderBook = await this.fetchOrderBook(symbol, limit, parameters);
                return orderBook;
            } catch(Exception e)
            {
                if (isTrue(isEqual((add(i, 1)), fetchSnapshotMaxRetries)))
                {
                    throw e;
                }
            }
        }
        return null;
    }

    public async virtual Task<object> watchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchOrderBook() is not supported yet")) ;
    }

    public async virtual Task<object> unWatchOrderBook(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " unWatchOrderBook() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTime(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTime() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTradingLimits(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTradingLimits() is not supported yet")) ;
    }

    public virtual object parseCurrency(object rawCurrency)
    {
        throw new NotSupported ((string)add(this.id, " parseCurrency() is not supported yet")) ;
    }

    public virtual object parseCurrencies(object rawCurrencies)
    {
        object result = new Dictionary<string, object>() {};
        object arr = this.toArray(rawCurrencies);
        for (object i = 0; isLessThan(i, getArrayLength(arr)); postFixIncrement(ref i))
        {
            object parsed = this.parseCurrency(getValue(arr, i));
            object code = getValue(parsed, "code");
            ((IDictionary<string,object>)result)[(string)code] = parsed;
        }
        return result;
    }

    public virtual object parseMarket(object market)
    {
        throw new NotSupported ((string)add(this.id, " parseMarket() is not supported yet")) ;
    }

    public virtual object parseMarkets(object markets)
    {
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(markets)); postFixIncrement(ref i))
        {
            ((IList<object>)result).Add(this.parseMarket(getValue(markets, i)));
        }
        return result;
    }

    public virtual object parseTicker(object ticker, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseTicker() is not supported yet")) ;
    }

    public virtual object parseDepositAddress(object depositAddress, object currency = null)
    {
        throw new NotSupported ((string)add(this.id, " parseDepositAddress() is not supported yet")) ;
    }

    public virtual object parseTrade(object trade, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseTrade() is not supported yet")) ;
    }

    public virtual object parseTransaction(object transaction, object currency = null)
    {
        throw new NotSupported ((string)add(this.id, " parseTransaction() is not supported yet")) ;
    }

    public virtual object parseTransfer(object transfer, object currency = null)
    {
        throw new NotSupported ((string)add(this.id, " parseTransfer() is not supported yet")) ;
    }

    public virtual object parseAccount(object account)
    {
        throw new NotSupported ((string)add(this.id, " parseAccount() is not supported yet")) ;
    }

    public virtual object parseLedgerEntry(object item, object currency = null)
    {
        throw new NotSupported ((string)add(this.id, " parseLedgerEntry() is not supported yet")) ;
    }

    public virtual object parseOrder(object order, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseOrder() is not supported yet")) ;
    }

    public async virtual Task<object> fetchCrossBorrowRates(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchCrossBorrowRates() is not supported yet")) ;
    }

    public async virtual Task<object> fetchIsolatedBorrowRates(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchIsolatedBorrowRates() is not supported yet")) ;
    }

    public virtual object parseMarketLeverageTiers(object info, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseMarketLeverageTiers() is not supported yet")) ;
    }

    public async virtual Task<object> fetchLeverageTiers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchLeverageTiers() is not supported yet")) ;
    }

    public virtual object parsePosition(object position, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parsePosition() is not supported yet")) ;
    }

    public virtual object parseFundingRateHistory(object info, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseFundingRateHistory() is not supported yet")) ;
    }

    public virtual object parseBorrowInterest(object info, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseBorrowInterest() is not supported yet")) ;
    }

    public virtual object parseIsolatedBorrowRate(object info, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseIsolatedBorrowRate() is not supported yet")) ;
    }

    public virtual object parseWsTrade(object trade, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseWsTrade() is not supported yet")) ;
    }

    public virtual object parseWsOrder(object order, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseWsOrder() is not supported yet")) ;
    }

    public virtual object parseWsOrderTrade(object trade, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseWsOrderTrade() is not supported yet")) ;
    }

    public virtual object parseWsOHLCV(object ohlcv, object market = null)
    {
        return this.parseOHLCV(ohlcv, market);
    }

    public async virtual Task<object> fetchFundingRates(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchFundingRates() is not supported yet")) ;
    }

    public async virtual Task<object> fetchFundingIntervals(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchFundingIntervals() is not supported yet")) ;
    }

    public async virtual Task<object> watchFundingRate(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchFundingRate() is not supported yet")) ;
    }

    public async virtual Task<object> watchFundingRates(object symbols, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchFundingRates() is not supported yet")) ;
    }

    public async virtual Task<object> watchFundingRatesForSymbols(object symbols, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.watchFundingRates(symbols, parameters);
    }

    public async virtual Task<object> transfer(object code, object amount, object fromAccount, object toAccount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " transfer() is not supported yet")) ;
    }

    public async virtual Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " withdraw() is not supported yet")) ;
    }

    public async virtual Task<object> createDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " createDepositAddress() is not supported yet")) ;
    }

    public async virtual Task<object> setLeverage(object leverage, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " setLeverage() is not supported yet")) ;
    }

    public async virtual Task<object> fetchLeverage(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchLeverages")))
        {
            object leverages = await this.fetchLeverages(new List<object>() {symbol}, parameters);
            return this.safeDict(leverages, symbol);
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchLeverage() is not supported yet")) ;
        }
    }

    public async virtual Task<object> fetchLeverages(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchLeverages() is not supported yet")) ;
    }

    public async virtual Task<object> setPositionMode(object hedged, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " setPositionMode() is not supported yet")) ;
    }

    public async virtual Task<object> addMargin(object symbol, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " addMargin() is not supported yet")) ;
    }

    public async virtual Task<object> reduceMargin(object symbol, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " reduceMargin() is not supported yet")) ;
    }

    public async virtual Task<object> setMargin(object symbol, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " setMargin() is not supported yet")) ;
    }

    public async virtual Task<object> fetchLongShortRatio(object symbol, object timeframe = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchLongShortRatio() is not supported yet")) ;
    }

    public async virtual Task<object> fetchLongShortRatioHistory(object symbol = null, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchLongShortRatioHistory() is not supported yet")) ;
    }

    public async virtual Task<object> fetchMarginAdjustmentHistory(object symbol = null, object type = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchMarginAdjustmentHistory() is not supported yet")) ;
    }

    public async virtual Task<object> setMarginMode(object marginMode, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " setMarginMode() is not supported yet")) ;
    }

    public async virtual Task<object> fetchDepositAddressesByNetwork(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchDepositAddressesByNetwork() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOpenInterestHistory(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1h";
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOpenInterestHistory() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOpenInterest(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOpenInterest() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOpenInterests(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOpenInterests() is not supported yet")) ;
    }

    public async virtual Task<object> signIn(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " signIn() is not supported yet")) ;
    }

    public async virtual Task<object> fetchPaymentMethods(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPaymentMethods() is not supported yet")) ;
    }

    public virtual object parseToInt(object number)
    {
        // Solve Common parseInt misuse ex: parseInt ((since / 1000).toString ())
        // using a number as parameter which is not valid in ts
        object stringifiedNumber = this.numberToString(number);
        object convertedNumber = ((object)parseFloat(stringifiedNumber));
        return parseInt(convertedNumber);
    }

    public virtual object parseToNumeric(object number)
    {
        object stringVersion = this.numberToString(number); // this will convert 1.0 and 1 to "1" and 1.1 to "1.1"
        // keep this in mind:
        // in JS: 1 == 1.0 is true;  1 === 1.0 is true
        // in Python: 1 == 1.0 is true
        // in PHP 1 == 1.0 is true, but 1 === 1.0 is false.
        if (isTrue(isGreaterThanOrEqual(getIndexOf(stringVersion, "."), 0)))
        {
            return parseFloat(stringVersion);
        }
        return parseInt(stringVersion);
    }

    public virtual object isRoundNumber(object value)
    {
        // this method is similar to isInteger, but this is more loyal and does not check for types.
        // i.e. isRoundNumber(1.000) returns true, while isInteger(1.000) returns false
        object res = this.parseToNumeric((mod(value, 1)));
        return isEqual(res, 0);
    }

    public virtual object safeNumberOmitZero(object obj, object key, object defaultValue = null)
    {
        object value = this.safeString(obj, key);
        object final = this.parseNumber(this.omitZero(value));
        return ((bool) isTrue((isEqual(final, null)))) ? defaultValue : final;
    }

    public virtual object safeIntegerOmitZero(object obj, object key, object defaultValue = null)
    {
        object timestamp = this.safeInteger(obj, key, defaultValue);
        if (isTrue(isTrue(isEqual(timestamp, null)) || isTrue(isEqual(timestamp, 0))))
        {
            return null;
        }
        return timestamp;
    }

    public virtual void afterConstruct()
    {
        // networks
        this.createNetworksByIdObject();
        this.featuresGenerator();
        // init predefined markets if any
        if (isTrue(this.markets))
        {
            this.setMarkets(this.markets);
        }
        // init the request rate limiter
        this.initRestRateLimiter();
        // sanbox mode
        object isSandbox = this.safeBool2(this.options, "sandbox", "testnet", false);
        if (isTrue(isSandbox))
        {
            this.setSandboxMode(isSandbox);
        }
    }

    public virtual void initRestRateLimiter()
    {
        if (isTrue(isTrue(isEqual(this.rateLimit, null)) || isTrue((isTrue(!isEqual(this.id, null)) && isTrue(isEqual(this.rateLimit, -1))))))
        {
            throw new ExchangeError ((string)add(this.id, ".rateLimit property is not configured")) ;
        }
        object refillRate = this.MAX_VALUE;
        if (isTrue(isGreaterThan(this.rateLimit, 0)))
        {
            refillRate = divide(1, this.rateLimit);
        }
        object defaultBucket = new Dictionary<string, object>() {
            { "delay", 0.001 },
            { "capacity", 1 },
            { "cost", 1 },
            { "maxCapacity", 1000 },
            { "refillRate", refillRate },
        };
        object existingBucket = ((bool) isTrue((isEqual(this.tokenBucket, null)))) ? new Dictionary<string, object>() {} : this.tokenBucket;
        this.tokenBucket = this.extend(defaultBucket, existingBucket);
        this.initThrottler();
    }

    public virtual void featuresGenerator()
    {
        //
        // in the exchange-specific features can be something like this, where we support 'string' aliases too:
        //
        //     {
        //         'my' : {
        //             'createOrder' : {...},
        //         },
        //         'swap': {
        //             'linear': {
        //                 'extends': my',
        //             },
        //         },
        //     }
        //
        if (isTrue(isEqual(this.features, null)))
        {
            return;
        }
        // reconstruct
        object initialFeatures = this.features;
        this.features = new Dictionary<string, object>() {};
        object unifiedMarketTypes = new List<object>() {"spot", "swap", "future", "option"};
        object subTypes = new List<object>() {"linear", "inverse"};
        // atm only support basic methods, eg: 'createOrder', 'fetchOrder', 'fetchOrders', 'fetchMyTrades'
        for (object i = 0; isLessThan(i, getArrayLength(unifiedMarketTypes)); postFixIncrement(ref i))
        {
            object marketType = getValue(unifiedMarketTypes, i);
            // if marketType is not filled for this exchange, don't add that in `features`
            if (!isTrue((inOp(initialFeatures, marketType))))
            {
                ((IDictionary<string,object>)this.features)[(string)marketType] = null;
            } else
            {
                if (isTrue(isEqual(marketType, "spot")))
                {
                    ((IDictionary<string,object>)this.features)[(string)marketType] = this.featuresMapper(initialFeatures, marketType, null);
                } else
                {
                    ((IDictionary<string,object>)this.features)[(string)marketType] = new Dictionary<string, object>() {};
                    for (object j = 0; isLessThan(j, getArrayLength(subTypes)); postFixIncrement(ref j))
                    {
                        object subType = getValue(subTypes, j);
                        ((IDictionary<string,object>)getValue(this.features, marketType))[(string)subType] = this.featuresMapper(initialFeatures, marketType, subType);
                    }
                }
            }
        }
    }

    public virtual object featuresMapper(object initialFeatures, object marketType, object subType = null)
    {
        object featuresObj = ((bool) isTrue((!isEqual(subType, null)))) ? getValue(getValue(initialFeatures, marketType), subType) : getValue(initialFeatures, marketType);
        // if exchange does not have that market-type (eg. future>inverse)
        if (isTrue(isEqual(featuresObj, null)))
        {
            return null;
        }
        object extendsStr = this.safeString(featuresObj, "extends");
        if (isTrue(!isEqual(extendsStr, null)))
        {
            featuresObj = this.omit(featuresObj, "extends");
            object extendObj = this.featuresMapper(initialFeatures, extendsStr);
            featuresObj = this.deepExtend(extendObj, featuresObj);
        }
        //
        // ### corrections ###
        //
        // createOrder
        if (isTrue(inOp(featuresObj, "createOrder")))
        {
            object value = this.safeDict(getValue(featuresObj, "createOrder"), "attachedStopLossTakeProfit");
            ((IDictionary<string,object>)getValue(featuresObj, "createOrder"))["stopLoss"] = value;
            ((IDictionary<string,object>)getValue(featuresObj, "createOrder"))["takeProfit"] = value;
            if (isTrue(isEqual(marketType, "spot")))
            {
                // default 'hedged': false
                ((IDictionary<string,object>)getValue(featuresObj, "createOrder"))["hedged"] = false;
                // default 'leverage': false
                if (!isTrue((inOp(getValue(featuresObj, "createOrder"), "leverage"))))
                {
                    ((IDictionary<string,object>)getValue(featuresObj, "createOrder"))["leverage"] = false;
                }
            }
            // default 'GTC' to true
            if (isTrue(isEqual(this.safeBool(getValue(getValue(featuresObj, "createOrder"), "timeInForce"), "GTC"), null)))
            {
                ((IDictionary<string,object>)getValue(getValue(featuresObj, "createOrder"), "timeInForce"))["GTC"] = true;
            }
        }
        // other methods
        object keys = new List<object>(((IDictionary<string,object>)featuresObj).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
        {
            object key = getValue(keys, i);
            object featureBlock = getValue(featuresObj, key);
            if (isTrue(!isTrue(this.inArray(key, new List<object>() {"sandbox"})) && isTrue(!isEqual(featureBlock, null))))
            {
                // default "symbolRequired" to false to all methods (except `createOrder`)
                if (!isTrue((inOp(featureBlock, "symbolRequired"))))
                {
                    ((IDictionary<string,object>)featureBlock)["symbolRequired"] = this.inArray(key, new List<object>() {"createOrder", "createOrders", "fetchOHLCV"});
                }
            }
        }
        return featuresObj;
    }

    public virtual object orderbookChecksumMessage(object symbol)
    {
        return add(add(symbol, " : "), "orderbook data checksum validation failed. You can reconnect by calling watchOrderBook again or you can mute the error by setting exchange.options[\"watchOrderBook\"][\"checksum\"] = false");
    }

    public virtual void createNetworksByIdObject()
    {
        // automatically generate network-id-to-code mappings
        object networkIdsToCodesGenerated = this.invertFlatStringDictionary(this.safeValue(this.options, "networks", new Dictionary<string, object>() {})); // invert defined networks dictionary
        ((IDictionary<string,object>)this.options)["networksById"] = this.extend(networkIdsToCodesGenerated, this.safeValue(this.options, "networksById", new Dictionary<string, object>() {})); // support manually overriden "networksById" dictionary too
    }

    public virtual object getDefaultOptions()
    {
        return new Dictionary<string, object>() {
            { "defaultNetworkCodeReplacements", new Dictionary<string, object>() {
                { "ETH", new Dictionary<string, object>() {
                    { "ERC20", "ETH" },
                } },
                { "TRX", new Dictionary<string, object>() {
                    { "TRC20", "TRX" },
                } },
                { "CRO", new Dictionary<string, object>() {
                    { "CRC20", "CRONOS" },
                } },
                { "BRC20", new Dictionary<string, object>() {
                    { "BRC20", "BTC" },
                } },
            } },
        };
    }

    public virtual object safeLedgerEntry(object entry, object currency = null)
    {
        currency = this.safeCurrency(null, currency);
        object direction = this.safeString(entry, "direction");
        object before = this.safeString(entry, "before");
        object after = this.safeString(entry, "after");
        object amount = this.safeString(entry, "amount");
        if (isTrue(!isEqual(amount, null)))
        {
            if (isTrue(isTrue(isEqual(before, null)) && isTrue(!isEqual(after, null))))
            {
                before = Precise.stringSub(after, amount);
            } else if (isTrue(isTrue(!isEqual(before, null)) && isTrue(isEqual(after, null))))
            {
                after = Precise.stringAdd(before, amount);
            }
        }
        if (isTrue(isTrue(!isEqual(before, null)) && isTrue(!isEqual(after, null))))
        {
            if (isTrue(isEqual(direction, null)))
            {
                if (isTrue(Precise.stringGt(before, after)))
                {
                    direction = "out";
                }
                if (isTrue(Precise.stringGt(after, before)))
                {
                    direction = "in";
                }
            }
        }
        object fee = this.safeValue(entry, "fee");
        if (isTrue(!isEqual(fee, null)))
        {
            ((IDictionary<string,object>)fee)["cost"] = this.safeNumber(fee, "cost");
        }
        object timestamp = this.safeInteger(entry, "timestamp");
        object info = this.safeDict(entry, "info", new Dictionary<string, object>() {});
        return new Dictionary<string, object>() {
            { "id", this.safeString(entry, "id") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "direction", direction },
            { "account", this.safeString(entry, "account") },
            { "referenceId", this.safeString(entry, "referenceId") },
            { "referenceAccount", this.safeString(entry, "referenceAccount") },
            { "type", this.safeString(entry, "type") },
            { "currency", getValue(currency, "code") },
            { "amount", this.parseNumber(amount) },
            { "before", this.parseNumber(before) },
            { "after", this.parseNumber(after) },
            { "status", this.safeString(entry, "status") },
            { "fee", fee },
            { "info", info },
        };
    }

    public virtual object safeCurrencyStructure(object currency)
    {
        // derive data from networks: deposit, withdraw, active, fee, limits, precision
        object networks = this.safeDict(currency, "networks", new Dictionary<string, object>() {});
        object keys = new List<object>(((IDictionary<string,object>)networks).Keys);
        object length = getArrayLength(keys);
        if (isTrue(!isEqual(length, 0)))
        {
            for (object i = 0; isLessThan(i, length); postFixIncrement(ref i))
            {
                object key = getValue(keys, i);
                object network = getValue(networks, key);
                object deposit = this.safeBool(network, "deposit");
                object currencyDeposit = this.safeBool(currency, "deposit");
                if (isTrue(isTrue(isEqual(currencyDeposit, null)) || isTrue(deposit)))
                {
                    ((IDictionary<string,object>)currency)["deposit"] = deposit;
                }
                object withdraw = this.safeBool(network, "withdraw");
                object currencyWithdraw = this.safeBool(currency, "withdraw");
                if (isTrue(isTrue(isEqual(currencyWithdraw, null)) || isTrue(withdraw)))
                {
                    ((IDictionary<string,object>)currency)["withdraw"] = withdraw;
                }
                // set network 'active' to false if D or W is disabled
                object active = this.safeBool(network, "active");
                if (isTrue(isEqual(active, null)))
                {
                    if (isTrue(isTrue(deposit) && isTrue(withdraw)))
                    {
                        ((IDictionary<string,object>)getValue(getValue(currency, "networks"), key))["active"] = true;
                    } else if (isTrue(isTrue(!isEqual(deposit, null)) && isTrue(!isEqual(withdraw, null))))
                    {
                        ((IDictionary<string,object>)getValue(getValue(currency, "networks"), key))["active"] = false;
                    }
                }
                active = this.safeBool(getValue(getValue(currency, "networks"), key), "active"); // dict might have been updated on above lines, so access directly instead of `network` variable
                object currencyActive = this.safeBool(currency, "active");
                if (isTrue(isTrue(isEqual(currencyActive, null)) || isTrue(active)))
                {
                    ((IDictionary<string,object>)currency)["active"] = active;
                }
                // find lowest fee (which is more desired)
                object fee = this.safeString(network, "fee");
                object feeMain = this.safeString(currency, "fee");
                if (isTrue(isTrue(isEqual(feeMain, null)) || isTrue(Precise.stringLt(fee, feeMain))))
                {
                    ((IDictionary<string,object>)currency)["fee"] = this.parseNumber(fee);
                }
                // find lowest precision (which is more desired)
                object precision = this.safeString(network, "precision");
                object precisionMain = this.safeString(currency, "precision");
                if (isTrue(isTrue(isEqual(precisionMain, null)) || isTrue(Precise.stringGt(precision, precisionMain))))
                {
                    ((IDictionary<string,object>)currency)["precision"] = this.parseNumber(precision);
                }
                // limits
                object limits = this.safeDict(network, "limits");
                object limitsMain = this.safeDict(currency, "limits");
                if (isTrue(isEqual(limitsMain, null)))
                {
                    ((IDictionary<string,object>)currency)["limits"] = new Dictionary<string, object>() {};
                }
                // deposits
                object limitsDeposit = this.safeDict(limits, "deposit");
                object limitsDepositMain = this.safeDict(limitsMain, "deposit");
                if (isTrue(isEqual(limitsDepositMain, null)))
                {
                    ((IDictionary<string,object>)getValue(currency, "limits"))["deposit"] = new Dictionary<string, object>() {};
                }
                object limitsDepositMin = this.safeString(limitsDeposit, "min");
                object limitsDepositMax = this.safeString(limitsDeposit, "max");
                object limitsDepositMinMain = this.safeString(limitsDepositMain, "min");
                object limitsDepositMaxMain = this.safeString(limitsDepositMain, "max");
                // find min
                if (isTrue(isTrue(isEqual(limitsDepositMinMain, null)) || isTrue(Precise.stringLt(limitsDepositMin, limitsDepositMinMain))))
                {
                    ((IDictionary<string,object>)getValue(getValue(currency, "limits"), "deposit"))["min"] = this.parseNumber(limitsDepositMin);
                }
                // find max
                if (isTrue(isTrue(isEqual(limitsDepositMaxMain, null)) || isTrue(Precise.stringGt(limitsDepositMax, limitsDepositMaxMain))))
                {
                    ((IDictionary<string,object>)getValue(getValue(currency, "limits"), "deposit"))["max"] = this.parseNumber(limitsDepositMax);
                }
                // withdrawals
                object limitsWithdraw = this.safeDict(limits, "withdraw");
                object limitsWithdrawMain = this.safeDict(limitsMain, "withdraw");
                if (isTrue(isEqual(limitsWithdrawMain, null)))
                {
                    ((IDictionary<string,object>)getValue(currency, "limits"))["withdraw"] = new Dictionary<string, object>() {};
                }
                object limitsWithdrawMin = this.safeString(limitsWithdraw, "min");
                object limitsWithdrawMax = this.safeString(limitsWithdraw, "max");
                object limitsWithdrawMinMain = this.safeString(limitsWithdrawMain, "min");
                object limitsWithdrawMaxMain = this.safeString(limitsWithdrawMain, "max");
                // find min
                if (isTrue(isTrue(isEqual(limitsWithdrawMinMain, null)) || isTrue(Precise.stringLt(limitsWithdrawMin, limitsWithdrawMinMain))))
                {
                    ((IDictionary<string,object>)getValue(getValue(currency, "limits"), "withdraw"))["min"] = this.parseNumber(limitsWithdrawMin);
                }
                // find max
                if (isTrue(isTrue(isEqual(limitsWithdrawMaxMain, null)) || isTrue(Precise.stringGt(limitsWithdrawMax, limitsWithdrawMaxMain))))
                {
                    ((IDictionary<string,object>)getValue(getValue(currency, "limits"), "withdraw"))["max"] = this.parseNumber(limitsWithdrawMax);
                }
            }
        }
        return this.extend(new Dictionary<string, object>() {
            { "info", null },
            { "id", null },
            { "numericId", null },
            { "code", null },
            { "precision", null },
            { "type", null },
            { "name", null },
            { "active", null },
            { "deposit", null },
            { "withdraw", null },
            { "fee", null },
            { "fees", new Dictionary<string, object>() {} },
            { "networks", new Dictionary<string, object>() {} },
            { "limits", new Dictionary<string, object>() {
                { "deposit", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "withdraw", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
            } },
        }, currency);
    }

    public virtual object safeMarketStructure(object market = null)
    {
        object cleanStructure = new Dictionary<string, object>() {
            { "id", null },
            { "lowercaseId", null },
            { "symbol", null },
            { "base", null },
            { "quote", null },
            { "settle", null },
            { "baseId", null },
            { "quoteId", null },
            { "settleId", null },
            { "type", null },
            { "spot", null },
            { "margin", null },
            { "swap", null },
            { "future", null },
            { "option", null },
            { "index", null },
            { "active", null },
            { "contract", null },
            { "linear", null },
            { "inverse", null },
            { "subType", null },
            { "taker", null },
            { "maker", null },
            { "contractSize", null },
            { "expiry", null },
            { "expiryDatetime", null },
            { "strike", null },
            { "optionType", null },
            { "precision", new Dictionary<string, object>() {
                { "amount", null },
                { "price", null },
                { "cost", null },
                { "base", null },
                { "quote", null },
            } },
            { "limits", new Dictionary<string, object>() {
                { "leverage", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "amount", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "price", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "cost", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
            } },
            { "marginModes", new Dictionary<string, object>() {
                { "cross", null },
                { "isolated", null },
            } },
            { "created", null },
            { "info", null },
        };
        if (isTrue(!isEqual(market, null)))
        {
            object result = this.extend(cleanStructure, market);
            // set undefined swap/future/etc
            if (isTrue(getValue(result, "spot")))
            {
                if (isTrue(isEqual(getValue(result, "contract"), null)))
                {
                    ((IDictionary<string,object>)result)["contract"] = false;
                }
                if (isTrue(isEqual(getValue(result, "swap"), null)))
                {
                    ((IDictionary<string,object>)result)["swap"] = false;
                }
                if (isTrue(isEqual(getValue(result, "future"), null)))
                {
                    ((IDictionary<string,object>)result)["future"] = false;
                }
                if (isTrue(isEqual(getValue(result, "option"), null)))
                {
                    ((IDictionary<string,object>)result)["option"] = false;
                }
                if (isTrue(isEqual(getValue(result, "index"), null)))
                {
                    ((IDictionary<string,object>)result)["index"] = false;
                }
            }
            return result;
        }
        return cleanStructure;
    }

    public virtual object setMarkets(object markets, object currencies = null)
    {
        object values = new List<object>() {};
        this.markets_by_id = this.createSafeDictionary();
        // handle marketId conflicts
        // we insert spot markets first
        object marketValues = this.sortBy(this.toArray(markets), "spot", true, true);
        for (object i = 0; isLessThan(i, getArrayLength(marketValues)); postFixIncrement(ref i))
        {
            object value = getValue(marketValues, i);
            if (isTrue(inOp(this.markets_by_id, getValue(value, "id"))))
            {
                object marketsByIdArray = ((object)getValue(this.markets_by_id, getValue(value, "id")));
                ((IList<object>)marketsByIdArray).Add(value);
                ((IDictionary<string,object>)this.markets_by_id)[(string)getValue(value, "id")] = marketsByIdArray;
            } else
            {
                ((IDictionary<string,object>)this.markets_by_id)[(string)getValue(value, "id")] = ((object)new List<object>() {value});
            }
            object market = this.deepExtend(this.safeMarketStructure(), new Dictionary<string, object>() {
                { "precision", this.precision },
                { "limits", this.limits },
            }, getValue(this.fees, "trading"), value);
            if (isTrue(getValue(market, "linear")))
            {
                ((IDictionary<string,object>)market)["subType"] = "linear";
            } else if (isTrue(getValue(market, "inverse")))
            {
                ((IDictionary<string,object>)market)["subType"] = "inverse";
            } else
            {
                ((IDictionary<string,object>)market)["subType"] = null;
            }
            ((IList<object>)values).Add(market);
        }
        this.markets = this.mapToSafeMap(((object)this.indexBy(values, "symbol")));
        object marketsSortedBySymbol = this.keysort(this.markets);
        object marketsSortedById = this.keysort(this.markets_by_id);
        this.symbols = new List<object>(((IDictionary<string,object>)marketsSortedBySymbol).Keys);
        this.ids = new List<object>(((IDictionary<string,object>)marketsSortedById).Keys);
        if (isTrue(!isEqual(currencies, null)))
        {
            // currencies is always undefined when called in constructor but not when called from loadMarkets
            this.currencies = this.mapToSafeMap(this.deepExtend(this.currencies, currencies));
        } else
        {
            object baseCurrencies = new List<object>() {};
            object quoteCurrencies = new List<object>() {};
            for (object i = 0; isLessThan(i, getArrayLength(values)); postFixIncrement(ref i))
            {
                object market = getValue(values, i);
                object defaultCurrencyPrecision = ((bool) isTrue((isEqual(this.precisionMode, DECIMAL_PLACES)))) ? 8 : this.parseNumber("1e-8");
                object marketPrecision = this.safeDict(market, "precision", new Dictionary<string, object>() {});
                if (isTrue(inOp(market, "base")))
                {
                    object currency = this.safeCurrencyStructure(new Dictionary<string, object>() {
                        { "id", this.safeString2(market, "baseId", "base") },
                        { "numericId", this.safeInteger(market, "baseNumericId") },
                        { "code", this.safeString(market, "base") },
                        { "precision", this.safeValue2(marketPrecision, "base", "amount", defaultCurrencyPrecision) },
                    });
                    ((IList<object>)baseCurrencies).Add(currency);
                }
                if (isTrue(inOp(market, "quote")))
                {
                    object currency = this.safeCurrencyStructure(new Dictionary<string, object>() {
                        { "id", this.safeString2(market, "quoteId", "quote") },
                        { "numericId", this.safeInteger(market, "quoteNumericId") },
                        { "code", this.safeString(market, "quote") },
                        { "precision", this.safeValue2(marketPrecision, "quote", "price", defaultCurrencyPrecision) },
                    });
                    ((IList<object>)quoteCurrencies).Add(currency);
                }
            }
            baseCurrencies = this.sortBy(baseCurrencies, "code", false, "");
            quoteCurrencies = this.sortBy(quoteCurrencies, "code", false, "");
            this.baseCurrencies = this.mapToSafeMap(this.indexBy(baseCurrencies, "code"));
            this.quoteCurrencies = this.mapToSafeMap(this.indexBy(quoteCurrencies, "code"));
            object allCurrencies = this.arrayConcat(baseCurrencies, quoteCurrencies);
            object groupedCurrencies = this.groupBy(allCurrencies, "code");
            object codes = new List<object>(((IDictionary<string,object>)groupedCurrencies).Keys);
            object resultingCurrencies = new List<object>() {};
            for (object i = 0; isLessThan(i, getArrayLength(codes)); postFixIncrement(ref i))
            {
                object code = getValue(codes, i);
                object groupedCurrenciesCode = this.safeList(groupedCurrencies, code, new List<object>() {});
                object highestPrecisionCurrency = this.safeValue(groupedCurrenciesCode, 0);
                for (object j = 1; isLessThan(j, getArrayLength(groupedCurrenciesCode)); postFixIncrement(ref j))
                {
                    object currentCurrency = getValue(groupedCurrenciesCode, j);
                    if (isTrue(isEqual(this.precisionMode, TICK_SIZE)))
                    {
                        highestPrecisionCurrency = ((bool) isTrue((isLessThan(getValue(currentCurrency, "precision"), getValue(highestPrecisionCurrency, "precision"))))) ? currentCurrency : highestPrecisionCurrency;
                    } else
                    {
                        highestPrecisionCurrency = ((bool) isTrue((isGreaterThan(getValue(currentCurrency, "precision"), getValue(highestPrecisionCurrency, "precision"))))) ? currentCurrency : highestPrecisionCurrency;
                    }
                }
                ((IList<object>)resultingCurrencies).Add(highestPrecisionCurrency);
            }
            object sortedCurrencies = this.sortBy(resultingCurrencies, "code");
            this.currencies = this.mapToSafeMap(this.deepExtend(this.currencies, this.indexBy(sortedCurrencies, "code")));
        }
        this.currencies_by_id = this.indexBySafe(this.currencies, "id");
        object currenciesSortedByCode = this.keysort(this.currencies);
        this.codes = new List<object>(((IDictionary<string,object>)currenciesSortedByCode).Keys);
        return this.markets;
    }

    public virtual object getDescribeForExtendedWsExchange(object currentRestInstance, object parentRestInstance, object wsBaseDescribe)
    {
        object extendedRestDescribe = this.deepExtend(((Exchange)parentRestInstance).describe(), ((Exchange)currentRestInstance).describe());
        object superWithRestDescribe = this.deepExtend(extendedRestDescribe, wsBaseDescribe);
        return superWithRestDescribe;
    }

    public virtual object safeBalance(object balance)
    {
        object balances = this.omit(balance, new List<object>() {"info", "timestamp", "datetime", "free", "used", "total"});
        object codes = new List<object>(((IDictionary<string,object>)balances).Keys);
        ((IDictionary<string,object>)balance)["free"] = new Dictionary<string, object>() {};
        ((IDictionary<string,object>)balance)["used"] = new Dictionary<string, object>() {};
        ((IDictionary<string,object>)balance)["total"] = new Dictionary<string, object>() {};
        object debtBalance = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(codes)); postFixIncrement(ref i))
        {
            object code = getValue(codes, i);
            object total = this.safeString(getValue(balance, code), "total");
            object free = this.safeString(getValue(balance, code), "free");
            object used = this.safeString(getValue(balance, code), "used");
            object debt = this.safeString(getValue(balance, code), "debt");
            if (isTrue(isTrue(isTrue((isEqual(total, null))) && isTrue((!isEqual(free, null)))) && isTrue((!isEqual(used, null)))))
            {
                total = Precise.stringAdd(free, used);
            }
            if (isTrue(isTrue(isTrue((isEqual(free, null))) && isTrue((!isEqual(total, null)))) && isTrue((!isEqual(used, null)))))
            {
                free = Precise.stringSub(total, used);
            }
            if (isTrue(isTrue(isTrue((isEqual(used, null))) && isTrue((!isEqual(total, null)))) && isTrue((!isEqual(free, null)))))
            {
                used = Precise.stringSub(total, free);
            }
            ((IDictionary<string,object>)getValue(balance, code))["free"] = this.parseNumber(free);
            ((IDictionary<string,object>)getValue(balance, code))["used"] = this.parseNumber(used);
            ((IDictionary<string,object>)getValue(balance, code))["total"] = this.parseNumber(total);
            ((IDictionary<string,object>)getValue(balance, "free"))[(string)code] = getValue(getValue(balance, code), "free");
            ((IDictionary<string,object>)getValue(balance, "used"))[(string)code] = getValue(getValue(balance, code), "used");
            ((IDictionary<string,object>)getValue(balance, "total"))[(string)code] = getValue(getValue(balance, code), "total");
            if (isTrue(!isEqual(debt, null)))
            {
                ((IDictionary<string,object>)getValue(balance, code))["debt"] = this.parseNumber(debt);
                ((IDictionary<string,object>)debtBalance)[(string)code] = getValue(getValue(balance, code), "debt");
            }
        }
        object debtBalanceArray = new List<object>(((IDictionary<string,object>)debtBalance).Keys);
        object length = getArrayLength(debtBalanceArray);
        if (isTrue(length))
        {
            ((IDictionary<string,object>)balance)["debt"] = debtBalance;
        }
        return ((object)balance);
    }

    public virtual object safeOrder(object order, object market = null)
    {
        // parses numbers as strings
        // * it is important pass the trades as unparsed rawTrades
        object amount = this.omitZero(this.safeString(order, "amount"));
        object remaining = this.safeString(order, "remaining");
        object filled = this.safeString(order, "filled");
        object cost = this.safeString(order, "cost");
        object average = this.omitZero(this.safeString(order, "average"));
        object price = this.omitZero(this.safeString(order, "price"));
        object lastTradeTimeTimestamp = this.safeInteger(order, "lastTradeTimestamp");
        object symbol = this.safeString(order, "symbol");
        object side = this.safeString(order, "side");
        object status = this.safeString(order, "status");
        object parseFilled = (isEqual(filled, null));
        object parseCost = (isEqual(cost, null));
        object parseLastTradeTimeTimestamp = (isEqual(lastTradeTimeTimestamp, null));
        object fee = this.safeValue(order, "fee");
        object parseFee = (isEqual(fee, null));
        object parseFees = isEqual(this.safeValue(order, "fees"), null);
        object parseSymbol = isEqual(symbol, null);
        object parseSide = isEqual(side, null);
        object shouldParseFees = isTrue(parseFee) || isTrue(parseFees);
        object fees = this.safeList(order, "fees", new List<object>() {});
        object trades = new List<object>() {};
        object isTriggerOrSLTpOrder = (isTrue((isTrue(!isEqual(this.safeString(order, "triggerPrice"), null)) || isTrue((!isEqual(this.safeString(order, "stopLossPrice"), null))))) || isTrue((!isEqual(this.safeString(order, "takeProfitPrice"), null))));
        if (isTrue(isTrue(isTrue(parseFilled) || isTrue(parseCost)) || isTrue(shouldParseFees)))
        {
            object rawTrades = this.safeValue(order, "trades", trades);
            // const oldNumber = this.number;
            // we parse trades as strings here!
            // i don't think this is needed anymore
            // (this as any).number = String;
            object firstTrade = this.safeValue(rawTrades, 0);
            // parse trades if they haven't already been parsed
            object tradesAreParsed = (isTrue(isTrue((!isEqual(firstTrade, null))) && isTrue((inOp(firstTrade, "info")))) && isTrue((inOp(firstTrade, "id"))));
            if (!isTrue(tradesAreParsed))
            {
                trades = this.parseTrades(rawTrades, market);
            } else
            {
                trades = rawTrades;
            }
            // this.number = oldNumber; why parse trades as strings if you read the value using `safeString` ?
            object tradesLength = 0;
            object isArray = ((trades is IList<object>) || (trades.GetType().IsGenericType && trades.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))));
            if (isTrue(isArray))
            {
                tradesLength = getArrayLength(trades);
            }
            if (isTrue(isTrue(isArray) && isTrue((isGreaterThan(tradesLength, 0)))))
            {
                // move properties that are defined in trades up into the order
                if (isTrue(isEqual(getValue(order, "symbol"), null)))
                {
                    ((IDictionary<string,object>)order)["symbol"] = getValue(getValue(trades, 0), "symbol");
                }
                if (isTrue(isEqual(getValue(order, "side"), null)))
                {
                    ((IDictionary<string,object>)order)["side"] = getValue(getValue(trades, 0), "side");
                }
                if (isTrue(isEqual(getValue(order, "type"), null)))
                {
                    ((IDictionary<string,object>)order)["type"] = getValue(getValue(trades, 0), "type");
                }
                if (isTrue(isEqual(getValue(order, "id"), null)))
                {
                    ((IDictionary<string,object>)order)["id"] = getValue(getValue(trades, 0), "order");
                }
                if (isTrue(parseFilled))
                {
                    filled = "0";
                }
                if (isTrue(parseCost))
                {
                    cost = "0";
                }
                for (object i = 0; isLessThan(i, getArrayLength(trades)); postFixIncrement(ref i))
                {
                    object trade = getValue(trades, i);
                    object tradeAmount = this.safeString(trade, "amount");
                    if (isTrue(isTrue(parseFilled) && isTrue((!isEqual(tradeAmount, null)))))
                    {
                        filled = Precise.stringAdd(filled, tradeAmount);
                    }
                    object tradeCost = this.safeString(trade, "cost");
                    if (isTrue(isTrue(parseCost) && isTrue((!isEqual(tradeCost, null)))))
                    {
                        cost = Precise.stringAdd(cost, tradeCost);
                    }
                    if (isTrue(parseSymbol))
                    {
                        symbol = this.safeString(trade, "symbol");
                    }
                    if (isTrue(parseSide))
                    {
                        side = this.safeString(trade, "side");
                    }
                    object tradeTimestamp = this.safeValue(trade, "timestamp");
                    if (isTrue(isTrue(parseLastTradeTimeTimestamp) && isTrue((!isEqual(tradeTimestamp, null)))))
                    {
                        if (isTrue(isEqual(lastTradeTimeTimestamp, null)))
                        {
                            lastTradeTimeTimestamp = tradeTimestamp;
                        } else
                        {
                            lastTradeTimeTimestamp = mathMax(lastTradeTimeTimestamp, tradeTimestamp);
                        }
                    }
                    if (isTrue(shouldParseFees))
                    {
                        object tradeFees = this.safeValue(trade, "fees");
                        if (isTrue(!isEqual(tradeFees, null)))
                        {
                            for (object j = 0; isLessThan(j, getArrayLength(tradeFees)); postFixIncrement(ref j))
                            {
                                object tradeFee = getValue(tradeFees, j);
                                ((IList<object>)fees).Add(this.extend(new Dictionary<string, object>() {}, tradeFee));
                            }
                        } else
                        {
                            object tradeFee = this.safeValue(trade, "fee");
                            if (isTrue(!isEqual(tradeFee, null)))
                            {
                                ((IList<object>)fees).Add(this.extend(new Dictionary<string, object>() {}, tradeFee));
                            }
                        }
                    }
                }
            }
        }
        if (isTrue(shouldParseFees))
        {
            object reducedFees = ((bool) isTrue(this.reduceFees)) ? this.reduceFeesByCurrency(fees) : fees;
            object reducedLength = getArrayLength(reducedFees);
            for (object i = 0; isLessThan(i, reducedLength); postFixIncrement(ref i))
            {
                ((IDictionary<string,object>)getValue(reducedFees, i))["cost"] = this.safeNumber(getValue(reducedFees, i), "cost");
                if (isTrue(inOp(getValue(reducedFees, i), "rate")))
                {
                    ((IDictionary<string,object>)getValue(reducedFees, i))["rate"] = this.safeNumber(getValue(reducedFees, i), "rate");
                }
            }
            if (isTrue(!isTrue(parseFee) && isTrue((isEqual(reducedLength, 0)))))
            {
                // copy fee to avoid modification by reference
                object feeCopy = this.deepExtend(fee);
                ((IDictionary<string,object>)feeCopy)["cost"] = this.safeNumber(feeCopy, "cost");
                if (isTrue(inOp(feeCopy, "rate")))
                {
                    ((IDictionary<string,object>)feeCopy)["rate"] = this.safeNumber(feeCopy, "rate");
                }
                ((IList<object>)reducedFees).Add(feeCopy);
            }
            ((IDictionary<string,object>)order)["fees"] = reducedFees;
            if (isTrue(isTrue(parseFee) && isTrue((isEqual(reducedLength, 1)))))
            {
                ((IDictionary<string,object>)order)["fee"] = getValue(reducedFees, 0);
            }
        }
        if (isTrue(isEqual(amount, null)))
        {
            // ensure amount = filled + remaining
            if (isTrue(isTrue(!isEqual(filled, null)) && isTrue(!isEqual(remaining, null))))
            {
                amount = Precise.stringAdd(filled, remaining);
            } else if (isTrue(isEqual(status, "closed")))
            {
                amount = filled;
            }
        }
        if (isTrue(isEqual(filled, null)))
        {
            if (isTrue(isTrue(!isEqual(amount, null)) && isTrue(!isEqual(remaining, null))))
            {
                filled = Precise.stringSub(amount, remaining);
            } else if (isTrue(isTrue(isEqual(status, "closed")) && isTrue(!isEqual(amount, null))))
            {
                filled = amount;
            }
        }
        if (isTrue(isEqual(remaining, null)))
        {
            if (isTrue(isTrue(!isEqual(amount, null)) && isTrue(!isEqual(filled, null))))
            {
                remaining = Precise.stringSub(amount, filled);
            } else if (isTrue(isEqual(status, "closed")))
            {
                remaining = "0";
            }
        }
        // ensure that the average field is calculated correctly
        object inverse = this.safeBool(market, "inverse", false);
        object contractSize = this.numberToString(this.safeValue(market, "contractSize", 1));
        // inverse
        // price = filled * contract size / cost
        //
        // linear
        // price = cost / (filled * contract size)
        if (isTrue(isEqual(average, null)))
        {
            if (isTrue(isTrue(isTrue((!isEqual(filled, null))) && isTrue((!isEqual(cost, null)))) && isTrue(Precise.stringGt(filled, "0"))))
            {
                object filledTimesContractSize = Precise.stringMul(filled, contractSize);
                if (isTrue(inverse))
                {
                    average = Precise.stringDiv(filledTimesContractSize, cost);
                } else
                {
                    average = Precise.stringDiv(cost, filledTimesContractSize);
                }
            }
        }
        // similarly
        // inverse
        // cost = filled * contract size / price
        //
        // linear
        // cost = filled * contract size * price
        object costPriceExists = isTrue((!isEqual(average, null))) || isTrue((!isEqual(price, null)));
        if (isTrue(isTrue(isTrue(parseCost) && isTrue((!isEqual(filled, null)))) && isTrue(costPriceExists)))
        {
            object multiplyPrice = null;
            if (isTrue(isEqual(average, null)))
            {
                multiplyPrice = price;
            } else
            {
                multiplyPrice = average;
            }
            // contract trading
            object filledTimesContractSize = Precise.stringMul(filled, contractSize);
            if (isTrue(inverse))
            {
                cost = Precise.stringDiv(filledTimesContractSize, multiplyPrice);
            } else
            {
                cost = Precise.stringMul(filledTimesContractSize, multiplyPrice);
            }
        }
        // support for market orders
        object orderType = this.safeValue(order, "type");
        object emptyPrice = isTrue((isEqual(price, null))) || isTrue(Precise.stringEquals(price, "0"));
        if (isTrue(isTrue(emptyPrice) && isTrue((isEqual(orderType, "market")))))
        {
            price = average;
        }
        // we have trades with string values at this point so we will mutate them
        for (object i = 0; isLessThan(i, getArrayLength(trades)); postFixIncrement(ref i))
        {
            object entry = getValue(trades, i);
            ((IDictionary<string,object>)entry)["amount"] = this.safeNumber(entry, "amount");
            ((IDictionary<string,object>)entry)["price"] = this.safeNumber(entry, "price");
            ((IDictionary<string,object>)entry)["cost"] = this.safeNumber(entry, "cost");
            object tradeFee = this.safeDict(entry, "fee", new Dictionary<string, object>() {});
            ((IDictionary<string,object>)tradeFee)["cost"] = this.safeNumber(tradeFee, "cost");
            if (isTrue(inOp(tradeFee, "rate")))
            {
                ((IDictionary<string,object>)tradeFee)["rate"] = this.safeNumber(tradeFee, "rate");
            }
            object entryFees = this.safeList(entry, "fees", new List<object>() {});
            for (object j = 0; isLessThan(j, getArrayLength(entryFees)); postFixIncrement(ref j))
            {
                ((IDictionary<string,object>)getValue(entryFees, j))["cost"] = this.safeNumber(getValue(entryFees, j), "cost");
            }
            ((IDictionary<string,object>)entry)["fees"] = entryFees;
            ((IDictionary<string,object>)entry)["fee"] = tradeFee;
        }
        object timeInForce = this.safeString(order, "timeInForce");
        object postOnly = this.safeValue(order, "postOnly");
        // timeInForceHandling
        if (isTrue(isEqual(timeInForce, null)))
        {
            if (isTrue(!isTrue(isTriggerOrSLTpOrder) && isTrue((isEqual(this.safeString(order, "type"), "market")))))
            {
                timeInForce = "IOC";
            }
            // allow postOnly override
            if (isTrue(postOnly))
            {
                timeInForce = "PO";
            }
        } else if (isTrue(isEqual(postOnly, null)))
        {
            // timeInForce is not undefined here
            postOnly = isEqual(timeInForce, "PO");
        }
        object timestamp = this.safeInteger(order, "timestamp");
        object lastUpdateTimestamp = this.safeInteger(order, "lastUpdateTimestamp");
        object datetime = this.safeString(order, "datetime");
        if (isTrue(isEqual(datetime, null)))
        {
            datetime = this.iso8601(timestamp);
        }
        object triggerPrice = this.parseNumber(this.safeString2(order, "triggerPrice", "stopPrice"));
        object takeProfitPrice = this.parseNumber(this.safeString(order, "takeProfitPrice"));
        object stopLossPrice = this.parseNumber(this.safeString(order, "stopLossPrice"));
        return this.extend(order, new Dictionary<string, object>() {
            { "id", this.safeString(order, "id") },
            { "clientOrderId", this.safeString(order, "clientOrderId") },
            { "timestamp", timestamp },
            { "datetime", datetime },
            { "symbol", symbol },
            { "type", this.safeString(order, "type") },
            { "side", side },
            { "lastTradeTimestamp", lastTradeTimeTimestamp },
            { "lastUpdateTimestamp", lastUpdateTimestamp },
            { "price", this.parseNumber(price) },
            { "amount", this.parseNumber(amount) },
            { "cost", this.parseNumber(cost) },
            { "average", this.parseNumber(average) },
            { "filled", this.parseNumber(filled) },
            { "remaining", this.parseNumber(remaining) },
            { "timeInForce", timeInForce },
            { "postOnly", postOnly },
            { "trades", trades },
            { "reduceOnly", this.safeValue(order, "reduceOnly") },
            { "stopPrice", triggerPrice },
            { "triggerPrice", triggerPrice },
            { "takeProfitPrice", takeProfitPrice },
            { "stopLossPrice", stopLossPrice },
            { "status", status },
            { "fee", this.safeValue(order, "fee") },
        });
    }

    public virtual object parseOrders(object orders, object market = null, object since = null, object limit = null, object parameters = null)
    {
        //
        // the value of orders is either a dict or a list
        //
        // dict
        //
        //     {
        //         'id1': { ... },
        //         'id2': { ... },
        //         'id3': { ... },
        //         ...
        //     }
        //
        // list
        //
        //     [
        //         { 'id': 'id1', ... },
        //         { 'id': 'id2', ... },
        //         { 'id': 'id3', ... },
        //         ...
        //     ]
        //
        parameters ??= new Dictionary<string, object>();
        object results = new List<object>() {};
        if (isTrue(((orders is IList<object>) || (orders.GetType().IsGenericType && orders.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            for (object i = 0; isLessThan(i, getArrayLength(orders)); postFixIncrement(ref i))
            {
                object parsed = this.parseOrder(getValue(orders, i), market); // don't inline this call
                object order = this.extend(parsed, parameters);
                ((IList<object>)results).Add(order);
            }
        } else
        {
            object ids = new List<object>(((IDictionary<string,object>)orders).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(ids)); postFixIncrement(ref i))
            {
                object id = getValue(ids, i);
                object idExtended = this.extend(new Dictionary<string, object>() {
                    { "id", id },
                }, getValue(orders, id));
                object parsedOrder = this.parseOrder(idExtended, market); // don't  inline these calls
                object order = this.extend(parsedOrder, parameters);
                ((IList<object>)results).Add(order);
            }
        }
        results = this.sortBy(results, "timestamp");
        object symbol = ((bool) isTrue((!isEqual(market, null)))) ? getValue(market, "symbol") : null;
        return this.filterBySymbolSinceLimit(results, symbol, since, limit);
    }

    public virtual object calculateFee(object symbol, object type, object side, object amount, object price, object takerOrMaker = null, object parameters = null)
    {
        /**
        * @method
        * @description calculates the presumptive fee that would be charged for an order
        * @param {string} symbol unified market symbol
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade, in units of the base currency on most exchanges, or number of contracts
        * @param {float} price the price for the order to be filled at, in units of the quote currency
        * @param {string} takerOrMaker 'taker' or 'maker'
        * @param {object} params
        * @returns {object} contains the rate, the percentage multiplied to the order amount to obtain the fee amount, and cost, the total value of the fee in units of the quote currency, for the order
        */
        takerOrMaker ??= "taker";
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isTrue(isEqual(type, "market")) && isTrue(isEqual(takerOrMaker, "maker"))))
        {
            throw new ArgumentsRequired ((string)add(this.id, " calculateFee() - you have provided incompatible arguments - \"market\" type order can not be \"maker\". Change either the \"type\" or the \"takerOrMaker\" argument to calculate the fee.")) ;
        }
        object market = getValue(this.markets, symbol);
        object feeSide = this.safeString(market, "feeSide", "quote");
        object useQuote = null;
        if (isTrue(isEqual(feeSide, "get")))
        {
            // the fee is always in the currency you get
            useQuote = isEqual(side, "sell");
        } else if (isTrue(isEqual(feeSide, "give")))
        {
            // the fee is always in the currency you give
            useQuote = isEqual(side, "buy");
        } else
        {
            // the fee is always in feeSide currency
            useQuote = isEqual(feeSide, "quote");
        }
        object cost = this.numberToString(amount);
        object key = null;
        if (isTrue(useQuote))
        {
            object priceString = this.numberToString(price);
            cost = Precise.stringMul(cost, priceString);
            key = "quote";
        } else
        {
            key = "base";
        }
        // for derivatives, the fee is in 'settle' currency
        if (!isTrue(getValue(market, "spot")))
        {
            key = "settle";
        }
        // even if `takerOrMaker` argument was set to 'maker', for 'market' orders we should forcefully override it to 'taker'
        if (isTrue(isEqual(type, "market")))
        {
            takerOrMaker = "taker";
        }
        object rate = this.safeString(market, takerOrMaker);
        cost = Precise.stringMul(cost, rate);
        return new Dictionary<string, object>() {
            { "type", takerOrMaker },
            { "currency", getValue(market, key) },
            { "rate", this.parseNumber(rate) },
            { "cost", this.parseNumber(cost) },
        };
    }

    public virtual object safeLiquidation(object liquidation, object market = null)
    {
        object contracts = this.safeString(liquidation, "contracts");
        object contractSize = this.safeString(market, "contractSize");
        object price = this.safeString(liquidation, "price");
        object baseValue = this.safeString(liquidation, "baseValue");
        object quoteValue = this.safeString(liquidation, "quoteValue");
        if (isTrue(isTrue(isTrue(isTrue((isEqual(baseValue, null))) && isTrue((!isEqual(contracts, null)))) && isTrue((!isEqual(contractSize, null)))) && isTrue((!isEqual(price, null)))))
        {
            baseValue = Precise.stringMul(contracts, contractSize);
        }
        if (isTrue(isTrue(isTrue((isEqual(quoteValue, null))) && isTrue((!isEqual(baseValue, null)))) && isTrue((!isEqual(price, null)))))
        {
            quoteValue = Precise.stringMul(baseValue, price);
        }
        ((IDictionary<string,object>)liquidation)["contracts"] = this.parseNumber(contracts);
        ((IDictionary<string,object>)liquidation)["contractSize"] = this.parseNumber(contractSize);
        ((IDictionary<string,object>)liquidation)["price"] = this.parseNumber(price);
        ((IDictionary<string,object>)liquidation)["baseValue"] = this.parseNumber(baseValue);
        ((IDictionary<string,object>)liquidation)["quoteValue"] = this.parseNumber(quoteValue);
        return liquidation;
    }

    public virtual object safeTrade(object trade, object market = null)
    {
        object amount = this.safeString(trade, "amount");
        object price = this.safeString(trade, "price");
        object cost = this.safeString(trade, "cost");
        if (isTrue(isEqual(cost, null)))
        {
            // contract trading
            object contractSize = this.safeString(market, "contractSize");
            object multiplyPrice = price;
            if (isTrue(!isEqual(contractSize, null)))
            {
                object inverse = this.safeBool(market, "inverse", false);
                if (isTrue(inverse))
                {
                    multiplyPrice = Precise.stringDiv("1", price);
                }
                multiplyPrice = Precise.stringMul(multiplyPrice, contractSize);
            }
            cost = Precise.stringMul(multiplyPrice, amount);
        }
        var resultFeeresultFeesVariable = this.parsedFeeAndFees(trade);
        var resultFee = ((IList<object>) resultFeeresultFeesVariable)[0];
        var resultFees = ((IList<object>) resultFeeresultFeesVariable)[1];
        ((IDictionary<string,object>)trade)["fee"] = resultFee;
        ((IDictionary<string,object>)trade)["fees"] = resultFees;
        ((IDictionary<string,object>)trade)["amount"] = this.parseNumber(amount);
        ((IDictionary<string,object>)trade)["price"] = this.parseNumber(price);
        ((IDictionary<string,object>)trade)["cost"] = this.parseNumber(cost);
        return trade;
    }

    public virtual object parsedFeeAndFees(object container)
    {
        object fee = this.safeDict(container, "fee");
        object fees = this.safeList(container, "fees");
        object feeDefined = !isEqual(fee, null);
        object feesDefined = !isEqual(fees, null);
        // parsing only if at least one of them is defined
        object shouldParseFees = (isTrue(feeDefined) || isTrue(feesDefined));
        if (isTrue(shouldParseFees))
        {
            if (isTrue(feeDefined))
            {
                fee = this.parseFeeNumeric(fee);
            }
            if (!isTrue(feesDefined))
            {
                // just set it directly, no further processing needed
                fees = new List<object>() {fee};
            }
            // 'fees' were set, so reparse them
            object reducedFees = ((bool) isTrue(this.reduceFees)) ? this.reduceFeesByCurrency(fees) : fees;
            object reducedLength = getArrayLength(reducedFees);
            for (object i = 0; isLessThan(i, reducedLength); postFixIncrement(ref i))
            {
                ((List<object>)reducedFees)[Convert.ToInt32(i)] = this.parseFeeNumeric(getValue(reducedFees, i));
            }
            fees = reducedFees;
            if (isTrue(isEqual(reducedLength, 1)))
            {
                fee = getValue(reducedFees, 0);
            } else if (isTrue(isEqual(reducedLength, 0)))
            {
                fee = null;
            }
        }
        // in case `fee & fees` are undefined, set `fees` as empty array
        if (isTrue(isEqual(fee, null)))
        {
            fee = new Dictionary<string, object>() {
                { "cost", null },
                { "currency", null },
            };
        }
        if (isTrue(isEqual(fees, null)))
        {
            fees = new List<object>() {};
        }
        return new List<object>() {fee, fees};
    }

    public virtual object parseFeeNumeric(object fee)
    {
        ((IDictionary<string,object>)fee)["cost"] = this.safeNumber(fee, "cost"); // ensure numeric
        if (isTrue(inOp(fee, "rate")))
        {
            ((IDictionary<string,object>)fee)["rate"] = this.safeNumber(fee, "rate");
        }
        return fee;
    }

    public virtual object findNearestCeiling(object arr, object providedValue)
    {
        //  i.e. findNearestCeiling ([ 10, 30, 50],  23) returns 30
        object length = getArrayLength(arr);
        for (object i = 0; isLessThan(i, length); postFixIncrement(ref i))
        {
            object current = getValue(arr, i);
            if (isTrue(isLessThanOrEqual(providedValue, current)))
            {
                return current;
            }
        }
        return getValue(arr, subtract(length, 1));
    }

    public virtual object invertFlatStringDictionary(object dict)
    {
        object reversed = new Dictionary<string, object>() {};
        object keys = new List<object>(((IDictionary<string,object>)dict).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
        {
            object key = getValue(keys, i);
            object value = getValue(dict, key);
            if (isTrue((value is string)))
            {
                ((IDictionary<string,object>)reversed)[(string)value] = key;
            }
        }
        return reversed;
    }

    public virtual object reduceFeesByCurrency(object fees)
    {
        //
        // this function takes a list of fee structures having the following format
        //
        //     string = true
        //
        //     [
        //         { 'currency': 'BTC', 'cost': '0.1' },
        //         { 'currency': 'BTC', 'cost': '0.2'  },
        //         { 'currency': 'BTC', 'cost': '0.2', 'rate': '0.00123' },
        //         { 'currency': 'BTC', 'cost': '0.4', 'rate': '0.00123' },
        //         { 'currency': 'BTC', 'cost': '0.5', 'rate': '0.00456' },
        //         { 'currency': 'USDT', 'cost': '12.3456' },
        //     ]
        //
        //     string = false
        //
        //     [
        //         { 'currency': 'BTC', 'cost': 0.1 },
        //         { 'currency': 'BTC', 'cost': 0.2 },
        //         { 'currency': 'BTC', 'cost': 0.2, 'rate': 0.00123 },
        //         { 'currency': 'BTC', 'cost': 0.4, 'rate': 0.00123 },
        //         { 'currency': 'BTC', 'cost': 0.5, 'rate': 0.00456 },
        //         { 'currency': 'USDT', 'cost': 12.3456 },
        //     ]
        //
        // and returns a reduced fee list, where fees are summed per currency and rate (if any)
        //
        //     string = true
        //
        //     [
        //         { 'currency': 'BTC', 'cost': '0.4'  },
        //         { 'currency': 'BTC', 'cost': '0.6', 'rate': '0.00123' },
        //         { 'currency': 'BTC', 'cost': '0.5', 'rate': '0.00456' },
        //         { 'currency': 'USDT', 'cost': '12.3456' },
        //     ]
        //
        //     string  = false
        //
        //     [
        //         { 'currency': 'BTC', 'cost': 0.3  },
        //         { 'currency': 'BTC', 'cost': 0.6, 'rate': 0.00123 },
        //         { 'currency': 'BTC', 'cost': 0.5, 'rate': 0.00456 },
        //         { 'currency': 'USDT', 'cost': 12.3456 },
        //     ]
        //
        object reduced = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(fees)); postFixIncrement(ref i))
        {
            object fee = getValue(fees, i);
            object code = this.safeString(fee, "currency");
            object feeCurrencyCode = ((bool) isTrue((!isEqual(code, null)))) ? code : ((object)i).ToString();
            if (isTrue(!isEqual(feeCurrencyCode, null)))
            {
                object rate = this.safeString(fee, "rate");
                object cost = this.safeString(fee, "cost");
                if (isTrue(isEqual(cost, null)))
                {
                    continue;
                }
                if (!isTrue((inOp(reduced, feeCurrencyCode))))
                {
                    ((IDictionary<string,object>)reduced)[(string)feeCurrencyCode] = new Dictionary<string, object>() {};
                }
                object rateKey = ((bool) isTrue((isEqual(rate, null)))) ? "" : rate;
                if (isTrue(inOp(getValue(reduced, feeCurrencyCode), rateKey)))
                {
                    ((IDictionary<string,object>)getValue(getValue(reduced, feeCurrencyCode), rateKey))["cost"] = Precise.stringAdd(getValue(getValue(getValue(reduced, feeCurrencyCode), rateKey), "cost"), cost);
                } else
                {
                    ((IDictionary<string,object>)getValue(reduced, feeCurrencyCode))[(string)rateKey] = new Dictionary<string, object>() {
                        { "currency", code },
                        { "cost", cost },
                    };
                    if (isTrue(!isEqual(rate, null)))
                    {
                        ((IDictionary<string,object>)getValue(getValue(reduced, feeCurrencyCode), rateKey))["rate"] = rate;
                    }
                }
            }
        }
        object result = new List<object>() {};
        object feeValues = new List<object>(((IDictionary<string,object>)reduced).Values);
        for (object i = 0; isLessThan(i, getArrayLength(feeValues)); postFixIncrement(ref i))
        {
            object reducedFeeValues = new List<object>(((IDictionary<string,object>)getValue(feeValues, i)).Values);
            result = this.arrayConcat(result, reducedFeeValues);
        }
        return result;
    }

    public virtual object safeTicker(object ticker, object market = null)
    {
        object open = this.omitZero(this.safeString(ticker, "open"));
        object close = this.omitZero(this.safeString2(ticker, "close", "last"));
        object change = this.omitZero(this.safeString(ticker, "change"));
        object percentage = this.omitZero(this.safeString(ticker, "percentage"));
        object average = this.omitZero(this.safeString(ticker, "average"));
        object vwap = this.safeString(ticker, "vwap");
        object baseVolume = this.safeString(ticker, "baseVolume");
        object quoteVolume = this.safeString(ticker, "quoteVolume");
        if (isTrue(isEqual(vwap, null)))
        {
            vwap = Precise.stringDiv(this.omitZero(quoteVolume), baseVolume);
        }
        // calculate open
        if (isTrue(!isEqual(change, null)))
        {
            if (isTrue(isTrue(isEqual(close, null)) && isTrue(!isEqual(average, null))))
            {
                close = Precise.stringAdd(average, Precise.stringDiv(change, "2"));
            }
            if (isTrue(isTrue(isEqual(open, null)) && isTrue(!isEqual(close, null))))
            {
                open = Precise.stringSub(close, change);
            }
        } else if (isTrue(!isEqual(percentage, null)))
        {
            if (isTrue(isTrue(isEqual(close, null)) && isTrue(!isEqual(average, null))))
            {
                object openAddClose = Precise.stringMul(average, "2");
                // openAddClose = open * (1 + (100 + percentage)/100)
                object denominator = Precise.stringAdd("2", Precise.stringDiv(percentage, "100"));
                object calcOpen = ((bool) isTrue((!isEqual(open, null)))) ? open : Precise.stringDiv(openAddClose, denominator);
                close = Precise.stringMul(calcOpen, Precise.stringAdd("1", Precise.stringDiv(percentage, "100")));
            }
            if (isTrue(isTrue(isEqual(open, null)) && isTrue(!isEqual(close, null))))
            {
                open = Precise.stringDiv(close, Precise.stringAdd("1", Precise.stringDiv(percentage, "100")));
            }
        }
        // change
        if (isTrue(isEqual(change, null)))
        {
            if (isTrue(isTrue(!isEqual(close, null)) && isTrue(!isEqual(open, null))))
            {
                change = Precise.stringSub(close, open);
            } else if (isTrue(isTrue(!isEqual(close, null)) && isTrue(!isEqual(percentage, null))))
            {
                change = Precise.stringMul(Precise.stringDiv(percentage, "100"), Precise.stringDiv(close, "100"));
            } else if (isTrue(isTrue(!isEqual(open, null)) && isTrue(!isEqual(percentage, null))))
            {
                change = Precise.stringMul(open, Precise.stringDiv(percentage, "100"));
            }
        }
        // calculate things according to "open" (similar can be done with "close")
        if (isTrue(!isEqual(open, null)))
        {
            // percentage (using change)
            if (isTrue(isTrue(isEqual(percentage, null)) && isTrue(!isEqual(change, null))))
            {
                percentage = Precise.stringMul(Precise.stringDiv(change, open), "100");
            }
            // close (using change)
            if (isTrue(isTrue(isEqual(close, null)) && isTrue(!isEqual(change, null))))
            {
                close = Precise.stringAdd(open, change);
            }
            // close (using average)
            if (isTrue(isTrue(isEqual(close, null)) && isTrue(!isEqual(average, null))))
            {
                close = Precise.stringMul(average, "2");
            }
            // average
            if (isTrue(isTrue(isEqual(average, null)) && isTrue(!isEqual(close, null))))
            {
                object precision = 18;
                if (isTrue(isTrue(!isEqual(market, null)) && isTrue(this.isTickPrecision())))
                {
                    object marketPrecision = this.safeDict(market, "precision");
                    object precisionPrice = this.safeString(marketPrecision, "price");
                    if (isTrue(!isEqual(precisionPrice, null)))
                    {
                        precision = this.precisionFromString(precisionPrice);
                    }
                }
                average = Precise.stringDiv(Precise.stringAdd(open, close), "2", precision);
            }
        }
        // timestamp and symbol operations don't belong in safeTicker
        // they should be done in the derived classes
        object closeParsed = this.parseNumber(this.omitZero(close));
        return this.extend(ticker, new Dictionary<string, object>() {
            { "bid", this.parseNumber(this.omitZero(this.safeString(ticker, "bid"))) },
            { "bidVolume", this.safeNumber(ticker, "bidVolume") },
            { "ask", this.parseNumber(this.omitZero(this.safeString(ticker, "ask"))) },
            { "askVolume", this.safeNumber(ticker, "askVolume") },
            { "high", this.parseNumber(this.omitZero(this.safeString(ticker, "high"))) },
            { "low", this.parseNumber(this.omitZero(this.safeString(ticker, "low"))) },
            { "open", this.parseNumber(this.omitZero(open)) },
            { "close", closeParsed },
            { "last", closeParsed },
            { "change", this.parseNumber(change) },
            { "percentage", this.parseNumber(percentage) },
            { "average", this.parseNumber(average) },
            { "vwap", this.parseNumber(vwap) },
            { "baseVolume", this.parseNumber(baseVolume) },
            { "quoteVolume", this.parseNumber(quoteVolume) },
            { "previousClose", this.safeNumber(ticker, "previousClose") },
            { "indexPrice", this.safeNumber(ticker, "indexPrice") },
            { "markPrice", this.safeNumber(ticker, "markPrice") },
        });
    }

    public async virtual Task<object> fetchBorrowRate(object code, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchBorrowRate is deprecated, please use fetchCrossBorrowRate or fetchIsolatedBorrowRate instead")) ;
    }

    public async virtual Task<object> repayCrossMargin(object code, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " repayCrossMargin is not support yet")) ;
    }

    public async virtual Task<object> repayIsolatedMargin(object symbol, object code, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " repayIsolatedMargin is not support yet")) ;
    }

    public async virtual Task<object> borrowCrossMargin(object code, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " borrowCrossMargin is not support yet")) ;
    }

    public async virtual Task<object> borrowIsolatedMargin(object symbol, object code, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " borrowIsolatedMargin is not support yet")) ;
    }

    public async virtual Task<object> borrowMargin(object code, object amount, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " borrowMargin is deprecated, please use borrowCrossMargin or borrowIsolatedMargin instead")) ;
    }

    public async virtual Task<object> repayMargin(object code, object amount, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " repayMargin is deprecated, please use repayCrossMargin or repayIsolatedMargin instead")) ;
    }

    public async virtual Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        object message = "";
        if (isTrue(getValue(this.has, "fetchTrades")))
        {
            message = ". If you want to build OHLCV candles from trade executions data, visit https://github.com/ccxt/ccxt/tree/master/examples/ and see \"build-ohlcv-bars\" file";
        }
        throw new NotSupported ((string)add(add(this.id, " fetchOHLCV() is not supported yet"), message)) ;
    }

    public async virtual Task<object> fetchOHLCVWs(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        object message = "";
        if (isTrue(getValue(this.has, "fetchTradesWs")))
        {
            message = ". If you want to build OHLCV candles from trade executions data, visit https://github.com/ccxt/ccxt/tree/master/examples/ and see \"build-ohlcv-bars\" file";
        }
        throw new NotSupported ((string)add(add(this.id, " fetchOHLCVWs() is not supported yet. Try using fetchOHLCV instead."), message)) ;
    }

    public async virtual Task<object> watchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchOHLCV() is not supported yet")) ;
    }

    public virtual object convertTradingViewToOHLCV(object ohlcvs, object timestamp = null, object open = null, object high = null, object low = null, object close = null, object volume = null, object ms = null)
    {
        timestamp ??= "t";
        open ??= "o";
        high ??= "h";
        low ??= "l";
        close ??= "c";
        volume ??= "v";
        ms ??= false;
        object result = new List<object>() {};
        object timestamps = this.safeList(ohlcvs, timestamp, new List<object>() {});
        object opens = this.safeList(ohlcvs, open, new List<object>() {});
        object highs = this.safeList(ohlcvs, high, new List<object>() {});
        object lows = this.safeList(ohlcvs, low, new List<object>() {});
        object closes = this.safeList(ohlcvs, close, new List<object>() {});
        object volumes = this.safeList(ohlcvs, volume, new List<object>() {});
        for (object i = 0; isLessThan(i, getArrayLength(timestamps)); postFixIncrement(ref i))
        {
            ((IList<object>)result).Add(new List<object>() {((bool) isTrue(ms)) ? this.safeInteger(timestamps, i) : this.safeTimestamp(timestamps, i), this.safeValue(opens, i), this.safeValue(highs, i), this.safeValue(lows, i), this.safeValue(closes, i), this.safeValue(volumes, i)});
        }
        return result;
    }

    public virtual object convertOHLCVToTradingView(object ohlcvs, object timestamp = null, object open = null, object high = null, object low = null, object close = null, object volume = null, object ms = null)
    {
        timestamp ??= "t";
        open ??= "o";
        high ??= "h";
        low ??= "l";
        close ??= "c";
        volume ??= "v";
        ms ??= false;
        object result = new Dictionary<string, object>() {};
        ((IDictionary<string,object>)result)[(string)timestamp] = new List<object>() {};
        ((IDictionary<string,object>)result)[(string)open] = new List<object>() {};
        ((IDictionary<string,object>)result)[(string)high] = new List<object>() {};
        ((IDictionary<string,object>)result)[(string)low] = new List<object>() {};
        ((IDictionary<string,object>)result)[(string)close] = new List<object>() {};
        ((IDictionary<string,object>)result)[(string)volume] = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(ohlcvs)); postFixIncrement(ref i))
        {
            object ts = ((bool) isTrue(ms)) ? getValue(getValue(ohlcvs, i), 0) : this.parseToInt(divide(getValue(getValue(ohlcvs, i), 0), 1000));
            object resultTimestamp = getValue(result, timestamp);
            ((IList<object>)resultTimestamp).Add(ts);
            object resultOpen = getValue(result, open);
            ((IList<object>)resultOpen).Add(getValue(getValue(ohlcvs, i), 1));
            object resultHigh = getValue(result, high);
            ((IList<object>)resultHigh).Add(getValue(getValue(ohlcvs, i), 2));
            object resultLow = getValue(result, low);
            ((IList<object>)resultLow).Add(getValue(getValue(ohlcvs, i), 3));
            object resultClose = getValue(result, close);
            ((IList<object>)resultClose).Add(getValue(getValue(ohlcvs, i), 4));
            object resultVolume = getValue(result, volume);
            ((IList<object>)resultVolume).Add(getValue(getValue(ohlcvs, i), 5));
        }
        return result;
    }

    public async virtual Task<object> fetchWebEndpoint(object method, object endpointMethod, object returnAsJson, object startRegex = null, object endRegex = null)
    {
        object errorMessage = "";
        object options = this.safeValue(this.options, method, new Dictionary<string, object>() {});
        object muteOnFailure = this.safeBool(options, "webApiMuteFailure", true);
        try
        {
            // if it was not explicitly disabled, then don't fetch
            if (isTrue(!isEqual(this.safeBool(options, "webApiEnable", true), true)))
            {
                return null;
            }
            object maxRetries = this.safeValue(options, "webApiRetries", 10);
            object response = null;
            object retry = 0;
            object shouldBreak = false;
            while (isLessThan(retry, maxRetries))
            {
                try
                {
                    response = await ((Task<object>)callDynamically(this, endpointMethod, new object[] { new Dictionary<string, object>() {} }));
                    shouldBreak = true;
                    break;
                } catch(Exception e)
                {
                    retry = add(retry, 1);
                    if (isTrue(isEqual(retry, maxRetries)))
                    {
                        throw e;
                    }
                }
                if (isTrue(shouldBreak))
                {
                    break; // this is needed because of GO
                }
            }
            object content = response;
            if (isTrue(!isEqual(startRegex, null)))
            {
                object splitted_by_start = ((string)content).Split(new [] {((string)startRegex)}, StringSplitOptions.None).ToList<object>();
                content = getValue(splitted_by_start, 1); // we need second part after start
            }
            if (isTrue(!isEqual(endRegex, null)))
            {
                object splitted_by_end = ((string)content).Split(new [] {((string)endRegex)}, StringSplitOptions.None).ToList<object>();
                content = getValue(splitted_by_end, 0); // we need first part after start
            }
            if (isTrue(isTrue(returnAsJson) && isTrue(((content is string)))))
            {
                object jsoned = this.parseJson(((string)content).Trim()); // content should be trimmed before json parsing
                if (isTrue(jsoned))
                {
                    return jsoned;  // if parsing was not successfull, exception should be thrown
                } else
                {
                    throw new BadResponse ((string)"could not parse the response into json") ;
                }
            } else
            {
                return content;
            }
        } catch(Exception e)
        {
            errorMessage = add(add(add(this.id, " "), method), "() failed to fetch correct data from website. Probably webpage markup has been changed, breaking the page custom parser.");
        }
        if (isTrue(muteOnFailure))
        {
            return null;
        } else
        {
            throw new BadResponse ((string)errorMessage) ;
        }
    }

    public virtual object marketIds(object symbols = null)
    {
        if (isTrue(isEqual(symbols, null)))
        {
            return symbols;
        }
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
        {
            ((IList<object>)result).Add(this.marketId(getValue(symbols, i)));
        }
        return result;
    }

    public virtual object currencyIds(object codes = null)
    {
        if (isTrue(isEqual(codes, null)))
        {
            return codes;
        }
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(codes)); postFixIncrement(ref i))
        {
            ((IList<object>)result).Add(this.currencyId(getValue(codes, i)));
        }
        return result;
    }

    public virtual object marketsForSymbols(object symbols = null)
    {
        if (isTrue(isEqual(symbols, null)))
        {
            return symbols;
        }
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
        {
            ((IList<object>)result).Add(this.market(getValue(symbols, i)));
        }
        return result;
    }

    public virtual object marketSymbols(object symbols = null, object type = null, object allowEmpty = null, object sameTypeOnly = null, object sameSubTypeOnly = null)
    {
        allowEmpty ??= true;
        sameTypeOnly ??= false;
        sameSubTypeOnly ??= false;
        if (isTrue(isEqual(symbols, null)))
        {
            if (!isTrue(allowEmpty))
            {
                throw new ArgumentsRequired ((string)add(this.id, " empty list of symbols is not supported")) ;
            }
            return symbols;
        }
        object symbolsLength = getArrayLength(symbols);
        if (isTrue(isEqual(symbolsLength, 0)))
        {
            if (!isTrue(allowEmpty))
            {
                throw new ArgumentsRequired ((string)add(this.id, " empty list of symbols is not supported")) ;
            }
            return symbols;
        }
        object result = new List<object>() {};
        object marketType = null;
        object isLinearSubType = null;
        for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
        {
            object market = this.market(getValue(symbols, i));
            if (isTrue(isTrue(sameTypeOnly) && isTrue((!isEqual(marketType, null)))))
            {
                if (isTrue(!isEqual(getValue(market, "type"), marketType)))
                {
                    throw new BadRequest ((string)add(add(add(add(add(this.id, " symbols must be of the same type, either "), marketType), " or "), getValue(market, "type")), ".")) ;
                }
            }
            if (isTrue(isTrue(sameSubTypeOnly) && isTrue((!isEqual(isLinearSubType, null)))))
            {
                if (isTrue(!isEqual(getValue(market, "linear"), isLinearSubType)))
                {
                    throw new BadRequest ((string)add(this.id, " symbols must be of the same subType, either linear or inverse.")) ;
                }
            }
            if (isTrue(isTrue(!isEqual(type, null)) && isTrue(!isEqual(getValue(market, "type"), type))))
            {
                throw new BadRequest ((string)add(add(add(this.id, " symbols must be of the same type "), type), ". If the type is incorrect you can change it in options or the params of the request")) ;
            }
            marketType = getValue(market, "type");
            if (!isTrue(getValue(market, "spot")))
            {
                isLinearSubType = getValue(market, "linear");
            }
            object symbol = this.safeString(market, "symbol", getValue(symbols, i));
            ((IList<object>)result).Add(symbol);
        }
        return result;
    }

    public virtual object marketCodes(object codes = null)
    {
        if (isTrue(isEqual(codes, null)))
        {
            return codes;
        }
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(codes)); postFixIncrement(ref i))
        {
            ((IList<object>)result).Add(this.commonCurrencyCode(getValue(codes, i)));
        }
        return result;
    }

    public virtual object parseBidsAsks(object bidasks, object priceKey = null, object amountKey = null, object countOrIdKey = null)
    {
        priceKey ??= 0;
        amountKey ??= 1;
        countOrIdKey ??= 2;
        bidasks = this.toArray(bidasks);
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(bidasks)); postFixIncrement(ref i))
        {
            ((IList<object>)result).Add(this.parseBidAsk(getValue(bidasks, i), priceKey, amountKey, countOrIdKey));
        }
        return result;
    }

    public async virtual Task<object> fetchL2OrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object orderbook = await this.fetchOrderBook(symbol, limit, parameters);
        return this.extend(orderbook, new Dictionary<string, object>() {
            { "asks", this.sortBy(this.aggregate(getValue(orderbook, "asks")), 0) },
            { "bids", this.sortBy(this.aggregate(getValue(orderbook, "bids")), 0, true) },
        });
    }

    public virtual object filterBySymbol(object objects, object symbol = null)
    {
        if (isTrue(isEqual(symbol, null)))
        {
            return objects;
        }
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(objects)); postFixIncrement(ref i))
        {
            object objectSymbol = this.safeString(getValue(objects, i), "symbol");
            if (isTrue(isEqual(objectSymbol, symbol)))
            {
                ((IList<object>)result).Add(getValue(objects, i));
            }
        }
        return result;
    }

    public virtual object parseOHLCV(object ohlcv, object market = null)
    {
        if (isTrue(((ohlcv is IList<object>) || (ohlcv.GetType().IsGenericType && ohlcv.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            return new List<object> {this.safeInteger(ohlcv, 0), this.safeNumber(ohlcv, 1), this.safeNumber(ohlcv, 2), this.safeNumber(ohlcv, 3), this.safeNumber(ohlcv, 4), this.safeNumber(ohlcv, 5)};
        }
        return ohlcv;
    }

    public virtual object networkCodeToId(object networkCode, object currencyCode = null)
    {
        /**
         * @ignore
         * @method
         * @name exchange#networkCodeToId
         * @description tries to convert the provided networkCode (which is expected to be an unified network code) to a network id. In order to achieve this, derived class needs to have 'options->networks' defined.
         * @param {string} networkCode unified network code
         * @param {string} currencyCode unified currency code, but this argument is not required by default, unless there is an exchange (like huobi) that needs an override of the method to be able to pass currencyCode argument additionally
         * @returns {string|undefined} exchange-specific network id
         */
        if (isTrue(isEqual(networkCode, null)))
        {
            return null;
        }
        object networkIdsByCodes = this.safeValue(this.options, "networks", new Dictionary<string, object>() {});
        object networkId = this.safeString(networkIdsByCodes, networkCode);
        // for example, if 'ETH' is passed for networkCode, but 'ETH' key not defined in `options->networks` object
        if (isTrue(isEqual(networkId, null)))
        {
            if (isTrue(isEqual(currencyCode, null)))
            {
                object currencies = new List<object>(((IDictionary<string,object>)this.currencies).Values);
                for (object i = 0; isLessThan(i, getArrayLength(currencies)); postFixIncrement(ref i))
                {
                    object currency = getValue(currencies, i);
                    object networks = this.safeDict(currency, "networks");
                    object network = this.safeDict(networks, networkCode);
                    networkId = this.safeString(network, "id");
                    if (isTrue(!isEqual(networkId, null)))
                    {
                        break;
                    }
                }
            } else
            {
                // if currencyCode was provided, then we try to find if that currencyCode has a replacement (i.e. ERC20 for ETH) or is in the currency
                object defaultNetworkCodeReplacements = this.safeValue(this.options, "defaultNetworkCodeReplacements", new Dictionary<string, object>() {});
                if (isTrue(inOp(defaultNetworkCodeReplacements, currencyCode)))
                {
                    // if there is a replacement for the passed networkCode, then we use it to find network-id in `options->networks` object
                    object replacementObject = getValue(defaultNetworkCodeReplacements, currencyCode); // i.e. { 'ERC20': 'ETH' }
                    object keys = new List<object>(((IDictionary<string,object>)replacementObject).Keys);
                    for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
                    {
                        object key = getValue(keys, i);
                        object value = getValue(replacementObject, key);
                        // if value matches to provided unified networkCode, then we use it's key to find network-id in `options->networks` object
                        if (isTrue(isEqual(value, networkCode)))
                        {
                            networkId = this.safeString(networkIdsByCodes, key);
                            break;
                        }
                    }
                } else
                {
                    // serach for network inside currency
                    object currency = this.safeDict(this.currencies, currencyCode);
                    object networks = this.safeDict(currency, "networks");
                    object network = this.safeDict(networks, networkCode);
                    networkId = this.safeString(network, "id");
                }
            }
            // if it wasn't found, we just set the provided value to network-id
            if (isTrue(isEqual(networkId, null)))
            {
                networkId = networkCode;
            }
        }
        return networkId;
    }

    public virtual object networkIdToCode(object networkId = null, object currencyCode = null)
    {
        /**
         * @ignore
         * @method
         * @name exchange#networkIdToCode
         * @description tries to convert the provided exchange-specific networkId to an unified network Code. In order to achieve this, derived class needs to have "options['networksById']" defined.
         * @param {string} networkId exchange specific network id/title, like: TRON, Trc-20, usdt-erc20, etc
         * @param {string|undefined} currencyCode unified currency code, but this argument is not required by default, unless there is an exchange (like huobi) that needs an override of the method to be able to pass currencyCode argument additionally
         * @returns {string|undefined} unified network code
         */
        if (isTrue(isEqual(networkId, null)))
        {
            return null;
        }
        object networkCodesByIds = this.safeDict(this.options, "networksById", new Dictionary<string, object>() {});
        object networkCode = this.safeString(networkCodesByIds, networkId, networkId);
        // replace mainnet network-codes (i.e. ERC20->ETH)
        if (isTrue(!isEqual(currencyCode, null)))
        {
            object defaultNetworkCodeReplacements = this.safeDict(this.options, "defaultNetworkCodeReplacements", new Dictionary<string, object>() {});
            if (isTrue(inOp(defaultNetworkCodeReplacements, currencyCode)))
            {
                object replacementObject = this.safeDict(defaultNetworkCodeReplacements, currencyCode, new Dictionary<string, object>() {});
                networkCode = this.safeString(replacementObject, networkCode, networkCode);
            }
        }
        return networkCode;
    }

    public virtual object handleNetworkCodeAndParams(object parameters)
    {
        object networkCodeInParams = this.safeString2(parameters, "networkCode", "network");
        if (isTrue(!isEqual(networkCodeInParams, null)))
        {
            parameters = this.omit(parameters, new List<object>() {"networkCode", "network"});
        }
        // if it was not defined by user, we should not set it from 'defaultNetworks', because handleNetworkCodeAndParams is for only request-side and thus we do not fill it with anything. We can only use 'defaultNetworks' after parsing response-side
        return new List<object>() {networkCodeInParams, parameters};
    }

    public virtual object defaultNetworkCode(object currencyCode)
    {
        object defaultNetworkCode = null;
        object defaultNetworks = this.safeDict(this.options, "defaultNetworks", new Dictionary<string, object>() {});
        if (isTrue(inOp(defaultNetworks, currencyCode)))
        {
            // if currency had set its network in "defaultNetworks", use it
            defaultNetworkCode = getValue(defaultNetworks, currencyCode);
        } else
        {
            // otherwise, try to use the global-scope 'defaultNetwork' value (even if that network is not supported by currency, it doesn't make any problem, this will be just used "at first" if currency supports this network at all)
            object defaultNetwork = this.safeString(this.options, "defaultNetwork");
            if (isTrue(!isEqual(defaultNetwork, null)))
            {
                defaultNetworkCode = defaultNetwork;
            }
        }
        return defaultNetworkCode;
    }

    public virtual object selectNetworkCodeFromUnifiedNetworks(object currencyCode, object networkCode, object indexedNetworkEntries)
    {
        return this.selectNetworkKeyFromNetworks(currencyCode, networkCode, indexedNetworkEntries, true);
    }

    public virtual object selectNetworkIdFromRawNetworks(object currencyCode, object networkCode, object indexedNetworkEntries)
    {
        return this.selectNetworkKeyFromNetworks(currencyCode, networkCode, indexedNetworkEntries, false);
    }

    public virtual object selectNetworkKeyFromNetworks(object currencyCode, object networkCode, object indexedNetworkEntries, object isIndexedByUnifiedNetworkCode = null)
    {
        // this method is used against raw & unparse network entries, which are just indexed by network id
        isIndexedByUnifiedNetworkCode ??= false;
        object chosenNetworkId = null;
        object availableNetworkIds = new List<object>(((IDictionary<string,object>)indexedNetworkEntries).Keys);
        object responseNetworksLength = getArrayLength(availableNetworkIds);
        if (isTrue(!isEqual(networkCode, null)))
        {
            if (isTrue(isEqual(responseNetworksLength, 0)))
            {
                throw new NotSupported ((string)add(add(add(add(this.id, " - "), networkCode), " network did not return any result for "), currencyCode)) ;
            } else
            {
                // if networkCode was provided by user, we should check it after response, as the referenced exchange doesn't support network-code during request
                object networkIdOrCode = ((bool) isTrue(isIndexedByUnifiedNetworkCode)) ? networkCode : this.networkCodeToId(networkCode, currencyCode);
                if (isTrue(inOp(indexedNetworkEntries, networkIdOrCode)))
                {
                    chosenNetworkId = networkIdOrCode;
                } else
                {
                    throw new NotSupported ((string)add(add(add(add(add(add(this.id, " - "), networkIdOrCode), " network was not found for "), currencyCode), ", use one of "), String.Join(", ", ((IList<object>)availableNetworkIds).ToArray()))) ;
                }
            }
        } else
        {
            if (isTrue(isEqual(responseNetworksLength, 0)))
            {
                throw new NotSupported ((string)add(add(this.id, " - no networks were returned for "), currencyCode)) ;
            } else
            {
                // if networkCode was not provided by user, then we try to use the default network (if it was defined in "defaultNetworks"), otherwise, we just return the first network entry
                object defaultNetworkCode = this.defaultNetworkCode(currencyCode);
                object defaultNetworkId = ((bool) isTrue(isIndexedByUnifiedNetworkCode)) ? defaultNetworkCode : this.networkCodeToId(defaultNetworkCode, currencyCode);
                if (isTrue(inOp(indexedNetworkEntries, defaultNetworkId)))
                {
                    return defaultNetworkId;
                }
                throw new NotSupported ((string)add(add(this.id, " - can not determine the default network, please pass param[\"network\"] one from : "), String.Join(", ", ((IList<object>)availableNetworkIds).ToArray()))) ;
            }
        }
        return chosenNetworkId;
    }

    public virtual object safeNumber2(object dictionary, object key1, object key2, object d = null)
    {
        object value = this.safeString2(dictionary, key1, key2);
        return this.parseNumber(value, d);
    }

    public virtual object parseOrderBook(object orderbook, object symbol, object timestamp = null, object bidsKey = null, object asksKey = null, object priceKey = null, object amountKey = null, object countOrIdKey = null)
    {
        bidsKey ??= "bids";
        asksKey ??= "asks";
        priceKey ??= 0;
        amountKey ??= 1;
        countOrIdKey ??= 2;
        object bids = this.parseBidsAsks(this.safeValue(orderbook, bidsKey, new List<object>() {}), priceKey, amountKey, countOrIdKey);
        object asks = this.parseBidsAsks(this.safeValue(orderbook, asksKey, new List<object>() {}), priceKey, amountKey, countOrIdKey);
        return ((object)new Dictionary<string, object>() {
            { "symbol", symbol },
            { "bids", this.sortBy(bids, 0, true) },
            { "asks", this.sortBy(asks, 0) },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "nonce", null },
        });
    }

    public virtual object parseOHLCVs(object ohlcvs, object market = null, object timeframe = null, object since = null, object limit = null, object tail = null)
    {
        timeframe ??= "1m";
        tail ??= false;
        object results = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(ohlcvs)); postFixIncrement(ref i))
        {
            ((IList<object>)results).Add(this.parseOHLCV(getValue(ohlcvs, i), market));
        }
        object sorted = this.sortBy(results, 0);
        return ((object)this.filterBySinceLimit(sorted, since, limit, 0, tail));
    }

    public virtual object parseLeverageTiers(object response, object symbols = null, object marketIdKey = null)
    {
        // marketIdKey should only be undefined when response is a dictionary.
        symbols = this.marketSymbols(symbols);
        object tiers = new Dictionary<string, object>() {};
        object symbolsLength = 0;
        if (isTrue(!isEqual(symbols, null)))
        {
            symbolsLength = getArrayLength(symbols);
        }
        object noSymbols = isTrue((isEqual(symbols, null))) || isTrue((isEqual(symbolsLength, 0)));
        if (isTrue(((response is IList<object>) || (response.GetType().IsGenericType && response.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
            {
                object item = getValue(response, i);
                object id = this.safeString(item, marketIdKey);
                object market = this.safeMarket(id, null, null, "swap");
                object symbol = getValue(market, "symbol");
                object contract = this.safeBool(market, "contract", false);
                if (isTrue(isTrue(contract) && isTrue((isTrue(noSymbols) || isTrue(this.inArray(symbol, symbols))))))
                {
                    ((IDictionary<string,object>)tiers)[(string)symbol] = this.parseMarketLeverageTiers(item, market);
                }
            }
        } else
        {
            object keys = new List<object>(((IDictionary<string,object>)response).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
            {
                object marketId = getValue(keys, i);
                object item = getValue(response, marketId);
                object market = this.safeMarket(marketId, null, null, "swap");
                object symbol = getValue(market, "symbol");
                object contract = this.safeBool(market, "contract", false);
                if (isTrue(isTrue(contract) && isTrue((isTrue(noSymbols) || isTrue(this.inArray(symbol, symbols))))))
                {
                    ((IDictionary<string,object>)tiers)[(string)symbol] = this.parseMarketLeverageTiers(item, market);
                }
            }
        }
        return tiers;
    }

    public async virtual Task<object> loadTradingLimits(object symbols = null, object reload = null, object parameters = null)
    {
        reload ??= false;
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchTradingLimits")))
        {
            if (isTrue(isTrue(reload) || !isTrue((inOp(this.options, "limitsLoaded")))))
            {
                object response = await this.fetchTradingLimits(symbols);
                for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
                {
                    object symbol = getValue(symbols, i);
                    ((IDictionary<string,object>)this.markets)[(string)symbol] = this.deepExtend(getValue(this.markets, symbol), getValue(response, symbol));
                }
                ((IDictionary<string,object>)this.options)["limitsLoaded"] = this.milliseconds();
            }
        }
        return this.markets;
    }

    public virtual object safePosition(object position)
    {
        // simplified version of: /pull/12765/
        object unrealizedPnlString = this.safeString(position, "unrealisedPnl");
        object initialMarginString = this.safeString(position, "initialMargin");
        //
        // PERCENTAGE
        //
        object percentage = this.safeValue(position, "percentage");
        if (isTrue(isTrue(isTrue((isEqual(percentage, null))) && isTrue((!isEqual(unrealizedPnlString, null)))) && isTrue((!isEqual(initialMarginString, null)))))
        {
            // as it was done in all implementations ( aax, btcex, bybit, deribit, ftx, gate, kucoinfutures, phemex )
            object percentageString = Precise.stringMul(Precise.stringDiv(unrealizedPnlString, initialMarginString, 4), "100");
            ((IDictionary<string,object>)position)["percentage"] = this.parseNumber(percentageString);
        }
        // if contractSize is undefined get from market
        object contractSize = this.safeNumber(position, "contractSize");
        object symbol = this.safeString(position, "symbol");
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.safeValue(this.markets, symbol);
        }
        if (isTrue(isTrue(isEqual(contractSize, null)) && isTrue(!isEqual(market, null))))
        {
            contractSize = this.safeNumber(market, "contractSize");
            ((IDictionary<string,object>)position)["contractSize"] = contractSize;
        }
        return position;
    }

    public virtual object parsePositions(object positions, object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        symbols = this.marketSymbols(symbols);
        positions = this.toArray(positions);
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(positions)); postFixIncrement(ref i))
        {
            object position = this.extend(this.parsePosition(getValue(positions, i), null), parameters);
            ((IList<object>)result).Add(position);
        }
        return this.filterByArrayPositions(result, "symbol", symbols, false);
    }

    public virtual object parseAccounts(object accounts, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        accounts = this.toArray(accounts);
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(accounts)); postFixIncrement(ref i))
        {
            object account = this.extend(this.parseAccount(getValue(accounts, i)), parameters);
            ((IList<object>)result).Add(account);
        }
        return result;
    }

    public virtual object parseTrades(object trades, object market = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        trades = this.toArray(trades);
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(trades)); postFixIncrement(ref i))
        {
            object trade = this.extend(this.parseTrade(getValue(trades, i), market), parameters);
            ((IList<object>)result).Add(trade);
        }
        result = this.sortBy2(result, "timestamp", "id");
        object symbol = ((bool) isTrue((!isEqual(market, null)))) ? getValue(market, "symbol") : null;
        return this.filterBySymbolSinceLimit(result, symbol, since, limit);
    }

    public virtual object parseTransactions(object transactions, object currency = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        transactions = this.toArray(transactions);
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(transactions)); postFixIncrement(ref i))
        {
            object transaction = this.extend(this.parseTransaction(getValue(transactions, i), currency), parameters);
            ((IList<object>)result).Add(transaction);
        }
        result = this.sortBy(result, "timestamp");
        object code = ((bool) isTrue((!isEqual(currency, null)))) ? getValue(currency, "code") : null;
        return this.filterByCurrencySinceLimit(result, code, since, limit);
    }

    public virtual object parseTransfers(object transfers, object currency = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        transfers = this.toArray(transfers);
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(transfers)); postFixIncrement(ref i))
        {
            object transfer = this.extend(this.parseTransfer(getValue(transfers, i), currency), parameters);
            ((IList<object>)result).Add(transfer);
        }
        result = this.sortBy(result, "timestamp");
        object code = ((bool) isTrue((!isEqual(currency, null)))) ? getValue(currency, "code") : null;
        return this.filterByCurrencySinceLimit(result, code, since, limit);
    }

    public virtual object parseLedger(object data, object currency = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object result = new List<object>() {};
        object arrayData = this.toArray(data);
        for (object i = 0; isLessThan(i, getArrayLength(arrayData)); postFixIncrement(ref i))
        {
            object itemOrItems = this.parseLedgerEntry(getValue(arrayData, i), currency);
            if (isTrue(((itemOrItems is IList<object>) || (itemOrItems.GetType().IsGenericType && itemOrItems.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
            {
                for (object j = 0; isLessThan(j, getArrayLength(itemOrItems)); postFixIncrement(ref j))
                {
                    ((IList<object>)result).Add(this.extend(getValue(itemOrItems, j), parameters));
                }
            } else
            {
                ((IList<object>)result).Add(this.extend(itemOrItems, parameters));
            }
        }
        result = this.sortBy(result, "timestamp");
        object code = ((bool) isTrue((!isEqual(currency, null)))) ? getValue(currency, "code") : null;
        return this.filterByCurrencySinceLimit(result, code, since, limit);
    }

    public virtual object nonce()
    {
        return this.seconds();
    }

    public virtual object setHeaders(object headers)
    {
        return headers;
    }

    public virtual object currencyId(object code)
    {
        object currency = this.safeDict(this.currencies, code);
        if (isTrue(isEqual(currency, null)))
        {
            currency = this.safeCurrency(code);
        }
        if (isTrue(!isEqual(currency, null)))
        {
            return getValue(currency, "id");
        }
        return code;
    }

    public virtual object marketId(object symbol)
    {
        object market = this.market(symbol);
        if (isTrue(!isEqual(market, null)))
        {
            return getValue(market, "id");
        }
        return symbol;
    }

    public virtual object symbol(object symbol)
    {
        object market = this.market(symbol);
        return this.safeString(market, "symbol", symbol);
    }

    public virtual object handleParamString(object parameters, object paramName, object defaultValue = null)
    {
        object value = this.safeString(parameters, paramName, defaultValue);
        if (isTrue(!isEqual(value, null)))
        {
            parameters = this.omit(parameters, paramName);
        }
        return new List<object>() {value, parameters};
    }

    public virtual object handleParamString2(object parameters, object paramName1, object paramName2, object defaultValue = null)
    {
        object value = this.safeString2(parameters, paramName1, paramName2, defaultValue);
        if (isTrue(!isEqual(value, null)))
        {
            parameters = this.omit(parameters, new List<object>() {paramName1, paramName2});
        }
        return new List<object>() {value, parameters};
    }

    public virtual object handleParamInteger(object parameters, object paramName, object defaultValue = null)
    {
        object value = this.safeInteger(parameters, paramName, defaultValue);
        if (isTrue(!isEqual(value, null)))
        {
            parameters = this.omit(parameters, paramName);
        }
        return new List<object>() {value, parameters};
    }

    public virtual object handleParamInteger2(object parameters, object paramName1, object paramName2, object defaultValue = null)
    {
        object value = this.safeInteger2(parameters, paramName1, paramName2, defaultValue);
        if (isTrue(!isEqual(value, null)))
        {
            parameters = this.omit(parameters, new List<object>() {paramName1, paramName2});
        }
        return new List<object>() {value, parameters};
    }

    public virtual object handleParamBool(object parameters, object paramName, object defaultValue = null)
    {
        object value = this.safeBool(parameters, paramName, defaultValue);
        if (isTrue(!isEqual(value, null)))
        {
            parameters = this.omit(parameters, paramName);
        }
        return new List<object>() {value, parameters};
    }

    public virtual object handleParamBool2(object parameters, object paramName1, object paramName2, object defaultValue = null)
    {
        object value = this.safeBool2(parameters, paramName1, paramName2, defaultValue);
        if (isTrue(!isEqual(value, null)))
        {
            parameters = this.omit(parameters, new List<object>() {paramName1, paramName2});
        }
        return new List<object>() {value, parameters};
    }

    /**
     * @param {object} params - extra parameters
     * @param {object} request - existing dictionary of request
     * @param {string} exchangeSpecificKey - the key for chain id to be set in request
     * @param {object} currencyCode - (optional) existing dictionary of request
     * @param {boolean} isRequired - (optional) whether that param is required to be present
     * @returns {object[]} - returns [request, params] where request is the modified request object and params is the modified params object
     */
    public virtual object handleRequestNetwork(object parameters, object request, object exchangeSpecificKey, object currencyCode = null, object isRequired = null)
    {
        isRequired ??= false;
        object networkCode = null;
        var networkCodeparametersVariable = this.handleNetworkCodeAndParams(parameters);
        networkCode = ((IList<object>)networkCodeparametersVariable)[0];
        parameters = ((IList<object>)networkCodeparametersVariable)[1];
        if (isTrue(!isEqual(networkCode, null)))
        {
            ((IDictionary<string,object>)request)[(string)exchangeSpecificKey] = this.networkCodeToId(networkCode, currencyCode);
        } else if (isTrue(isRequired))
        {
            throw new ArgumentsRequired ((string)add(this.id, " - \"network\" param is required for this request")) ;
        }
        return new List<object>() {request, parameters};
    }

    public virtual object resolvePath(object path, object parameters)
    {
        return new List<object> {this.implodeParams(path, parameters), this.omit(parameters, this.extractParams(path))};
    }

    public virtual object getListFromObjectValues(object objects, object key)
    {
        object newArray = objects;
        if (!isTrue(((objects is IList<object>) || (objects.GetType().IsGenericType && objects.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            newArray = this.toArray(objects);
        }
        object results = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(newArray)); postFixIncrement(ref i))
        {
            ((IList<object>)results).Add(getValue(getValue(newArray, i), key));
        }
        return results;
    }

    public virtual object getSymbolsForMarketType(object marketType = null, object subType = null, object symbolWithActiveStatus = null, object symbolWithUnknownStatus = null)
    {
        symbolWithActiveStatus ??= true;
        symbolWithUnknownStatus ??= true;
        object filteredMarkets = this.markets;
        if (isTrue(!isEqual(marketType, null)))
        {
            filteredMarkets = this.filterBy(filteredMarkets, "type", marketType);
        }
        if (isTrue(!isEqual(subType, null)))
        {
            this.checkRequiredArgument("getSymbolsForMarketType", subType, "subType", new List<object>() {"linear", "inverse", "quanto"});
            filteredMarkets = this.filterBy(filteredMarkets, "subType", subType);
        }
        object activeStatuses = new List<object>() {};
        if (isTrue(symbolWithActiveStatus))
        {
            ((IList<object>)activeStatuses).Add(true);
        }
        if (isTrue(symbolWithUnknownStatus))
        {
            ((IList<object>)activeStatuses).Add(null);
        }
        filteredMarkets = this.filterByArray(filteredMarkets, "active", activeStatuses, false);
        return this.getListFromObjectValues(filteredMarkets, "symbol");
    }

    public virtual object filterByArray(object objects, object key, object values = null, object indexed = null)
    {
        indexed ??= true;
        objects = this.toArray(objects);
        // return all of them if no values were passed
        if (isTrue(isTrue(isEqual(values, null)) || !isTrue(values)))
        {
            // return indexed ? this.indexBy (objects, key) : objects;
            if (isTrue(indexed))
            {
                return this.indexBy(objects, key);
            } else
            {
                return objects;
            }
        }
        object results = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(objects)); postFixIncrement(ref i))
        {
            if (isTrue(this.inArray(getValue(getValue(objects, i), key), values)))
            {
                ((IList<object>)results).Add(getValue(objects, i));
            }
        }
        // return indexed ? this.indexBy (results, key) : results;
        if (isTrue(indexed))
        {
            return this.indexBy(results, key);
        }
        return results;
    }

    public async virtual Task<object> fetch2(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null, object config = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        config ??= new Dictionary<string, object>();
        if (isTrue(this.enableRateLimit))
        {
            object cost = this.calculateRateLimiterCost(api, method, path, parameters, config);
            await this.throttle(cost);
        }
        object retries = null;
        var retriesparametersVariable = this.handleOptionAndParams(parameters, path, "maxRetriesOnFailure", 0);
        retries = ((IList<object>)retriesparametersVariable)[0];
        parameters = ((IList<object>)retriesparametersVariable)[1];
        object retryDelay = null;
        var retryDelayparametersVariable = this.handleOptionAndParams(parameters, path, "maxRetriesOnFailureDelay", 0);
        retryDelay = ((IList<object>)retryDelayparametersVariable)[0];
        parameters = ((IList<object>)retryDelayparametersVariable)[1];
        this.lastRestRequestTimestamp = this.milliseconds();
        object request = this.sign(path, api, method, parameters, headers, body);
        this.last_request_headers = getValue(request, "headers");
        this.last_request_body = getValue(request, "body");
        this.last_request_url = getValue(request, "url");
        for (object i = 0; isLessThan(i, add(retries, 1)); postFixIncrement(ref i))
        {
            try
            {
                return await this.fetch(getValue(request, "url"), getValue(request, "method"), getValue(request, "headers"), getValue(request, "body"));
            } catch(Exception e)
            {
                if (isTrue(e is OperationFailed))
                {
                    if (isTrue(isLessThan(i, retries)))
                    {
                        if (isTrue(this.verbose))
                        {
                            this.log(add(add(add(add(add(add("Request failed with the error: ", ((object)e).ToString()), ", retrying "), ((object)(add(i, 1))).ToString()), " of "), ((object)retries).ToString()), "..."));
                        }
                        if (isTrue(isTrue((!isEqual(retryDelay, null))) && isTrue((!isEqual(retryDelay, 0)))))
                        {
                            await this.sleep(retryDelay);
                        }
                    } else
                    {
                        throw e;
                    }
                } else
                {
                    throw e;
                }
            }
        }
        return null;  // this line is never reached, but exists for c# value return requirement
    }

    public async virtual Task<object> request(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null, object config = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        config ??= new Dictionary<string, object>();
        return await this.fetch2(path, api, method, parameters, headers, body, config);
    }

    public async virtual Task<object> loadAccounts(object reload = null, object parameters = null)
    {
        reload ??= false;
        parameters ??= new Dictionary<string, object>();
        if (isTrue(reload))
        {
            this.accounts = await this.fetchAccounts(parameters);
        } else
        {
            if (isTrue(this.accounts))
            {
                return this.accounts;
            } else
            {
                this.accounts = await this.fetchAccounts(parameters);
            }
        }
        this.accountsById = ((object)this.indexBy(this.accounts, "id"));
        return this.accounts;
    }

    public virtual object buildOHLCVC(object trades, object timeframe = null, object since = null, object limit = null)
    {
        // given a sorted arrays of trades (recent last) and a timeframe builds an array of OHLCV candles
        // note, default limit value (**********) is max int32 value
        timeframe ??= "1m";
        since ??= 0;
        limit ??= **********;
        object ms = multiply(this.parseTimeframe(timeframe), 1000);
        object ohlcvs = new List<object>() {};
        object i_timestamp = 0;
        // const open = 1;
        object i_high = 2;
        object i_low = 3;
        object i_close = 4;
        object i_volume = 5;
        object i_count = 6;
        object tradesLength = getArrayLength(trades);
        object oldest = mathMin(tradesLength, limit);
        for (object i = 0; isLessThan(i, oldest); postFixIncrement(ref i))
        {
            object trade = getValue(trades, i);
            object ts = getValue(trade, "timestamp");
            if (isTrue(isLessThan(ts, since)))
            {
                continue;
            }
            object openingTime = multiply((Math.Floor(Double.Parse((divide(ts, ms)).ToString()))), ms); // shift to the edge of m/h/d (but not M)
            if (isTrue(isLessThan(openingTime, since)))
            {
                continue;
            }
            object ohlcv_length = getArrayLength(ohlcvs);
            object candle = subtract(ohlcv_length, 1);
            if (isTrue(isTrue((isEqual(candle, -1))) || isTrue((isGreaterThanOrEqual(openingTime, this.sum(getValue(getValue(ohlcvs, candle), i_timestamp), ms))))))
            {
                // moved to a new timeframe -> create a new candle from opening trade
                ((IList<object>)ohlcvs).Add(new List<object>() {openingTime, getValue(trade, "price"), getValue(trade, "price"), getValue(trade, "price"), getValue(trade, "price"), getValue(trade, "amount"), 1});
            } else
            {
                // still processing the same timeframe -> update opening trade
                ((List<object>)getValue(ohlcvs, candle))[Convert.ToInt32(i_high)] = mathMax(getValue(getValue(ohlcvs, candle), i_high), getValue(trade, "price"));
                ((List<object>)getValue(ohlcvs, candle))[Convert.ToInt32(i_low)] = mathMin(getValue(getValue(ohlcvs, candle), i_low), getValue(trade, "price"));
                ((List<object>)getValue(ohlcvs, candle))[Convert.ToInt32(i_close)] = getValue(trade, "price");
                ((List<object>)getValue(ohlcvs, candle))[Convert.ToInt32(i_volume)] = this.sum(getValue(getValue(ohlcvs, candle), i_volume), getValue(trade, "amount"));
                ((List<object>)getValue(ohlcvs, candle))[Convert.ToInt32(i_count)] = this.sum(getValue(getValue(ohlcvs, candle), i_count), 1);
            }
        }
        return ohlcvs;
    }

    public virtual object parseTradingViewOHLCV(object ohlcvs, object market = null, object timeframe = null, object since = null, object limit = null)
    {
        timeframe ??= "1m";
        object result = this.convertTradingViewToOHLCV(ohlcvs);
        return this.parseOHLCVs(result, market, timeframe, since, limit);
    }

    public async virtual Task<object> editLimitBuyOrder(object id, object symbol, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.editLimitOrder(id, symbol, "buy", amount, price, parameters);
    }

    public async virtual Task<object> editLimitSellOrder(object id, object symbol, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.editLimitOrder(id, symbol, "sell", amount, price, parameters);
    }

    public async virtual Task<object> editLimitOrder(object id, object symbol, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.editOrder(id, symbol, "limit", side, amount, price, parameters);
    }

    public async virtual Task<object> editOrder(object id, object symbol, object type, object side, object amount = null, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.cancelOrder(id, symbol);
        return await this.createOrder(symbol, type, side, amount, price, parameters);
    }

    public async virtual Task<object> editOrderWs(object id, object symbol, object type, object side, object amount = null, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.cancelOrderWs(id, symbol);
        return await this.createOrderWs(symbol, type, side, amount, price, parameters);
    }

    public async virtual Task<object> fetchPosition(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPosition() is not supported yet")) ;
    }

    public async virtual Task<object> fetchPositionWs(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPositionWs() is not supported yet")) ;
    }

    public async virtual Task<object> watchPosition(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchPosition() is not supported yet")) ;
    }

    public async virtual Task<object> watchPositions(object symbols = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchPositions() is not supported yet")) ;
    }

    public async virtual Task<object> watchPositionForSymbols(object symbols = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.watchPositions(symbols, since, limit, parameters);
    }

    public async virtual Task<object> fetchPositionsForSymbol(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPositionsForSymbol() is not supported yet")) ;
    }

    public async virtual Task<object> fetchPositionsForSymbolWs(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPositionsForSymbol() is not supported yet")) ;
    }

    public async virtual Task<object> fetchPositions(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPositions() is not supported yet")) ;
    }

    public async virtual Task<object> fetchPositionsWs(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPositions() is not supported yet")) ;
    }

    public async virtual Task<object> fetchPositionsRisk(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPositionsRisk() is not supported yet")) ;
    }

    public async virtual Task<object> fetchBidsAsks(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchBidsAsks() is not supported yet")) ;
    }

    public async virtual Task<object> fetchBorrowInterest(object code = null, object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchBorrowInterest() is not supported yet")) ;
    }

    public async virtual Task<object> fetchLedger(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchLedger() is not supported yet")) ;
    }

    public async virtual Task<object> fetchLedgerEntry(object id, object code = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchLedgerEntry() is not supported yet")) ;
    }

    public virtual object parseBidAsk(object bidask, object priceKey = null, object amountKey = null, object countOrIdKey = null)
    {
        priceKey ??= 0;
        amountKey ??= 1;
        countOrIdKey ??= 2;
        object price = this.safeNumber(bidask, priceKey);
        object amount = this.safeNumber(bidask, amountKey);
        object countOrId = this.safeInteger(bidask, countOrIdKey);
        object bidAsk = new List<object>() {price, amount};
        if (isTrue(!isEqual(countOrId, null)))
        {
            ((IList<object>)bidAsk).Add(countOrId);
        }
        return bidAsk;
    }

    public virtual object safeCurrency(object currencyId, object currency = null)
    {
        if (isTrue(isTrue((isEqual(currencyId, null))) && isTrue((!isEqual(currency, null)))))
        {
            return currency;
        }
        if (isTrue(isTrue(isTrue((!isEqual(this.currencies_by_id, null))) && isTrue((inOp(this.currencies_by_id, currencyId)))) && isTrue((!isEqual(getValue(this.currencies_by_id, currencyId), null)))))
        {
            return getValue(this.currencies_by_id, currencyId);
        }
        object code = currencyId;
        if (isTrue(!isEqual(currencyId, null)))
        {
            code = this.commonCurrencyCode(((string)currencyId).ToUpper());
        }
        return this.safeCurrencyStructure(new Dictionary<string, object>() {
            { "id", currencyId },
            { "code", code },
            { "precision", null },
        });
    }

    public virtual object safeMarket(object marketId = null, object market = null, object delimiter = null, object marketType = null)
    {
        object result = this.safeMarketStructure(new Dictionary<string, object>() {
            { "symbol", marketId },
            { "marketId", marketId },
        });
        if (isTrue(!isEqual(marketId, null)))
        {
            if (isTrue(isTrue((!isEqual(this.markets_by_id, null))) && isTrue((inOp(this.markets_by_id, marketId)))))
            {
                object markets = getValue(this.markets_by_id, marketId);
                object numMarkets = getArrayLength(markets);
                if (isTrue(isEqual(numMarkets, 1)))
                {
                    return getValue(markets, 0);
                } else
                {
                    if (isTrue(isEqual(marketType, null)))
                    {
                        if (isTrue(isEqual(market, null)))
                        {
                            throw new ArgumentsRequired ((string)add(add(add(this.id, " safeMarket() requires a fourth argument for "), marketId), " to disambiguate between different markets with the same market id")) ;
                        } else
                        {
                            marketType = getValue(market, "type");
                        }
                    }
                    for (object i = 0; isLessThan(i, getArrayLength(markets)); postFixIncrement(ref i))
                    {
                        object currentMarket = getValue(markets, i);
                        if (isTrue(getValue(currentMarket, marketType)))
                        {
                            return currentMarket;
                        }
                    }
                }
            } else if (isTrue(isTrue(!isEqual(delimiter, null)) && isTrue(!isEqual(delimiter, ""))))
            {
                object parts = ((string)marketId).Split(new [] {((string)delimiter)}, StringSplitOptions.None).ToList<object>();
                object partsLength = getArrayLength(parts);
                if (isTrue(isEqual(partsLength, 2)))
                {
                    ((IDictionary<string,object>)result)["baseId"] = this.safeString(parts, 0);
                    ((IDictionary<string,object>)result)["quoteId"] = this.safeString(parts, 1);
                    ((IDictionary<string,object>)result)["base"] = this.safeCurrencyCode(getValue(result, "baseId"));
                    ((IDictionary<string,object>)result)["quote"] = this.safeCurrencyCode(getValue(result, "quoteId"));
                    ((IDictionary<string,object>)result)["symbol"] = add(add(getValue(result, "base"), "/"), getValue(result, "quote"));
                    return result;
                } else
                {
                    return result;
                }
            }
        }
        if (isTrue(!isEqual(market, null)))
        {
            return market;
        }
        return result;
    }

    public virtual object checkRequiredCredentials(object error = null)
    {
        /**
        * @ignore
        * @method
        * @param {boolean} error throw an error that a credential is required if true
        * @returns {boolean} true if all required credentials have been set, otherwise false or an error is thrown is param error=true
        */
        error ??= true;
        object keys = new List<object>(((IDictionary<string,object>)this.requiredCredentials).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
        {
            object key = getValue(keys, i);
            if (isTrue(isTrue(getValue(this.requiredCredentials, key)) && !isTrue(getValue(this, key))))
            {
                if (isTrue(error))
                {
                    throw new AuthenticationError ((string)add(add(add(this.id, " requires \""), key), "\" credential")) ;
                } else
                {
                    return false;
                }
            }
        }
        return true;
    }

    public virtual object oath()
    {
        if (isTrue(!isEqual(this.twofa, null)))
        {
            return totp(this.twofa);
        } else
        {
            throw new ExchangeError ((string)add(this.id, " exchange.twofa has not been set for 2FA Two-Factor Authentication")) ;
        }
    }

    public async virtual Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchBalance() is not supported yet")) ;
    }

    public async virtual Task<object> fetchBalanceWs(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchBalanceWs() is not supported yet")) ;
    }

    public virtual object parseBalance(object response)
    {
        throw new NotSupported ((string)add(this.id, " parseBalance() is not supported yet")) ;
    }

    public async virtual Task<object> watchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchBalance() is not supported yet")) ;
    }

    public async virtual Task<object> fetchPartialBalance(object part, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object balance = await this.fetchBalance(parameters);
        return getValue(balance, part);
    }

    public async virtual Task<object> fetchFreeBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.fetchPartialBalance("free", parameters);
    }

    public async virtual Task<object> fetchUsedBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.fetchPartialBalance("used", parameters);
    }

    public async virtual Task<object> fetchTotalBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.fetchPartialBalance("total", parameters);
    }

    public async virtual Task<object> fetchStatus(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchStatus() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTransactionFee(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "fetchTransactionFees")))
        {
            throw new NotSupported ((string)add(this.id, " fetchTransactionFee() is not supported yet")) ;
        }
        return await this.fetchTransactionFees(new List<object>() {code}, parameters);
    }

    public async virtual Task<object> fetchTransactionFees(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTransactionFees() is not supported yet")) ;
    }

    public async virtual Task<object> fetchDepositWithdrawFees(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchDepositWithdrawFees() is not supported yet")) ;
    }

    public async virtual Task<object> fetchDepositWithdrawFee(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "fetchDepositWithdrawFees")))
        {
            throw new NotSupported ((string)add(this.id, " fetchDepositWithdrawFee() is not supported yet")) ;
        }
        object fees = await this.fetchDepositWithdrawFees(new List<object>() {code}, parameters);
        return this.safeValue(fees, code);
    }

    public virtual object getSupportedMapping(object key, object mapping = null)
    {
        mapping ??= new Dictionary<string, object>();
        if (isTrue(inOp(mapping, key)))
        {
            return getValue(mapping, key);
        } else
        {
            throw new NotSupported ((string)add(add(add(this.id, " "), key), " does not have a value in mapping")) ;
        }
    }

    public async virtual Task<object> fetchCrossBorrowRate(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        if (!isTrue(getValue(this.has, "fetchBorrowRates")))
        {
            throw new NotSupported ((string)add(this.id, " fetchCrossBorrowRate() is not supported yet")) ;
        }
        object borrowRates = await this.fetchCrossBorrowRates(parameters);
        object rate = this.safeValue(borrowRates, code);
        if (isTrue(isEqual(rate, null)))
        {
            throw new ExchangeError ((string)add(add(this.id, " fetchCrossBorrowRate() could not find the borrow rate for currency code "), code)) ;
        }
        return rate;
    }

    public async virtual Task<object> fetchIsolatedBorrowRate(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        if (!isTrue(getValue(this.has, "fetchBorrowRates")))
        {
            throw new NotSupported ((string)add(this.id, " fetchIsolatedBorrowRate() is not supported yet")) ;
        }
        object borrowRates = await this.fetchIsolatedBorrowRates(parameters);
        object rate = this.safeDict(borrowRates, symbol);
        if (isTrue(isEqual(rate, null)))
        {
            throw new ExchangeError ((string)add(add(this.id, " fetchIsolatedBorrowRate() could not find the borrow rate for market symbol "), symbol)) ;
        }
        return rate;
    }

    public virtual object handleOptionAndParams(object parameters, object methodName, object optionName, object defaultValue = null)
    {
        // This method can be used to obtain method specific properties, i.e: this.handleOptionAndParams (params, 'fetchPosition', 'marginMode', 'isolated')
        object defaultOptionName = add("default", this.capitalize(optionName)); // we also need to check the 'defaultXyzWhatever'
        // check if params contain the key
        object value = this.safeValue2(parameters, optionName, defaultOptionName);
        if (isTrue(!isEqual(value, null)))
        {
            parameters = this.omit(parameters, new List<object>() {optionName, defaultOptionName});
        } else
        {
            // handle routed methods like "watchTrades > watchTradesForSymbols" (or "watchTicker > watchTickers")
            var methodNameparametersVariable = this.handleParamString(parameters, "callerMethodName", methodName);
            methodName = ((IList<object>)methodNameparametersVariable)[0];
            parameters = ((IList<object>)methodNameparametersVariable)[1];
            // check if exchange has properties for this method
            object exchangeWideMethodOptions = this.safeValue(this.options, methodName);
            if (isTrue(!isEqual(exchangeWideMethodOptions, null)))
            {
                // check if the option is defined inside this method's props
                value = this.safeValue2(exchangeWideMethodOptions, optionName, defaultOptionName);
            }
            if (isTrue(isEqual(value, null)))
            {
                // if it's still undefined, check if global exchange-wide option exists
                value = this.safeValue2(this.options, optionName, defaultOptionName);
            }
            // if it's still undefined, use the default value
            value = ((bool) isTrue((!isEqual(value, null)))) ? value : defaultValue;
        }
        return new List<object>() {value, parameters};
    }

    public virtual object handleOptionAndParams2(object parameters, object methodName1, object optionName1, object optionName2, object defaultValue = null)
    {
        object value = null;
        var valueparametersVariable = this.handleOptionAndParams(parameters, methodName1, optionName1);
        value = ((IList<object>)valueparametersVariable)[0];
        parameters = ((IList<object>)valueparametersVariable)[1];
        if (isTrue(!isEqual(value, null)))
        {
            // omit optionName2 too from params
            parameters = this.omit(parameters, optionName2);
            return new List<object>() {value, parameters};
        }
        // if still undefined, try optionName2
        object value2 = null;
        var value2parametersVariable = this.handleOptionAndParams(parameters, methodName1, optionName2, defaultValue);
        value2 = ((IList<object>)value2parametersVariable)[0];
        parameters = ((IList<object>)value2parametersVariable)[1];
        return new List<object>() {value2, parameters};
    }

    public virtual object handleOption(object methodName, object optionName, object defaultValue = null)
    {
        object res = this.handleOptionAndParams(new Dictionary<string, object>() {}, methodName, optionName, defaultValue);
        return this.safeValue(res, 0);
    }

    public virtual object handleMarketTypeAndParams(object methodName, object market = null, object parameters = null, object defaultValue = null)
    {
        /**
        * @ignore
        * @method
        * @name exchange#handleMarketTypeAndParams
        * @param methodName the method calling handleMarketTypeAndParams
        * @param {Market} market
        * @param {object} params
        * @param {string} [params.type] type assigned by user
        * @param {string} [params.defaultType] same as params.type
        * @param {string} [defaultValue] assigned programatically in the method calling handleMarketTypeAndParams
        * @returns {[string, object]} the market type and params with type and defaultType omitted
        */
        // type from param
        parameters ??= new Dictionary<string, object>();
        object type = this.safeString2(parameters, "defaultType", "type");
        if (isTrue(!isEqual(type, null)))
        {
            parameters = this.omit(parameters, new List<object>() {"defaultType", "type"});
            return new List<object>() {type, parameters};
        }
        // type from market
        if (isTrue(!isEqual(market, null)))
        {
            return new List<object>() {getValue(market, "type"), parameters};
        }
        // type from default-argument
        if (isTrue(!isEqual(defaultValue, null)))
        {
            return new List<object>() {defaultValue, parameters};
        }
        object methodOptions = this.safeDict(this.options, methodName);
        if (isTrue(!isEqual(methodOptions, null)))
        {
            if (isTrue((methodOptions is string)))
            {
                return new List<object>() {methodOptions, parameters};
            } else
            {
                object typeFromMethod = this.safeString2(methodOptions, "defaultType", "type");
                if (isTrue(!isEqual(typeFromMethod, null)))
                {
                    return new List<object>() {typeFromMethod, parameters};
                }
            }
        }
        object defaultType = this.safeString2(this.options, "defaultType", "type", "spot");
        return new List<object>() {defaultType, parameters};
    }

    public virtual object handleSubTypeAndParams(object methodName, object market = null, object parameters = null, object defaultValue = null)
    {
        parameters ??= new Dictionary<string, object>();
        object subType = null;
        // if set in params, it takes precedence
        object subTypeInParams = this.safeString2(parameters, "subType", "defaultSubType");
        // avoid omitting if it's not present
        if (isTrue(!isEqual(subTypeInParams, null)))
        {
            subType = subTypeInParams;
            parameters = this.omit(parameters, new List<object>() {"subType", "defaultSubType"});
        } else
        {
            // at first, check from market object
            if (isTrue(!isEqual(market, null)))
            {
                if (isTrue(getValue(market, "linear")))
                {
                    subType = "linear";
                } else if (isTrue(getValue(market, "inverse")))
                {
                    subType = "inverse";
                }
            }
            // if it was not defined in market object
            if (isTrue(isEqual(subType, null)))
            {
                object values = this.handleOptionAndParams(new Dictionary<string, object>() {}, methodName, "subType", defaultValue); // no need to re-test params here
                subType = getValue(values, 0);
            }
        }
        return new List<object>() {subType, parameters};
    }

    public virtual object handleMarginModeAndParams(object methodName, object parameters = null, object defaultValue = null)
    {
        /**
        * @ignore
        * @method
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {Array} the marginMode in lowercase as specified by params["marginMode"], params["defaultMarginMode"] this.options["marginMode"] or this.options["defaultMarginMode"]
        */
        parameters ??= new Dictionary<string, object>();
        return this.handleOptionAndParams(parameters, methodName, "marginMode", defaultValue);
    }

    public virtual void throwExactlyMatchedException(object exact, object str, object message)
    {
        if (isTrue(isEqual(str, null)))
        {
            return;
        }
        if (isTrue(inOp(exact, str)))
        {
            throwDynamicException(getValue(exact, str), message);
        }
    }

    public virtual void throwBroadlyMatchedException(object broad, object str, object message)
    {
        object broadKey = this.findBroadlyMatchedKey(broad, str);
        if (isTrue(!isEqual(broadKey, null)))
        {
            throwDynamicException(getValue(broad, broadKey), message);
        }
    }

    public virtual object findBroadlyMatchedKey(object broad, object str)
    {
        // a helper for matching error strings exactly vs broadly
        object keys = new List<object>(((IDictionary<string,object>)broad).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
        {
            object key = getValue(keys, i);
            if (isTrue(!isEqual(str, null)))
            {
                if (isTrue(isGreaterThanOrEqual(getIndexOf(str, key), 0)))
                {
                    return key;
                }
            }
        }
        return null;
    }

    public virtual object handleErrors(object statusCode, object statusText, object url, object method, object responseHeaders, object responseBody, object response, object requestHeaders, object requestBody)
    {
        // it is a stub method that must be overrided in the derived exchange classes
        // throw new NotSupported (this.id + ' handleErrors() not implemented yet');
        return null;
    }

    public virtual object calculateRateLimiterCost(object api, object method, object path, object parameters, object config = null)
    {
        config ??= new Dictionary<string, object>();
        return this.safeValue(config, "cost", 1);
    }

    public async virtual Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchTickers")))
        {
            await this.loadMarkets();
            object market = this.market(symbol);
            symbol = getValue(market, "symbol");
            object tickers = await this.fetchTickers(new List<object>() {symbol}, parameters);
            object ticker = this.safeDict(tickers, symbol);
            if (isTrue(isEqual(ticker, null)))
            {
                throw new NullResponse ((string)add(add(this.id, " fetchTickers() could not find a ticker for "), symbol)) ;
            } else
            {
                return ticker;
            }
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchTicker() is not supported yet")) ;
        }
    }

    public async virtual Task<object> fetchMarkPrice(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchMarkPrices")))
        {
            await this.loadMarkets();
            object market = this.market(symbol);
            symbol = getValue(market, "symbol");
            object tickers = await this.fetchMarkPrices(new List<object>() {symbol}, parameters);
            object ticker = this.safeDict(tickers, symbol);
            if (isTrue(isEqual(ticker, null)))
            {
                throw new NullResponse ((string)add(add(this.id, " fetchMarkPrices() could not find a ticker for "), symbol)) ;
            } else
            {
                return ticker;
            }
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchMarkPrices() is not supported yet")) ;
        }
    }

    public async virtual Task<object> fetchTickerWs(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchTickersWs")))
        {
            await this.loadMarkets();
            object market = this.market(symbol);
            symbol = getValue(market, "symbol");
            object tickers = await this.fetchTickersWs(new List<object>() {symbol}, parameters);
            object ticker = this.safeDict(tickers, symbol);
            if (isTrue(isEqual(ticker, null)))
            {
                throw new NullResponse ((string)add(add(this.id, " fetchTickerWs() could not find a ticker for "), symbol)) ;
            } else
            {
                return ticker;
            }
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchTickerWs() is not supported yet")) ;
        }
    }

    public async virtual Task<object> watchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchTicker() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTickers() is not supported yet")) ;
    }

    public async virtual Task<object> fetchMarkPrices(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchMarkPrices() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTickersWs(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTickers() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOrderBooks(object symbols = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOrderBooks() is not supported yet")) ;
    }

    public async virtual Task<object> watchBidsAsks(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchBidsAsks() is not supported yet")) ;
    }

    public async virtual Task<object> watchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchTickers() is not supported yet")) ;
    }

    public async virtual Task<object> unWatchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " unWatchTickers() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOrder() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOrderWs(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOrderWs() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOrderStatus(object id, object symbol = null, object parameters = null)
    {
        // TODO: TypeScript: change method signature by replacing
        // Promise<string> with Promise<Order['status']>.
        parameters ??= new Dictionary<string, object>();
        object order = await this.fetchOrder(id, symbol, parameters);
        return getValue(order, "status");
    }

    public async virtual Task<object> fetchUnifiedOrder(object order, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.fetchOrder(this.safeString(order, "id"), this.safeString(order, "symbol"), parameters);
    }

    public async virtual Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " createOrder() is not supported yet")) ;
    }

    public async virtual Task<object> createConvertTrade(object id, object fromCode, object toCode, object amount = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " createConvertTrade() is not supported yet")) ;
    }

    public async virtual Task<object> fetchConvertTrade(object id, object code = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchConvertTrade() is not supported yet")) ;
    }

    public async virtual Task<object> fetchConvertTradeHistory(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchConvertTradeHistory() is not supported yet")) ;
    }

    public async virtual Task<object> fetchPositionMode(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPositionMode() is not supported yet")) ;
    }

    public async virtual Task<object> createTrailingAmountOrder(object symbol, object type, object side, object amount, object price = null, object trailingAmount = null, object trailingTriggerPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createTrailingAmountOrder
        * @description create a trailing order by providing the symbol, type, side, amount, price and trailingAmount
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency, or number of contracts
        * @param {float} [price] the price for the order to be filled at, in units of the quote currency, ignored in market orders
        * @param {float} trailingAmount the quote amount to trail away from the current market price
        * @param {float} [trailingTriggerPrice] the price to activate a trailing order, default uses the price argument
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(trailingAmount, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createTrailingAmountOrder() requires a trailingAmount argument")) ;
        }
        ((IDictionary<string,object>)parameters)["trailingAmount"] = trailingAmount;
        if (isTrue(!isEqual(trailingTriggerPrice, null)))
        {
            ((IDictionary<string,object>)parameters)["trailingTriggerPrice"] = trailingTriggerPrice;
        }
        if (isTrue(getValue(this.has, "createTrailingAmountOrder")))
        {
            return await this.createOrder(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createTrailingAmountOrder() is not supported yet")) ;
    }

    public async virtual Task<object> createTrailingAmountOrderWs(object symbol, object type, object side, object amount, object price = null, object trailingAmount = null, object trailingTriggerPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createTrailingAmountOrderWs
        * @description create a trailing order by providing the symbol, type, side, amount, price and trailingAmount
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency, or number of contracts
        * @param {float} [price] the price for the order to be filled at, in units of the quote currency, ignored in market orders
        * @param {float} trailingAmount the quote amount to trail away from the current market price
        * @param {float} [trailingTriggerPrice] the price to activate a trailing order, default uses the price argument
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(trailingAmount, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createTrailingAmountOrderWs() requires a trailingAmount argument")) ;
        }
        ((IDictionary<string,object>)parameters)["trailingAmount"] = trailingAmount;
        if (isTrue(!isEqual(trailingTriggerPrice, null)))
        {
            ((IDictionary<string,object>)parameters)["trailingTriggerPrice"] = trailingTriggerPrice;
        }
        if (isTrue(getValue(this.has, "createTrailingAmountOrderWs")))
        {
            return await this.createOrderWs(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createTrailingAmountOrderWs() is not supported yet")) ;
    }

    public async virtual Task<object> createTrailingPercentOrder(object symbol, object type, object side, object amount, object price = null, object trailingPercent = null, object trailingTriggerPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createTrailingPercentOrder
        * @description create a trailing order by providing the symbol, type, side, amount, price and trailingPercent
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency, or number of contracts
        * @param {float} [price] the price for the order to be filled at, in units of the quote currency, ignored in market orders
        * @param {float} trailingPercent the percent to trail away from the current market price
        * @param {float} [trailingTriggerPrice] the price to activate a trailing order, default uses the price argument
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(trailingPercent, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createTrailingPercentOrder() requires a trailingPercent argument")) ;
        }
        ((IDictionary<string,object>)parameters)["trailingPercent"] = trailingPercent;
        if (isTrue(!isEqual(trailingTriggerPrice, null)))
        {
            ((IDictionary<string,object>)parameters)["trailingTriggerPrice"] = trailingTriggerPrice;
        }
        if (isTrue(getValue(this.has, "createTrailingPercentOrder")))
        {
            return await this.createOrder(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createTrailingPercentOrder() is not supported yet")) ;
    }

    public async virtual Task<object> createTrailingPercentOrderWs(object symbol, object type, object side, object amount, object price = null, object trailingPercent = null, object trailingTriggerPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createTrailingPercentOrderWs
        * @description create a trailing order by providing the symbol, type, side, amount, price and trailingPercent
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency, or number of contracts
        * @param {float} [price] the price for the order to be filled at, in units of the quote currency, ignored in market orders
        * @param {float} trailingPercent the percent to trail away from the current market price
        * @param {float} [trailingTriggerPrice] the price to activate a trailing order, default uses the price argument
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(trailingPercent, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createTrailingPercentOrderWs() requires a trailingPercent argument")) ;
        }
        ((IDictionary<string,object>)parameters)["trailingPercent"] = trailingPercent;
        if (isTrue(!isEqual(trailingTriggerPrice, null)))
        {
            ((IDictionary<string,object>)parameters)["trailingTriggerPrice"] = trailingTriggerPrice;
        }
        if (isTrue(getValue(this.has, "createTrailingPercentOrderWs")))
        {
            return await this.createOrderWs(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createTrailingPercentOrderWs() is not supported yet")) ;
    }

    public async virtual Task<object> createMarketOrderWithCost(object symbol, object side, object cost, object parameters = null)
    {
        /**
        * @method
        * @name createMarketOrderWithCost
        * @description create a market order by providing the symbol, side and cost
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} side 'buy' or 'sell'
        * @param {float} cost how much you want to trade in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isTrue(getValue(this.has, "createMarketOrderWithCost")) || isTrue((isTrue(getValue(this.has, "createMarketBuyOrderWithCost")) && isTrue(getValue(this.has, "createMarketSellOrderWithCost"))))))
        {
            return await this.createOrder(symbol, "market", side, cost, 1, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createMarketOrderWithCost() is not supported yet")) ;
    }

    public async virtual Task<object> createMarketBuyOrderWithCost(object symbol, object cost, object parameters = null)
    {
        /**
        * @method
        * @name createMarketBuyOrderWithCost
        * @description create a market buy order by providing the symbol and cost
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {float} cost how much you want to trade in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isTrue(getValue(this.options, "createMarketBuyOrderRequiresPrice")) || isTrue(getValue(this.has, "createMarketBuyOrderWithCost"))))
        {
            return await this.createOrder(symbol, "market", "buy", cost, 1, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createMarketBuyOrderWithCost() is not supported yet")) ;
    }

    public async virtual Task<object> createMarketSellOrderWithCost(object symbol, object cost, object parameters = null)
    {
        /**
        * @method
        * @name createMarketSellOrderWithCost
        * @description create a market sell order by providing the symbol and cost
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {float} cost how much you want to trade in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isTrue(getValue(this.options, "createMarketSellOrderRequiresPrice")) || isTrue(getValue(this.has, "createMarketSellOrderWithCost"))))
        {
            return await this.createOrder(symbol, "market", "sell", cost, 1, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createMarketSellOrderWithCost() is not supported yet")) ;
    }

    public async virtual Task<object> createMarketOrderWithCostWs(object symbol, object side, object cost, object parameters = null)
    {
        /**
        * @method
        * @name createMarketOrderWithCostWs
        * @description create a market order by providing the symbol, side and cost
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} side 'buy' or 'sell'
        * @param {float} cost how much you want to trade in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isTrue(getValue(this.has, "createMarketOrderWithCostWs")) || isTrue((isTrue(getValue(this.has, "createMarketBuyOrderWithCostWs")) && isTrue(getValue(this.has, "createMarketSellOrderWithCostWs"))))))
        {
            return await this.createOrderWs(symbol, "market", side, cost, 1, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createMarketOrderWithCostWs() is not supported yet")) ;
    }

    public async virtual Task<object> createTriggerOrder(object symbol, object type, object side, object amount, object price = null, object triggerPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createTriggerOrder
        * @description create a trigger stop order (type 1)
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
        * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
        * @param {float} triggerPrice the price to trigger the stop order, in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(triggerPrice, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createTriggerOrder() requires a triggerPrice argument")) ;
        }
        ((IDictionary<string,object>)parameters)["triggerPrice"] = triggerPrice;
        if (isTrue(getValue(this.has, "createTriggerOrder")))
        {
            return await this.createOrder(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createTriggerOrder() is not supported yet")) ;
    }

    public async virtual Task<object> createTriggerOrderWs(object symbol, object type, object side, object amount, object price = null, object triggerPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createTriggerOrderWs
        * @description create a trigger stop order (type 1)
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
        * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
        * @param {float} triggerPrice the price to trigger the stop order, in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(triggerPrice, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createTriggerOrderWs() requires a triggerPrice argument")) ;
        }
        ((IDictionary<string,object>)parameters)["triggerPrice"] = triggerPrice;
        if (isTrue(getValue(this.has, "createTriggerOrderWs")))
        {
            return await this.createOrderWs(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createTriggerOrderWs() is not supported yet")) ;
    }

    public async virtual Task<object> createStopLossOrder(object symbol, object type, object side, object amount, object price = null, object stopLossPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createStopLossOrder
        * @description create a trigger stop loss order (type 2)
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
        * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
        * @param {float} stopLossPrice the price to trigger the stop loss order, in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(stopLossPrice, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createStopLossOrder() requires a stopLossPrice argument")) ;
        }
        ((IDictionary<string,object>)parameters)["stopLossPrice"] = stopLossPrice;
        if (isTrue(getValue(this.has, "createStopLossOrder")))
        {
            return await this.createOrder(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createStopLossOrder() is not supported yet")) ;
    }

    public async virtual Task<object> createStopLossOrderWs(object symbol, object type, object side, object amount, object price = null, object stopLossPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createStopLossOrderWs
        * @description create a trigger stop loss order (type 2)
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
        * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
        * @param {float} stopLossPrice the price to trigger the stop loss order, in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(stopLossPrice, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createStopLossOrderWs() requires a stopLossPrice argument")) ;
        }
        ((IDictionary<string,object>)parameters)["stopLossPrice"] = stopLossPrice;
        if (isTrue(getValue(this.has, "createStopLossOrderWs")))
        {
            return await this.createOrderWs(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createStopLossOrderWs() is not supported yet")) ;
    }

    public async virtual Task<object> createTakeProfitOrder(object symbol, object type, object side, object amount, object price = null, object takeProfitPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createTakeProfitOrder
        * @description create a trigger take profit order (type 2)
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
        * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
        * @param {float} takeProfitPrice the price to trigger the take profit order, in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(takeProfitPrice, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createTakeProfitOrder() requires a takeProfitPrice argument")) ;
        }
        ((IDictionary<string,object>)parameters)["takeProfitPrice"] = takeProfitPrice;
        if (isTrue(getValue(this.has, "createTakeProfitOrder")))
        {
            return await this.createOrder(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createTakeProfitOrder() is not supported yet")) ;
    }

    public async virtual Task<object> createTakeProfitOrderWs(object symbol, object type, object side, object amount, object price = null, object takeProfitPrice = null, object parameters = null)
    {
        /**
        * @method
        * @name createTakeProfitOrderWs
        * @description create a trigger take profit order (type 2)
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
        * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
        * @param {float} takeProfitPrice the price to trigger the take profit order, in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(takeProfitPrice, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createTakeProfitOrderWs() requires a takeProfitPrice argument")) ;
        }
        ((IDictionary<string,object>)parameters)["takeProfitPrice"] = takeProfitPrice;
        if (isTrue(getValue(this.has, "createTakeProfitOrderWs")))
        {
            return await this.createOrderWs(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createTakeProfitOrderWs() is not supported yet")) ;
    }

    public async virtual Task<object> createOrderWithTakeProfitAndStopLoss(object symbol, object type, object side, object amount, object price = null, object takeProfit = null, object stopLoss = null, object parameters = null)
    {
        /**
        * @method
        * @name createOrderWithTakeProfitAndStopLoss
        * @description create an order with a stop loss or take profit attached (type 3)
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
        * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
        * @param {float} [takeProfit] the take profit price, in units of the quote currency
        * @param {float} [stopLoss] the stop loss price, in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @param {string} [params.takeProfitType] *not available on all exchanges* 'limit' or 'market'
        * @param {string} [params.stopLossType] *not available on all exchanges* 'limit' or 'market'
        * @param {string} [params.takeProfitPriceType] *not available on all exchanges* 'last', 'mark' or 'index'
        * @param {string} [params.stopLossPriceType] *not available on all exchanges* 'last', 'mark' or 'index'
        * @param {float} [params.takeProfitLimitPrice] *not available on all exchanges* limit price for a limit take profit order
        * @param {float} [params.stopLossLimitPrice] *not available on all exchanges* stop loss for a limit stop loss order
        * @param {float} [params.takeProfitAmount] *not available on all exchanges* the amount for a take profit
        * @param {float} [params.stopLossAmount] *not available on all exchanges* the amount for a stop loss
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        parameters = this.setTakeProfitAndStopLossParams(symbol, type, side, amount, price, takeProfit, stopLoss, parameters);
        if (isTrue(getValue(this.has, "createOrderWithTakeProfitAndStopLoss")))
        {
            return await this.createOrder(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createOrderWithTakeProfitAndStopLoss() is not supported yet")) ;
    }

    public virtual object setTakeProfitAndStopLossParams(object symbol, object type, object side, object amount, object price = null, object takeProfit = null, object stopLoss = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isTrue((isEqual(takeProfit, null))) && isTrue((isEqual(stopLoss, null)))))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createOrderWithTakeProfitAndStopLoss() requires either a takeProfit or stopLoss argument")) ;
        }
        if (isTrue(!isEqual(takeProfit, null)))
        {
            ((IDictionary<string,object>)parameters)["takeProfit"] = new Dictionary<string, object>() {
                { "triggerPrice", takeProfit },
            };
        }
        if (isTrue(!isEqual(stopLoss, null)))
        {
            ((IDictionary<string,object>)parameters)["stopLoss"] = new Dictionary<string, object>() {
                { "triggerPrice", stopLoss },
            };
        }
        object takeProfitType = this.safeString(parameters, "takeProfitType");
        object takeProfitPriceType = this.safeString(parameters, "takeProfitPriceType");
        object takeProfitLimitPrice = this.safeString(parameters, "takeProfitLimitPrice");
        object takeProfitAmount = this.safeString(parameters, "takeProfitAmount");
        object stopLossType = this.safeString(parameters, "stopLossType");
        object stopLossPriceType = this.safeString(parameters, "stopLossPriceType");
        object stopLossLimitPrice = this.safeString(parameters, "stopLossLimitPrice");
        object stopLossAmount = this.safeString(parameters, "stopLossAmount");
        if (isTrue(!isEqual(takeProfitType, null)))
        {
            ((IDictionary<string,object>)getValue(parameters, "takeProfit"))["type"] = takeProfitType;
        }
        if (isTrue(!isEqual(takeProfitPriceType, null)))
        {
            ((IDictionary<string,object>)getValue(parameters, "takeProfit"))["priceType"] = takeProfitPriceType;
        }
        if (isTrue(!isEqual(takeProfitLimitPrice, null)))
        {
            ((IDictionary<string,object>)getValue(parameters, "takeProfit"))["price"] = this.parseToNumeric(takeProfitLimitPrice);
        }
        if (isTrue(!isEqual(takeProfitAmount, null)))
        {
            ((IDictionary<string,object>)getValue(parameters, "takeProfit"))["amount"] = this.parseToNumeric(takeProfitAmount);
        }
        if (isTrue(!isEqual(stopLossType, null)))
        {
            ((IDictionary<string,object>)getValue(parameters, "stopLoss"))["type"] = stopLossType;
        }
        if (isTrue(!isEqual(stopLossPriceType, null)))
        {
            ((IDictionary<string,object>)getValue(parameters, "stopLoss"))["priceType"] = stopLossPriceType;
        }
        if (isTrue(!isEqual(stopLossLimitPrice, null)))
        {
            ((IDictionary<string,object>)getValue(parameters, "stopLoss"))["price"] = this.parseToNumeric(stopLossLimitPrice);
        }
        if (isTrue(!isEqual(stopLossAmount, null)))
        {
            ((IDictionary<string,object>)getValue(parameters, "stopLoss"))["amount"] = this.parseToNumeric(stopLossAmount);
        }
        parameters = this.omit(parameters, new List<object>() {"takeProfitType", "takeProfitPriceType", "takeProfitLimitPrice", "takeProfitAmount", "stopLossType", "stopLossPriceType", "stopLossLimitPrice", "stopLossAmount"});
        return parameters;
    }

    public async virtual Task<object> createOrderWithTakeProfitAndStopLossWs(object symbol, object type, object side, object amount, object price = null, object takeProfit = null, object stopLoss = null, object parameters = null)
    {
        /**
        * @method
        * @name createOrderWithTakeProfitAndStopLossWs
        * @description create an order with a stop loss or take profit attached (type 3)
        * @param {string} symbol unified symbol of the market to create an order in
        * @param {string} type 'market' or 'limit'
        * @param {string} side 'buy' or 'sell'
        * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
        * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
        * @param {float} [takeProfit] the take profit price, in units of the quote currency
        * @param {float} [stopLoss] the stop loss price, in units of the quote currency
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @param {string} [params.takeProfitType] *not available on all exchanges* 'limit' or 'market'
        * @param {string} [params.stopLossType] *not available on all exchanges* 'limit' or 'market'
        * @param {string} [params.takeProfitPriceType] *not available on all exchanges* 'last', 'mark' or 'index'
        * @param {string} [params.stopLossPriceType] *not available on all exchanges* 'last', 'mark' or 'index'
        * @param {float} [params.takeProfitLimitPrice] *not available on all exchanges* limit price for a limit take profit order
        * @param {float} [params.stopLossLimitPrice] *not available on all exchanges* stop loss for a limit stop loss order
        * @param {float} [params.takeProfitAmount] *not available on all exchanges* the amount for a take profit
        * @param {float} [params.stopLossAmount] *not available on all exchanges* the amount for a stop loss
        * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
        */
        parameters ??= new Dictionary<string, object>();
        parameters = this.setTakeProfitAndStopLossParams(symbol, type, side, amount, price, takeProfit, stopLoss, parameters);
        if (isTrue(getValue(this.has, "createOrderWithTakeProfitAndStopLossWs")))
        {
            return await this.createOrderWs(symbol, type, side, amount, price, parameters);
        }
        throw new NotSupported ((string)add(this.id, " createOrderWithTakeProfitAndStopLossWs() is not supported yet")) ;
    }

    public async virtual Task<object> createOrders(object orders, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " createOrders() is not supported yet")) ;
    }

    public async virtual Task<object> editOrders(object orders, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " editOrders() is not supported yet")) ;
    }

    public async virtual Task<object> createOrderWs(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " createOrderWs() is not supported yet")) ;
    }

    public async virtual Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " cancelOrder() is not supported yet")) ;
    }

    public async virtual Task<object> cancelOrderWs(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " cancelOrderWs() is not supported yet")) ;
    }

    public async virtual Task<object> cancelOrdersWs(object ids, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " cancelOrdersWs() is not supported yet")) ;
    }

    public async virtual Task<object> cancelAllOrders(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " cancelAllOrders() is not supported yet")) ;
    }

    public async virtual Task<object> cancelAllOrdersAfter(object timeout, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " cancelAllOrdersAfter() is not supported yet")) ;
    }

    public async virtual Task<object> cancelOrdersForSymbols(object orders, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " cancelOrdersForSymbols() is not supported yet")) ;
    }

    public async virtual Task<object> cancelAllOrdersWs(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " cancelAllOrdersWs() is not supported yet")) ;
    }

    public async virtual Task<object> cancelUnifiedOrder(object order, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return this.cancelOrder(this.safeString(order, "id"), this.safeString(order, "symbol"), parameters);
    }

    public async virtual Task<object> fetchOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isTrue(getValue(this.has, "fetchOpenOrders")) && isTrue(getValue(this.has, "fetchClosedOrders"))))
        {
            throw new NotSupported ((string)add(this.id, " fetchOrders() is not supported yet, consider using fetchOpenOrders() and fetchClosedOrders() instead")) ;
        }
        throw new NotSupported ((string)add(this.id, " fetchOrders() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOrdersWs(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOrdersWs() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOrderTrades(object id, object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOrderTrades() is not supported yet")) ;
    }

    public async virtual Task<object> watchOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchOrders() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchOrders")))
        {
            object orders = await this.fetchOrders(symbol, since, limit, parameters);
            return this.filterBy(orders, "status", "open");
        }
        throw new NotSupported ((string)add(this.id, " fetchOpenOrders() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOpenOrdersWs(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchOrdersWs")))
        {
            object orders = await this.fetchOrdersWs(symbol, since, limit, parameters);
            return this.filterBy(orders, "status", "open");
        }
        throw new NotSupported ((string)add(this.id, " fetchOpenOrdersWs() is not supported yet")) ;
    }

    public async virtual Task<object> fetchClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchOrders")))
        {
            object orders = await this.fetchOrders(symbol, since, limit, parameters);
            return this.filterBy(orders, "status", "closed");
        }
        throw new NotSupported ((string)add(this.id, " fetchClosedOrders() is not supported yet")) ;
    }

    public async virtual Task<object> fetchCanceledAndClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchCanceledAndClosedOrders() is not supported yet")) ;
    }

    public async virtual Task<object> fetchClosedOrdersWs(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchOrdersWs")))
        {
            object orders = await this.fetchOrdersWs(symbol, since, limit, parameters);
            return this.filterBy(orders, "status", "closed");
        }
        throw new NotSupported ((string)add(this.id, " fetchClosedOrdersWs() is not supported yet")) ;
    }

    public async virtual Task<object> fetchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchMyTrades() is not supported yet")) ;
    }

    public async virtual Task<object> fetchMyLiquidations(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchMyLiquidations() is not supported yet")) ;
    }

    public async virtual Task<object> fetchLiquidations(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchLiquidations() is not supported yet")) ;
    }

    public async virtual Task<object> fetchMyTradesWs(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchMyTradesWs() is not supported yet")) ;
    }

    public async virtual Task<object> watchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " watchMyTrades() is not supported yet")) ;
    }

    public async virtual Task<object> fetchGreeks(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchGreeks() is not supported yet")) ;
    }

    public async virtual Task<object> fetchAllGreeks(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchAllGreeks() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOptionChain(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOptionChain() is not supported yet")) ;
    }

    public async virtual Task<object> fetchOption(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchOption() is not supported yet")) ;
    }

    public async virtual Task<object> fetchConvertQuote(object fromCode, object toCode, object amount = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchConvertQuote() is not supported yet")) ;
    }

    public async virtual Task<object> fetchDepositsWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchDepositsWithdrawals() is not supported yet")) ;
    }

    public async virtual Task<object> fetchDeposits(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchDeposits() is not supported yet")) ;
    }

    public async virtual Task<object> fetchWithdrawals(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchWithdrawals() is not supported yet")) ;
    }

    public async virtual Task<object> fetchDepositsWs(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchDepositsWs() is not supported yet")) ;
    }

    public async virtual Task<object> fetchWithdrawalsWs(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchWithdrawalsWs() is not supported yet")) ;
    }

    public async virtual Task<object> fetchFundingRateHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchFundingRateHistory() is not supported yet")) ;
    }

    public async virtual Task<object> fetchFundingHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchFundingHistory() is not supported yet")) ;
    }

    public async virtual Task<object> closePosition(object symbol, object side = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " closePosition() is not supported yet")) ;
    }

    public async virtual Task<object> closeAllPositions(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " closeAllPositions() is not supported yet")) ;
    }

    public async virtual Task<object> fetchL3OrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new BadRequest ((string)add(this.id, " fetchL3OrderBook() is not supported yet")) ;
    }

    public virtual object parseLastPrice(object price, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseLastPrice() is not supported yet")) ;
    }

    public async virtual Task<object> fetchDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchDepositAddresses")))
        {
            object depositAddresses = await this.fetchDepositAddresses(new List<object>() {code}, parameters);
            object depositAddress = this.safeValue(depositAddresses, code);
            if (isTrue(isEqual(depositAddress, null)))
            {
                throw new InvalidAddress ((string)add(add(add(this.id, " fetchDepositAddress() could not find a deposit address for "), code), ", make sure you have created a corresponding deposit address in your wallet on the exchange website")) ;
            } else
            {
                return depositAddress;
            }
        } else if (isTrue(getValue(this.has, "fetchDepositAddressesByNetwork")))
        {
            object network = this.safeString(parameters, "network");
            parameters = this.omit(parameters, "network");
            object addressStructures = await this.fetchDepositAddressesByNetwork(code, parameters);
            if (isTrue(!isEqual(network, null)))
            {
                return this.safeDict(addressStructures, network);
            } else
            {
                object keys = new List<object>(((IDictionary<string,object>)addressStructures).Keys);
                object key = this.safeString(keys, 0);
                return this.safeDict(addressStructures, key);
            }
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchDepositAddress() is not supported yet")) ;
        }
    }

    public virtual object account()
    {
        return new Dictionary<string, object>() {
            { "free", null },
            { "used", null },
            { "total", null },
        };
    }

    public virtual object commonCurrencyCode(object code)
    {
        if (!isTrue(this.substituteCommonCurrencyCodes))
        {
            return code;
        }
        return this.safeString(this.commonCurrencies, code, code);
    }

    public virtual object currency(object code)
    {
        if (isTrue(isEqual(this.currencies, null)))
        {
            throw new ExchangeError ((string)add(this.id, " currencies not loaded")) ;
        }
        if (isTrue((code is string)))
        {
            if (isTrue(inOp(this.currencies, code)))
            {
                return getValue(this.currencies, code);
            } else if (isTrue(inOp(this.currencies_by_id, code)))
            {
                return getValue(this.currencies_by_id, code);
            }
        }
        throw new ExchangeError ((string)add(add(this.id, " does not have currency code "), code)) ;
    }

    public virtual object market(object symbol)
    {
        if (isTrue(isEqual(this.markets, null)))
        {
            throw new ExchangeError ((string)add(this.id, " markets not loaded")) ;
        }
        if (isTrue(inOp(this.markets, symbol)))
        {
            return getValue(this.markets, symbol);
        } else if (isTrue(inOp(this.markets_by_id, symbol)))
        {
            object markets = getValue(this.markets_by_id, symbol);
            object defaultType = this.safeString2(this.options, "defaultType", "defaultSubType", "spot");
            for (object i = 0; isLessThan(i, getArrayLength(markets)); postFixIncrement(ref i))
            {
                object market = getValue(markets, i);
                if (isTrue(getValue(market, defaultType)))
                {
                    return market;
                }
            }
            return getValue(markets, 0);
        } else if (isTrue(isTrue(isTrue(isTrue((((string)symbol).EndsWith(((string)"-C")))) || isTrue((((string)symbol).EndsWith(((string)"-P"))))) || isTrue((((string)symbol).StartsWith(((string)"C-"))))) || isTrue((((string)symbol).StartsWith(((string)"P-"))))))
        {
            return this.createExpiredOptionMarket(symbol);
        }
        throw new BadSymbol ((string)add(add(this.id, " does not have market symbol "), symbol)) ;
    }

    public virtual object createExpiredOptionMarket(object symbol)
    {
        throw new NotSupported ((string)add(this.id, " createExpiredOptionMarket () is not supported yet")) ;
    }

    public virtual object isLeveragedCurrency(object currencyCode, object checkBaseCoin = null, object existingCurrencies = null)
    {
        checkBaseCoin ??= false;
        object leverageSuffixes = new List<object>() {"2L", "2S", "3L", "3S", "4L", "4S", "5L", "5S", "UP", "DOWN", "BULL", "BEAR"};
        for (object i = 0; isLessThan(i, getArrayLength(leverageSuffixes)); postFixIncrement(ref i))
        {
            object leverageSuffix = getValue(leverageSuffixes, i);
            if (isTrue(((string)currencyCode).EndsWith(((string)leverageSuffix))))
            {
                if (!isTrue(checkBaseCoin))
                {
                    return true;
                } else
                {
                    // check if base currency is inside dict
                    object baseCurrencyCode = ((string)currencyCode).Replace((string)leverageSuffix, (string)"");
                    if (isTrue(inOp(existingCurrencies, baseCurrencyCode)))
                    {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public virtual object handleWithdrawTagAndParams(object tag, object parameters)
    {
        if (isTrue(isTrue((!isEqual(tag, null))) && isTrue(((tag is IDictionary<string, object>)))))
        {
            parameters = this.extend(tag, parameters);
            tag = null;
        }
        if (isTrue(isEqual(tag, null)))
        {
            tag = this.safeString(parameters, "tag");
            if (isTrue(!isEqual(tag, null)))
            {
                parameters = this.omit(parameters, "tag");
            }
        }
        return new List<object>() {tag, parameters};
    }

    public async virtual Task<object> createLimitOrder(object symbol, object side, object amount, object price, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrder(symbol, "limit", side, amount, price, parameters);
    }

    public async virtual Task<object> createLimitOrderWs(object symbol, object side, object amount, object price, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrderWs(symbol, "limit", side, amount, price, parameters);
    }

    public async virtual Task<object> createMarketOrder(object symbol, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrder(symbol, "market", side, amount, price, parameters);
    }

    public async virtual Task<object> createMarketOrderWs(object symbol, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrderWs(symbol, "market", side, amount, price, parameters);
    }

    public async virtual Task<object> createLimitBuyOrder(object symbol, object amount, object price, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrder(symbol, "limit", "buy", amount, price, parameters);
    }

    public async virtual Task<object> createLimitBuyOrderWs(object symbol, object amount, object price, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrderWs(symbol, "limit", "buy", amount, price, parameters);
    }

    public async virtual Task<object> createLimitSellOrder(object symbol, object amount, object price, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrder(symbol, "limit", "sell", amount, price, parameters);
    }

    public async virtual Task<object> createLimitSellOrderWs(object symbol, object amount, object price, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrderWs(symbol, "limit", "sell", amount, price, parameters);
    }

    public async virtual Task<object> createMarketBuyOrder(object symbol, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrder(symbol, "market", "buy", amount, null, parameters);
    }

    public async virtual Task<object> createMarketBuyOrderWs(object symbol, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrderWs(symbol, "market", "buy", amount, null, parameters);
    }

    public async virtual Task<object> createMarketSellOrder(object symbol, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrder(symbol, "market", "sell", amount, null, parameters);
    }

    public async virtual Task<object> createMarketSellOrderWs(object symbol, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createOrderWs(symbol, "market", "sell", amount, null, parameters);
    }

    public virtual object costToPrecision(object symbol, object cost)
    {
        if (isTrue(isEqual(cost, null)))
        {
            return null;
        }
        object market = this.market(symbol);
        return this.decimalToPrecision(cost, TRUNCATE, getValue(getValue(market, "precision"), "price"), this.precisionMode, this.paddingMode);
    }

    public virtual object priceToPrecision(object symbol, object price)
    {
        if (isTrue(isEqual(price, null)))
        {
            return null;
        }
        object market = this.market(symbol);
        object result = this.decimalToPrecision(price, ROUND, getValue(getValue(market, "precision"), "price"), this.precisionMode, this.paddingMode);
        if (isTrue(isEqual(result, "0")))
        {
            throw new InvalidOrder ((string)add(add(add(add(this.id, " price of "), getValue(market, "symbol")), " must be greater than minimum price precision of "), this.numberToString(getValue(getValue(market, "precision"), "price")))) ;
        }
        return result;
    }

    public virtual object amountToPrecision(object symbol, object amount)
    {
        if (isTrue(isEqual(amount, null)))
        {
            return null;
        }
        object market = this.market(symbol);
        object result = this.decimalToPrecision(amount, TRUNCATE, getValue(getValue(market, "precision"), "amount"), this.precisionMode, this.paddingMode);
        if (isTrue(isEqual(result, "0")))
        {
            throw new InvalidOrder ((string)add(add(add(add(this.id, " amount of "), getValue(market, "symbol")), " must be greater than minimum amount precision of "), this.numberToString(getValue(getValue(market, "precision"), "amount")))) ;
        }
        return result;
    }

    public virtual object feeToPrecision(object symbol, object fee)
    {
        if (isTrue(isEqual(fee, null)))
        {
            return null;
        }
        object market = this.market(symbol);
        return this.decimalToPrecision(fee, ROUND, getValue(getValue(market, "precision"), "price"), this.precisionMode, this.paddingMode);
    }

    public virtual object currencyToPrecision(object code, object fee, object networkCode = null)
    {
        object currency = getValue(this.currencies, code);
        object precision = this.safeValue(currency, "precision");
        if (isTrue(!isEqual(networkCode, null)))
        {
            object networks = this.safeDict(currency, "networks", new Dictionary<string, object>() {});
            object networkItem = this.safeDict(networks, networkCode, new Dictionary<string, object>() {});
            precision = this.safeValue(networkItem, "precision", precision);
        }
        if (isTrue(isEqual(precision, null)))
        {
            return this.forceString(fee);
        } else
        {
            object roundingMode = this.safeInteger(this.options, "currencyToPrecisionRoundingMode", ROUND);
            return this.decimalToPrecision(fee, roundingMode, precision, this.precisionMode, this.paddingMode);
        }
    }

    public virtual object forceString(object value)
    {
        if (isTrue(!(value is string)))
        {
            return this.numberToString(value);
        }
        return value;
    }

    public virtual object isTickPrecision()
    {
        return isEqual(this.precisionMode, TICK_SIZE);
    }

    public virtual object isDecimalPrecision()
    {
        return isEqual(this.precisionMode, DECIMAL_PLACES);
    }

    public virtual object isSignificantPrecision()
    {
        return isEqual(this.precisionMode, SIGNIFICANT_DIGITS);
    }

    public virtual object safeNumber(object obj, object key, object defaultNumber = null)
    {
        object value = this.safeString(obj, key);
        return this.parseNumber(value, defaultNumber);
    }

    public virtual object safeNumberN(object obj, object arr, object defaultNumber = null)
    {
        object value = this.safeStringN(obj, arr);
        return this.parseNumber(value, defaultNumber);
    }

    public virtual object parsePrecision(object precision)
    {
        /**
         * @ignore
         * @method
         * @param {string} precision The number of digits to the right of the decimal
         * @returns {string} a string number equal to 1e-precision
         */
        if (isTrue(isEqual(precision, null)))
        {
            return null;
        }
        object precisionNumber = parseInt(precision);
        if (isTrue(isEqual(precisionNumber, 0)))
        {
            return "1";
        }
        if (isTrue(isGreaterThan(precisionNumber, 0)))
        {
            object parsedPrecision = "0.";
            for (object i = 0; isLessThan(i, subtract(precisionNumber, 1)); postFixIncrement(ref i))
            {
                parsedPrecision = add(parsedPrecision, "0");
            }
            return add(parsedPrecision, "1");
        } else
        {
            object parsedPrecision = "1";
            for (object i = 0; isLessThan(i, subtract(multiply(precisionNumber, -1), 1)); postFixIncrement(ref i))
            {
                parsedPrecision = add(parsedPrecision, "0");
            }
            return add(parsedPrecision, "0");
        }
    }

    public virtual object integerPrecisionToAmount(object precision)
    {
        /**
         * @ignore
         * @method
         * @description handles positive & negative numbers too. parsePrecision() does not handle negative numbers, but this method handles
         * @param {string} precision The number of digits to the right of the decimal
         * @returns {string} a string number equal to 1e-precision
         */
        if (isTrue(isEqual(precision, null)))
        {
            return null;
        }
        if (isTrue(Precise.stringGe(precision, "0")))
        {
            return this.parsePrecision(precision);
        } else
        {
            object positivePrecisionString = Precise.stringAbs(precision);
            object positivePrecision = parseInt(positivePrecisionString);
            object parsedPrecision = "1";
            for (object i = 0; isLessThan(i, subtract(positivePrecision, 1)); postFixIncrement(ref i))
            {
                parsedPrecision = add(parsedPrecision, "0");
            }
            return add(parsedPrecision, "0");
        }
    }

    public async virtual Task<object> loadTimeDifference(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object serverTime = await this.fetchTime(parameters);
        object after = this.milliseconds();
        ((IDictionary<string,object>)this.options)["timeDifference"] = subtract(after, serverTime);
        return getValue(this.options, "timeDifference");
    }

    public virtual object implodeHostname(object url)
    {
        return this.implodeParams(url, new Dictionary<string, object>() {
            { "hostname", this.hostname },
        });
    }

    public async virtual Task<object> fetchMarketLeverageTiers(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchLeverageTiers")))
        {
            object market = this.market(symbol);
            if (!isTrue(getValue(market, "contract")))
            {
                throw new BadSymbol ((string)add(this.id, " fetchMarketLeverageTiers() supports contract markets only")) ;
            }
            object tiers = await this.fetchLeverageTiers(new List<object>() {symbol});
            return this.safeValue(tiers, symbol);
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchMarketLeverageTiers() is not supported yet")) ;
        }
    }

    public async virtual Task<object> createPostOnlyOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createPostOnlyOrder")))
        {
            throw new NotSupported ((string)add(this.id, " createPostOnlyOrder() is not supported yet")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "postOnly", true },
        });
        return await this.createOrder(symbol, type, side, amount, price, query);
    }

    public async virtual Task<object> createPostOnlyOrderWs(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createPostOnlyOrderWs")))
        {
            throw new NotSupported ((string)add(this.id, " createPostOnlyOrderWs() is not supported yet")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "postOnly", true },
        });
        return await this.createOrderWs(symbol, type, side, amount, price, query);
    }

    public async virtual Task<object> createReduceOnlyOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createReduceOnlyOrder")))
        {
            throw new NotSupported ((string)add(this.id, " createReduceOnlyOrder() is not supported yet")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "reduceOnly", true },
        });
        return await this.createOrder(symbol, type, side, amount, price, query);
    }

    public async virtual Task<object> createReduceOnlyOrderWs(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createReduceOnlyOrderWs")))
        {
            throw new NotSupported ((string)add(this.id, " createReduceOnlyOrderWs() is not supported yet")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "reduceOnly", true },
        });
        return await this.createOrderWs(symbol, type, side, amount, price, query);
    }

    public async virtual Task<object> createStopOrder(object symbol, object type, object side, object amount, object price = null, object triggerPrice = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createStopOrder")))
        {
            throw new NotSupported ((string)add(this.id, " createStopOrder() is not supported yet")) ;
        }
        if (isTrue(isEqual(triggerPrice, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " create_stop_order() requires a stopPrice argument")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "stopPrice", triggerPrice },
        });
        return await this.createOrder(symbol, type, side, amount, price, query);
    }

    public async virtual Task<object> createStopOrderWs(object symbol, object type, object side, object amount, object price = null, object triggerPrice = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createStopOrderWs")))
        {
            throw new NotSupported ((string)add(this.id, " createStopOrderWs() is not supported yet")) ;
        }
        if (isTrue(isEqual(triggerPrice, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createStopOrderWs() requires a stopPrice argument")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "stopPrice", triggerPrice },
        });
        return await this.createOrderWs(symbol, type, side, amount, price, query);
    }

    public async virtual Task<object> createStopLimitOrder(object symbol, object side, object amount, object price, object triggerPrice, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createStopLimitOrder")))
        {
            throw new NotSupported ((string)add(this.id, " createStopLimitOrder() is not supported yet")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "stopPrice", triggerPrice },
        });
        return await this.createOrder(symbol, "limit", side, amount, price, query);
    }

    public async virtual Task<object> createStopLimitOrderWs(object symbol, object side, object amount, object price, object triggerPrice, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createStopLimitOrderWs")))
        {
            throw new NotSupported ((string)add(this.id, " createStopLimitOrderWs() is not supported yet")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "stopPrice", triggerPrice },
        });
        return await this.createOrderWs(symbol, "limit", side, amount, price, query);
    }

    public async virtual Task<object> createStopMarketOrder(object symbol, object side, object amount, object triggerPrice, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createStopMarketOrder")))
        {
            throw new NotSupported ((string)add(this.id, " createStopMarketOrder() is not supported yet")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "stopPrice", triggerPrice },
        });
        return await this.createOrder(symbol, "market", side, amount, null, query);
    }

    public async virtual Task<object> createStopMarketOrderWs(object symbol, object side, object amount, object triggerPrice, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "createStopMarketOrderWs")))
        {
            throw new NotSupported ((string)add(this.id, " createStopMarketOrderWs() is not supported yet")) ;
        }
        object query = this.extend(parameters, new Dictionary<string, object>() {
            { "stopPrice", triggerPrice },
        });
        return await this.createOrderWs(symbol, "market", side, amount, null, query);
    }

    public virtual object safeCurrencyCode(object currencyId, object currency = null)
    {
        currency = this.safeCurrency(currencyId, currency);
        return getValue(currency, "code");
    }

    public virtual object filterBySymbolSinceLimit(object array, object symbol = null, object since = null, object limit = null, object tail = null)
    {
        tail ??= false;
        return this.filterByValueSinceLimit(array, "symbol", symbol, since, limit, "timestamp", tail);
    }

    public virtual object filterByCurrencySinceLimit(object array, object code = null, object since = null, object limit = null, object tail = null)
    {
        tail ??= false;
        return this.filterByValueSinceLimit(array, "currency", code, since, limit, "timestamp", tail);
    }

    public virtual object filterBySymbolsSinceLimit(object array, object symbols = null, object since = null, object limit = null, object tail = null)
    {
        tail ??= false;
        object result = this.filterByArray(array, "symbol", symbols, false);
        return this.filterBySinceLimit(result, since, limit, "timestamp", tail);
    }

    public virtual object parseLastPrices(object pricesData, object symbols = null, object parameters = null)
    {
        //
        // the value of tickers is either a dict or a list
        //
        // dict
        //
        //     {
        //         'marketId1': { ... },
        //         'marketId2': { ... },
        //         ...
        //     }
        //
        // list
        //
        //     [
        //         { 'market': 'marketId1', ... },
        //         { 'market': 'marketId2', ... },
        //         ...
        //     ]
        //
        parameters ??= new Dictionary<string, object>();
        object results = new List<object>() {};
        if (isTrue(((pricesData is IList<object>) || (pricesData.GetType().IsGenericType && pricesData.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            for (object i = 0; isLessThan(i, getArrayLength(pricesData)); postFixIncrement(ref i))
            {
                object priceData = this.extend(this.parseLastPrice(getValue(pricesData, i)), parameters);
                ((IList<object>)results).Add(priceData);
            }
        } else
        {
            object marketIds = new List<object>(((IDictionary<string,object>)pricesData).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
            {
                object marketId = getValue(marketIds, i);
                object market = this.safeMarket(marketId);
                object priceData = this.extend(this.parseLastPrice(getValue(pricesData, marketId), market), parameters);
                ((IList<object>)results).Add(priceData);
            }
        }
        symbols = this.marketSymbols(symbols);
        return this.filterByArray(results, "symbol", symbols);
    }

    public virtual object parseTickers(object tickers, object symbols = null, object parameters = null)
    {
        //
        // the value of tickers is either a dict or a list
        //
        //
        // dict
        //
        //     {
        //         'marketId1': { ... },
        //         'marketId2': { ... },
        //         'marketId3': { ... },
        //         ...
        //     }
        //
        // list
        //
        //     [
        //         { 'market': 'marketId1', ... },
        //         { 'market': 'marketId2', ... },
        //         { 'market': 'marketId3', ... },
        //         ...
        //     ]
        //
        parameters ??= new Dictionary<string, object>();
        object results = new List<object>() {};
        if (isTrue(((tickers is IList<object>) || (tickers.GetType().IsGenericType && tickers.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            for (object i = 0; isLessThan(i, getArrayLength(tickers)); postFixIncrement(ref i))
            {
                object parsedTicker = this.parseTicker(getValue(tickers, i));
                object ticker = this.extend(parsedTicker, parameters);
                ((IList<object>)results).Add(ticker);
            }
        } else
        {
            object marketIds = new List<object>(((IDictionary<string,object>)tickers).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
            {
                object marketId = getValue(marketIds, i);
                object market = this.safeMarket(marketId);
                object parsed = this.parseTicker(getValue(tickers, marketId), market);
                object ticker = this.extend(parsed, parameters);
                ((IList<object>)results).Add(ticker);
            }
        }
        symbols = this.marketSymbols(symbols);
        return this.filterByArray(results, "symbol", symbols);
    }

    public virtual object parseDepositAddresses(object addresses, object codes = null, object indexed = null, object parameters = null)
    {
        indexed ??= true;
        parameters ??= new Dictionary<string, object>();
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(addresses)); postFixIncrement(ref i))
        {
            object address = this.extend(this.parseDepositAddress(getValue(addresses, i)), parameters);
            ((IList<object>)result).Add(address);
        }
        if (isTrue(!isEqual(codes, null)))
        {
            result = this.filterByArray(result, "currency", codes, false);
        }
        if (isTrue(indexed))
        {
            result = this.filterByArray(result, "currency", null, indexed);
        }
        return result;
    }

    public virtual object parseBorrowInterests(object response, object market = null)
    {
        object interests = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object row = getValue(response, i);
            ((IList<object>)interests).Add(this.parseBorrowInterest(row, market));
        }
        return interests;
    }

    public virtual object parseBorrowRate(object info, object currency = null)
    {
        throw new NotSupported ((string)add(this.id, " parseBorrowRate() is not supported yet")) ;
    }

    public virtual object parseBorrowRateHistory(object response, object code, object since, object limit)
    {
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object item = getValue(response, i);
            object borrowRate = this.parseBorrowRate(item);
            ((IList<object>)result).Add(borrowRate);
        }
        object sorted = this.sortBy(result, "timestamp");
        return this.filterByCurrencySinceLimit(sorted, code, since, limit);
    }

    public virtual object parseIsolatedBorrowRates(object info)
    {
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(info)); postFixIncrement(ref i))
        {
            object item = getValue(info, i);
            object borrowRate = this.parseIsolatedBorrowRate(item);
            object symbol = this.safeString(borrowRate, "symbol");
            ((IDictionary<string,object>)result)[(string)symbol] = borrowRate;
        }
        return ((object)result);
    }

    public virtual object parseFundingRateHistories(object response, object market = null, object since = null, object limit = null)
    {
        object rates = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object entry = getValue(response, i);
            ((IList<object>)rates).Add(this.parseFundingRateHistory(entry, market));
        }
        object sorted = this.sortBy(rates, "timestamp");
        object symbol = ((bool) isTrue((isEqual(market, null)))) ? null : getValue(market, "symbol");
        return this.filterBySymbolSinceLimit(sorted, symbol, since, limit);
    }

    public virtual object safeSymbol(object marketId, object market = null, object delimiter = null, object marketType = null)
    {
        market = this.safeMarket(marketId, market, delimiter, marketType);
        return getValue(market, "symbol");
    }

    public virtual object parseFundingRate(object contract, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseFundingRate() is not supported yet")) ;
    }

    public virtual object parseFundingRates(object response, object symbols = null)
    {
        object fundingRates = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object entry = getValue(response, i);
            object parsed = this.parseFundingRate(entry);
            ((IDictionary<string,object>)fundingRates)[(string)getValue(parsed, "symbol")] = parsed;
        }
        return this.filterByArray(fundingRates, "symbol", symbols);
    }

    public virtual object parseLongShortRatio(object info, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseLongShortRatio() is not supported yet")) ;
    }

    public virtual object parseLongShortRatioHistory(object response, object market = null, object since = null, object limit = null)
    {
        object rates = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object entry = getValue(response, i);
            ((IList<object>)rates).Add(this.parseLongShortRatio(entry, market));
        }
        object sorted = this.sortBy(rates, "timestamp");
        object symbol = ((bool) isTrue((isEqual(market, null)))) ? null : getValue(market, "symbol");
        return this.filterBySymbolSinceLimit(sorted, symbol, since, limit);
    }

    public virtual object handleTriggerDirectionAndParams(object parameters, object exchangeSpecificKey = null, object allowEmpty = null)
    {
        /**
        * @ignore
        * @method
        * @returns {[string, object]} the trigger-direction value and omited params
        */
        allowEmpty ??= false;
        object triggerDirection = this.safeString(parameters, "triggerDirection");
        object exchangeSpecificDefined = isTrue((!isEqual(exchangeSpecificKey, null))) && isTrue((inOp(parameters, exchangeSpecificKey)));
        if (isTrue(!isEqual(triggerDirection, null)))
        {
            parameters = this.omit(parameters, "triggerDirection");
        }
        // throw exception if:
        // A) if provided value is not unified (support old "up/down" strings too)
        // B) if exchange specific "trigger direction key" (eg. "stopPriceSide") was not provided
        if (isTrue(isTrue(!isTrue(this.inArray(triggerDirection, new List<object>() {"ascending", "descending", "up", "down", "above", "below"})) && !isTrue(exchangeSpecificDefined)) && !isTrue(allowEmpty)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createOrder() : trigger orders require params[\"triggerDirection\"] to be either \"ascending\" or \"descending\"")) ;
        }
        // if old format was provided, overwrite to new
        if (isTrue(isTrue(isEqual(triggerDirection, "up")) || isTrue(isEqual(triggerDirection, "above"))))
        {
            triggerDirection = "ascending";
        } else if (isTrue(isTrue(isEqual(triggerDirection, "down")) || isTrue(isEqual(triggerDirection, "below"))))
        {
            triggerDirection = "descending";
        }
        return new List<object>() {triggerDirection, parameters};
    }

    public virtual object handleTriggerAndParams(object parameters)
    {
        object isTrigger = this.safeBool2(parameters, "trigger", "stop");
        if (isTrue(isTrigger))
        {
            parameters = this.omit(parameters, new List<object>() {"trigger", "stop"});
        }
        return new List<object>() {isTrigger, parameters};
    }

    public virtual object isTriggerOrder(object parameters)
    {
        // for backwards compatibility
        return this.handleTriggerAndParams(parameters);
    }

    public virtual object isPostOnly(object isMarketOrder, object exchangeSpecificParam, object parameters = null)
    {
        /**
        * @ignore
        * @method
        * @param {string} type Order type
        * @param {boolean} exchangeSpecificParam exchange specific postOnly
        * @param {object} [params] exchange specific params
        * @returns {boolean} true if a post only order, false otherwise
        */
        parameters ??= new Dictionary<string, object>();
        object timeInForce = this.safeStringUpper(parameters, "timeInForce");
        object postOnly = this.safeBool2(parameters, "postOnly", "post_only", false);
        // we assume timeInForce is uppercase from safeStringUpper (params, 'timeInForce')
        object ioc = isEqual(timeInForce, "IOC");
        object fok = isEqual(timeInForce, "FOK");
        object timeInForcePostOnly = isEqual(timeInForce, "PO");
        postOnly = isTrue(isTrue(postOnly) || isTrue(timeInForcePostOnly)) || isTrue(exchangeSpecificParam);
        if (isTrue(postOnly))
        {
            if (isTrue(isTrue(ioc) || isTrue(fok)))
            {
                throw new InvalidOrder ((string)add(add(this.id, " postOnly orders cannot have timeInForce equal to "), timeInForce)) ;
            } else if (isTrue(isMarketOrder))
            {
                throw new InvalidOrder ((string)add(this.id, " market orders cannot be postOnly")) ;
            } else
            {
                return true;
            }
        } else
        {
            return false;
        }
    }

    public virtual object handlePostOnly(object isMarketOrder, object exchangeSpecificPostOnlyOption, object parameters = null)
    {
        /**
        * @ignore
        * @method
        * @param {string} type Order type
        * @param {boolean} exchangeSpecificBoolean exchange specific postOnly
        * @param {object} [params] exchange specific params
        * @returns {Array}
        */
        parameters ??= new Dictionary<string, object>();
        object timeInForce = this.safeStringUpper(parameters, "timeInForce");
        object postOnly = this.safeBool(parameters, "postOnly", false);
        object ioc = isEqual(timeInForce, "IOC");
        object fok = isEqual(timeInForce, "FOK");
        object po = isEqual(timeInForce, "PO");
        postOnly = isTrue(isTrue(postOnly) || isTrue(po)) || isTrue(exchangeSpecificPostOnlyOption);
        if (isTrue(postOnly))
        {
            if (isTrue(isTrue(ioc) || isTrue(fok)))
            {
                throw new InvalidOrder ((string)add(add(this.id, " postOnly orders cannot have timeInForce equal to "), timeInForce)) ;
            } else if (isTrue(isMarketOrder))
            {
                throw new InvalidOrder ((string)add(this.id, " market orders cannot be postOnly")) ;
            } else
            {
                if (isTrue(po))
                {
                    parameters = this.omit(parameters, "timeInForce");
                }
                parameters = this.omit(parameters, "postOnly");
                return new List<object>() {true, parameters};
            }
        }
        return new List<object>() {false, parameters};
    }

    public async virtual Task<object> fetchLastPrices(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchLastPrices() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTradingFees(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTradingFees() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTradingFeesWs(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTradingFeesWs() is not supported yet")) ;
    }

    public async virtual Task<object> fetchTradingFee(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(getValue(this.has, "fetchTradingFees")))
        {
            throw new NotSupported ((string)add(this.id, " fetchTradingFee() is not supported yet")) ;
        }
        object fees = await this.fetchTradingFees(parameters);
        return this.safeDict(fees, symbol);
    }

    public async virtual Task<object> fetchConvertCurrencies(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchConvertCurrencies() is not supported yet")) ;
    }

    public virtual object parseOpenInterest(object interest, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseOpenInterest () is not supported yet")) ;
    }

    public virtual object parseOpenInterests(object response, object symbols = null)
    {
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object entry = getValue(response, i);
            object parsed = this.parseOpenInterest(entry);
            ((IDictionary<string,object>)result)[(string)getValue(parsed, "symbol")] = parsed;
        }
        return this.filterByArray(result, "symbol", symbols);
    }

    public virtual object parseOpenInterestsHistory(object response, object market = null, object since = null, object limit = null)
    {
        object interests = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object entry = getValue(response, i);
            object interest = this.parseOpenInterest(entry, market);
            ((IList<object>)interests).Add(interest);
        }
        object sorted = this.sortBy(interests, "timestamp");
        object symbol = this.safeString(market, "symbol");
        return this.filterBySymbolSinceLimit(sorted, symbol, since, limit);
    }

    public async virtual Task<object> fetchFundingRate(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchFundingRates")))
        {
            await this.loadMarkets();
            object market = this.market(symbol);
            symbol = getValue(market, "symbol");
            if (!isTrue(getValue(market, "contract")))
            {
                throw new BadSymbol ((string)add(this.id, " fetchFundingRate() supports contract markets only")) ;
            }
            object rates = await this.fetchFundingRates(new List<object>() {symbol}, parameters);
            object rate = this.safeValue(rates, symbol);
            if (isTrue(isEqual(rate, null)))
            {
                throw new NullResponse ((string)add(add(this.id, " fetchFundingRate () returned no data for "), symbol)) ;
            } else
            {
                return rate;
            }
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchFundingRate () is not supported yet")) ;
        }
    }

    public async virtual Task<object> fetchFundingInterval(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchFundingIntervals")))
        {
            await this.loadMarkets();
            object market = this.market(symbol);
            symbol = getValue(market, "symbol");
            if (!isTrue(getValue(market, "contract")))
            {
                throw new BadSymbol ((string)add(this.id, " fetchFundingInterval() supports contract markets only")) ;
            }
            object rates = await this.fetchFundingIntervals(new List<object>() {symbol}, parameters);
            object rate = this.safeValue(rates, symbol);
            if (isTrue(isEqual(rate, null)))
            {
                throw new NullResponse ((string)add(add(this.id, " fetchFundingInterval() returned no data for "), symbol)) ;
            } else
            {
                return rate;
            }
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchFundingInterval() is not supported yet")) ;
        }
    }

    public async virtual Task<object> fetchMarkOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        /**
        * @method
        * @name exchange#fetchMarkOHLCV
        * @description fetches historical mark price candlestick data containing the open, high, low, and close price of a market
        * @param {string} symbol unified symbol of the market to fetch OHLCV data for
        * @param {string} timeframe the length of time each candle represents
        * @param {int} [since] timestamp in ms of the earliest candle to fetch
        * @param {int} [limit] the maximum amount of candles to fetch
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {float[][]} A list of candles ordered as timestamp, open, high, low, close, undefined
        */
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchMarkOHLCV")))
        {
            object request = new Dictionary<string, object>() {
                { "price", "mark" },
            };
            return await this.fetchOHLCV(symbol, timeframe, since, limit, this.extend(request, parameters));
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchMarkOHLCV () is not supported yet")) ;
        }
    }

    public async virtual Task<object> fetchIndexOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        /**
        * @method
        * @name exchange#fetchIndexOHLCV
        * @description fetches historical index price candlestick data containing the open, high, low, and close price of a market
        * @param {string} symbol unified symbol of the market to fetch OHLCV data for
        * @param {string} timeframe the length of time each candle represents
        * @param {int} [since] timestamp in ms of the earliest candle to fetch
        * @param {int} [limit] the maximum amount of candles to fetch
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {} A list of candles ordered as timestamp, open, high, low, close, undefined
        */
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchIndexOHLCV")))
        {
            object request = new Dictionary<string, object>() {
                { "price", "index" },
            };
            return await this.fetchOHLCV(symbol, timeframe, since, limit, this.extend(request, parameters));
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchIndexOHLCV () is not supported yet")) ;
        }
    }

    public async virtual Task<object> fetchPremiumIndexOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        /**
        * @method
        * @name exchange#fetchPremiumIndexOHLCV
        * @description fetches historical premium index price candlestick data containing the open, high, low, and close price of a market
        * @param {string} symbol unified symbol of the market to fetch OHLCV data for
        * @param {string} timeframe the length of time each candle represents
        * @param {int} [since] timestamp in ms of the earliest candle to fetch
        * @param {int} [limit] the maximum amount of candles to fetch
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {float[][]} A list of candles ordered as timestamp, open, high, low, close, undefined
        */
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchPremiumIndexOHLCV")))
        {
            object request = new Dictionary<string, object>() {
                { "price", "premiumIndex" },
            };
            return await this.fetchOHLCV(symbol, timeframe, since, limit, this.extend(request, parameters));
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchPremiumIndexOHLCV () is not supported yet")) ;
        }
    }

    public virtual object handleTimeInForce(object parameters = null)
    {
        /**
        * @ignore
        * @method
        * Must add timeInForce to this.options to use this method
        * @returns {string} returns the exchange specific value for timeInForce
        */
        parameters ??= new Dictionary<string, object>();
        object timeInForce = this.safeStringUpper(parameters, "timeInForce"); // supported values GTC, IOC, PO
        if (isTrue(!isEqual(timeInForce, null)))
        {
            object exchangeValue = this.safeString(getValue(this.options, "timeInForce"), timeInForce);
            if (isTrue(isEqual(exchangeValue, null)))
            {
                throw new ExchangeError ((string)add(add(add(this.id, " does not support timeInForce \""), timeInForce), "\"")) ;
            }
            return exchangeValue;
        }
        return null;
    }

    public virtual object convertTypeToAccount(object account)
    {
        /**
         * @ignore
         * @method
         * Must add accountsByType to this.options to use this method
         * @param {string} account key for account name in this.options['accountsByType']
         * @returns the exchange specific account name or the isolated margin id for transfers
         */
        object accountsByType = this.safeDict(this.options, "accountsByType", new Dictionary<string, object>() {});
        object lowercaseAccount = ((string)account).ToLower();
        if (isTrue(inOp(accountsByType, lowercaseAccount)))
        {
            return getValue(accountsByType, lowercaseAccount);
        } else if (isTrue(isTrue((inOp(this.markets, account))) || isTrue((inOp(this.markets_by_id, account)))))
        {
            object market = this.market(account);
            return getValue(market, "id");
        } else
        {
            return account;
        }
    }

    public virtual void checkRequiredArgument(object methodName, object argument, object argumentName, object options = null)
    {
        /**
        * @ignore
        * @method
        * @param {string} methodName the name of the method that the argument is being checked for
        * @param {string} argument the argument's actual value provided
        * @param {string} argumentName the name of the argument being checked (for logging purposes)
        * @param {string[]} options a list of options that the argument can be
        * @returns {undefined}
        */
        options ??= new List<object>();
        object optionsLength = getArrayLength(options);
        if (isTrue(isTrue((isEqual(argument, null))) || isTrue((isTrue((isGreaterThan(optionsLength, 0))) && isTrue((!isTrue((this.inArray(argument, options)))))))))
        {
            object messageOptions = String.Join(", ", ((IList<object>)options).ToArray());
            object message = add(add(add(add(add(this.id, " "), methodName), "() requires a "), argumentName), " argument");
            if (isTrue(!isEqual(messageOptions, "")))
            {
                message = add(message, add(add(add(", one of ", "("), messageOptions), ")"));
            }
            throw new ArgumentsRequired ((string)message) ;
        }
    }

    public virtual void checkRequiredMarginArgument(object methodName, object symbol, object marginMode)
    {
        /**
         * @ignore
         * @method
         * @param {string} symbol unified symbol of the market
         * @param {string} methodName name of the method that requires a symbol
         * @param {string} marginMode is either 'isolated' or 'cross'
         */
        if (isTrue(isTrue((isEqual(marginMode, "isolated"))) && isTrue((isEqual(symbol, null)))))
        {
            throw new ArgumentsRequired ((string)add(add(add(this.id, " "), methodName), "() requires a symbol argument for isolated margin")) ;
        } else if (isTrue(isTrue((isEqual(marginMode, "cross"))) && isTrue((!isEqual(symbol, null)))))
        {
            throw new ArgumentsRequired ((string)add(add(add(this.id, " "), methodName), "() cannot have a symbol argument for cross margin")) ;
        }
    }

    public virtual object parseDepositWithdrawFees(object response, object codes = null, object currencyIdKey = null)
    {
        /**
         * @ignore
         * @method
         * @param {object[]|object} response unparsed response from the exchange
         * @param {string[]|undefined} codes the unified currency codes to fetch transactions fees for, returns all currencies when undefined
         * @param {str} currencyIdKey *should only be undefined when response is a dictionary* the object key that corresponds to the currency id
         * @returns {object} objects with withdraw and deposit fees, indexed by currency codes
         */
        object depositWithdrawFees = new Dictionary<string, object>() {};
        object isArray = ((response is IList<object>) || (response.GetType().IsGenericType && response.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))));
        object responseKeys = response;
        if (!isTrue(isArray))
        {
            responseKeys = new List<object>(((IDictionary<string,object>)response).Keys);
        }
        for (object i = 0; isLessThan(i, getArrayLength(responseKeys)); postFixIncrement(ref i))
        {
            object entry = getValue(responseKeys, i);
            object dictionary = ((bool) isTrue(isArray)) ? entry : getValue(response, entry);
            object currencyId = ((bool) isTrue(isArray)) ? this.safeString(dictionary, currencyIdKey) : entry;
            object currency = this.safeCurrency(currencyId);
            object code = this.safeString(currency, "code");
            if (isTrue(isTrue((isEqual(codes, null))) || isTrue((this.inArray(code, codes)))))
            {
                ((IDictionary<string,object>)depositWithdrawFees)[(string)code] = this.parseDepositWithdrawFee(dictionary, currency);
            }
        }
        return depositWithdrawFees;
    }

    public virtual object parseDepositWithdrawFee(object fee, object currency = null)
    {
        throw new NotSupported ((string)add(this.id, " parseDepositWithdrawFee() is not supported yet")) ;
    }

    public virtual object depositWithdrawFee(object info)
    {
        return new Dictionary<string, object>() {
            { "info", info },
            { "withdraw", new Dictionary<string, object>() {
                { "fee", null },
                { "percentage", null },
            } },
            { "deposit", new Dictionary<string, object>() {
                { "fee", null },
                { "percentage", null },
            } },
            { "networks", new Dictionary<string, object>() {} },
        };
    }

    public virtual object assignDefaultDepositWithdrawFees(object fee, object currency = null)
    {
        /**
         * @ignore
         * @method
         * @description Takes a depositWithdrawFee structure and assigns the default values for withdraw and deposit
         * @param {object} fee A deposit withdraw fee structure
         * @param {object} currency A currency structure, the response from this.currency ()
         * @returns {object} A deposit withdraw fee structure
         */
        object networkKeys = new List<object>(((IDictionary<string,object>)getValue(fee, "networks")).Keys);
        object numNetworks = getArrayLength(networkKeys);
        if (isTrue(isEqual(numNetworks, 1)))
        {
            ((IDictionary<string,object>)fee)["withdraw"] = getValue(getValue(getValue(fee, "networks"), getValue(networkKeys, 0)), "withdraw");
            ((IDictionary<string,object>)fee)["deposit"] = getValue(getValue(getValue(fee, "networks"), getValue(networkKeys, 0)), "deposit");
            return fee;
        }
        object currencyCode = this.safeString(currency, "code");
        for (object i = 0; isLessThan(i, numNetworks); postFixIncrement(ref i))
        {
            object network = getValue(networkKeys, i);
            if (isTrue(isEqual(network, currencyCode)))
            {
                ((IDictionary<string,object>)fee)["withdraw"] = getValue(getValue(getValue(fee, "networks"), getValue(networkKeys, i)), "withdraw");
                ((IDictionary<string,object>)fee)["deposit"] = getValue(getValue(getValue(fee, "networks"), getValue(networkKeys, i)), "deposit");
            }
        }
        return fee;
    }

    public virtual object parseIncome(object info, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseIncome () is not supported yet")) ;
    }

    public virtual object parseIncomes(object incomes, object market = null, object since = null, object limit = null)
    {
        /**
         * @ignore
         * @method
         * @description parses funding fee info from exchange response
         * @param {object[]} incomes each item describes once instance of currency being received or paid
         * @param {object} market ccxt market
         * @param {int} [since] when defined, the response items are filtered to only include items after this timestamp
         * @param {int} [limit] limits the number of items in the response
         * @returns {object[]} an array of [funding history structures]{@link https://docs.ccxt.com/#/?id=funding-history-structure}
         */
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(incomes)); postFixIncrement(ref i))
        {
            object entry = getValue(incomes, i);
            object parsed = this.parseIncome(entry, market);
            ((IList<object>)result).Add(parsed);
        }
        object sorted = this.sortBy(result, "timestamp");
        object symbol = this.safeString(market, "symbol");
        return this.filterBySymbolSinceLimit(sorted, symbol, since, limit);
    }

    public virtual object getMarketFromSymbols(object symbols = null)
    {
        if (isTrue(isEqual(symbols, null)))
        {
            return null;
        }
        object firstMarket = this.safeString(symbols, 0);
        object market = this.market(firstMarket);
        return market;
    }

    public virtual object parseWsOHLCVs(object ohlcvs, object market = null, object timeframe = null, object since = null, object limit = null)
    {
        timeframe ??= "1m";
        object results = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(ohlcvs)); postFixIncrement(ref i))
        {
            ((IList<object>)results).Add(this.parseWsOHLCV(getValue(ohlcvs, i), market));
        }
        return results;
    }

    public async virtual Task<object> fetchTransactions(object code = null, object since = null, object limit = null, object parameters = null)
    {
        /**
        * @method
        * @name exchange#fetchTransactions
        * @deprecated
        * @description *DEPRECATED* use fetchDepositsWithdrawals instead
        * @param {string} code unified currency code for the currency of the deposit/withdrawals, default is undefined
        * @param {int} [since] timestamp in ms of the earliest deposit/withdrawal, default is undefined
        * @param {int} [limit] max number of deposit/withdrawals to return, default is undefined
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {object} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchDepositsWithdrawals")))
        {
            return await this.fetchDepositsWithdrawals(code, since, limit, parameters);
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchTransactions () is not supported yet")) ;
        }
    }

    public virtual object filterByArrayPositions(object objects, object key, object values = null, object indexed = null)
    {
        /**
        * @ignore
        * @method
        * @description Typed wrapper for filterByArray that returns a list of positions
        */
        indexed ??= true;
        return this.filterByArray(objects, key, values, indexed);
    }

    public virtual object filterByArrayTickers(object objects, object key, object values = null, object indexed = null)
    {
        /**
        * @ignore
        * @method
        * @description Typed wrapper for filterByArray that returns a dictionary of tickers
        */
        indexed ??= true;
        return this.filterByArray(objects, key, values, indexed);
    }

    public virtual object createOHLCVObject(object symbol, object timeframe, object data)
    {
        object res = new Dictionary<string, object>() {};
        ((IDictionary<string,object>)res)[(string)symbol] = new Dictionary<string, object>() {};
        ((IDictionary<string,object>)getValue(res, symbol))[(string)timeframe] = data;
        return res;
    }

    public virtual object handleMaxEntriesPerRequestAndParams(object method, object maxEntriesPerRequest = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object newMaxEntriesPerRequest = null;
        var newMaxEntriesPerRequestparametersVariable = this.handleOptionAndParams(parameters, method, "maxEntriesPerRequest");
        newMaxEntriesPerRequest = ((IList<object>)newMaxEntriesPerRequestparametersVariable)[0];
        parameters = ((IList<object>)newMaxEntriesPerRequestparametersVariable)[1];
        if (isTrue(isTrue((!isEqual(newMaxEntriesPerRequest, null))) && isTrue((!isEqual(newMaxEntriesPerRequest, maxEntriesPerRequest)))))
        {
            maxEntriesPerRequest = newMaxEntriesPerRequest;
        }
        if (isTrue(isEqual(maxEntriesPerRequest, null)))
        {
            maxEntriesPerRequest = 1000; // default to 1000
        }
        return new List<object>() {maxEntriesPerRequest, parameters};
    }

    public async virtual Task<object> fetchPaginatedCallDynamic(object method, object symbol = null, object since = null, object limit = null, object parameters = null, object maxEntriesPerRequest = null, object removeRepeated = null)
    {
        parameters ??= new Dictionary<string, object>();
        removeRepeated ??= true;
        object maxCalls = null;
        var maxCallsparametersVariable = this.handleOptionAndParams(parameters, method, "paginationCalls", 10);
        maxCalls = ((IList<object>)maxCallsparametersVariable)[0];
        parameters = ((IList<object>)maxCallsparametersVariable)[1];
        object maxRetries = null;
        var maxRetriesparametersVariable = this.handleOptionAndParams(parameters, method, "maxRetries", 3);
        maxRetries = ((IList<object>)maxRetriesparametersVariable)[0];
        parameters = ((IList<object>)maxRetriesparametersVariable)[1];
        object paginationDirection = null;
        var paginationDirectionparametersVariable = this.handleOptionAndParams(parameters, method, "paginationDirection", "backward");
        paginationDirection = ((IList<object>)paginationDirectionparametersVariable)[0];
        parameters = ((IList<object>)paginationDirectionparametersVariable)[1];
        object paginationTimestamp = null;
        object removeRepeatedOption = removeRepeated;
        var removeRepeatedOptionparametersVariable = this.handleOptionAndParams(parameters, method, "removeRepeated", removeRepeated);
        removeRepeatedOption = ((IList<object>)removeRepeatedOptionparametersVariable)[0];
        parameters = ((IList<object>)removeRepeatedOptionparametersVariable)[1];
        object calls = 0;
        object result = new List<object>() {};
        object errors = 0;
        object until = this.safeInteger2(parameters, "untill", "till"); // do not omit it from params here
        var maxEntriesPerRequestparametersVariable = this.handleMaxEntriesPerRequestAndParams(method, maxEntriesPerRequest, parameters);
        maxEntriesPerRequest = ((IList<object>)maxEntriesPerRequestparametersVariable)[0];
        parameters = ((IList<object>)maxEntriesPerRequestparametersVariable)[1];
        if (isTrue((isEqual(paginationDirection, "forward"))))
        {
            if (isTrue(isEqual(since, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " pagination requires a since argument when paginationDirection set to forward")) ;
            }
            paginationTimestamp = since;
        }
        while ((isLessThan(calls, maxCalls)))
        {
            calls = add(calls, 1);
            try
            {
                if (isTrue(isEqual(paginationDirection, "backward")))
                {
                    // do it backwards, starting from the last
                    // UNTIL filtering is required in order to work
                    if (isTrue(!isEqual(paginationTimestamp, null)))
                    {
                        ((IDictionary<string,object>)parameters)["until"] = subtract(paginationTimestamp, 1);
                    }
                    object response = await ((Task<object>)callDynamically(this, method, new object[] { symbol, null, maxEntriesPerRequest, parameters }));
                    object responseLength = getArrayLength(response);
                    if (isTrue(this.verbose))
                    {
                        object backwardMessage = add(add(add(add(add("Dynamic pagination call ", this.numberToString(calls)), " method "), method), " response length "), this.numberToString(responseLength));
                        if (isTrue(!isEqual(paginationTimestamp, null)))
                        {
                            backwardMessage = add(backwardMessage, add(" timestamp ", this.numberToString(paginationTimestamp)));
                        }
                        this.log(backwardMessage);
                    }
                    if (isTrue(isEqual(responseLength, 0)))
                    {
                        break;
                    }
                    errors = 0;
                    result = this.arrayConcat(result, response);
                    object firstElement = this.safeValue(response, 0);
                    paginationTimestamp = this.safeInteger2(firstElement, "timestamp", 0);
                    if (isTrue(isTrue((!isEqual(since, null))) && isTrue((isLessThanOrEqual(paginationTimestamp, since)))))
                    {
                        break;
                    }
                } else
                {
                    // do it forwards, starting from the since
                    object response = await ((Task<object>)callDynamically(this, method, new object[] { symbol, paginationTimestamp, maxEntriesPerRequest, parameters }));
                    object responseLength = getArrayLength(response);
                    if (isTrue(this.verbose))
                    {
                        object forwardMessage = add(add(add(add(add("Dynamic pagination call ", this.numberToString(calls)), " method "), method), " response length "), this.numberToString(responseLength));
                        if (isTrue(!isEqual(paginationTimestamp, null)))
                        {
                            forwardMessage = add(forwardMessage, add(" timestamp ", this.numberToString(paginationTimestamp)));
                        }
                        this.log(forwardMessage);
                    }
                    if (isTrue(isEqual(responseLength, 0)))
                    {
                        break;
                    }
                    errors = 0;
                    result = this.arrayConcat(result, response);
                    object last = this.safeValue(response, subtract(responseLength, 1));
                    paginationTimestamp = add(this.safeInteger(last, "timestamp"), 1);
                    if (isTrue(isTrue((!isEqual(until, null))) && isTrue((isGreaterThanOrEqual(paginationTimestamp, until)))))
                    {
                        break;
                    }
                }
            } catch(Exception e)
            {
                errors = add(errors, 1);
                if (isTrue(isGreaterThan(errors, maxRetries)))
                {
                    throw e;
                }
            }
        }
        object uniqueResults = result;
        if (isTrue(removeRepeatedOption))
        {
            uniqueResults = this.removeRepeatedElementsFromArray(result);
        }
        object key = ((bool) isTrue((isEqual(method, "fetchOHLCV")))) ? 0 : "timestamp";
        return this.filterBySinceLimit(uniqueResults, since, limit, key);
    }

    public async virtual Task<object> safeDeterministicCall(object method, object symbol = null, object since = null, object limit = null, object timeframe = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object maxRetries = null;
        var maxRetriesparametersVariable = this.handleOptionAndParams(parameters, method, "maxRetries", 3);
        maxRetries = ((IList<object>)maxRetriesparametersVariable)[0];
        parameters = ((IList<object>)maxRetriesparametersVariable)[1];
        object errors = 0;
        while (isLessThanOrEqual(errors, maxRetries))
        {
            try
            {
                if (isTrue(isTrue(timeframe) && isTrue(!isEqual(method, "fetchFundingRateHistory"))))
                {
                    return await ((Task<object>)callDynamically(this, method, new object[] { symbol, timeframe, since, limit, parameters }));
                } else
                {
                    return await ((Task<object>)callDynamically(this, method, new object[] { symbol, since, limit, parameters }));
                }
            } catch(Exception e)
            {
                if (isTrue(e is RateLimitExceeded))
                {
                    throw e;
                }
                errors = add(errors, 1);
                if (isTrue(isGreaterThan(errors, maxRetries)))
                {
                    throw e;
                }
            }
        }
        return new List<object>() {};
    }

    public async virtual Task<object> fetchPaginatedCallDeterministic(object method, object symbol = null, object since = null, object limit = null, object timeframe = null, object parameters = null, object maxEntriesPerRequest = null)
    {
        parameters ??= new Dictionary<string, object>();
        object maxCalls = null;
        var maxCallsparametersVariable = this.handleOptionAndParams(parameters, method, "paginationCalls", 10);
        maxCalls = ((IList<object>)maxCallsparametersVariable)[0];
        parameters = ((IList<object>)maxCallsparametersVariable)[1];
        var maxEntriesPerRequestparametersVariable = this.handleMaxEntriesPerRequestAndParams(method, maxEntriesPerRequest, parameters);
        maxEntriesPerRequest = ((IList<object>)maxEntriesPerRequestparametersVariable)[0];
        parameters = ((IList<object>)maxEntriesPerRequestparametersVariable)[1];
        object current = this.milliseconds();
        object tasks = new List<object>() {};
        object time = multiply(this.parseTimeframe(timeframe), 1000);
        object step = multiply(time, maxEntriesPerRequest);
        object currentSince = subtract(subtract(current, (multiply(maxCalls, step))), 1);
        if (isTrue(!isEqual(since, null)))
        {
            currentSince = mathMax(currentSince, since);
        } else
        {
            currentSince = mathMax(currentSince, 1241440531000); // avoid timestamps older than 2009
        }
        object until = this.safeInteger2(parameters, "until", "till"); // do not omit it here
        if (isTrue(!isEqual(until, null)))
        {
            object requiredCalls = Math.Ceiling(Convert.ToDouble(divide((subtract(until, since)), step)));
            if (isTrue(isGreaterThan(requiredCalls, maxCalls)))
            {
                throw new BadRequest ((string)add(add(add(add(this.id, " the number of required calls is greater than the max number of calls allowed, either increase the paginationCalls or decrease the since-until gap. Current paginationCalls limit is "), ((object)maxCalls).ToString()), " required calls is "), ((object)requiredCalls).ToString())) ;
            }
        }
        for (object i = 0; isLessThan(i, maxCalls); postFixIncrement(ref i))
        {
            if (isTrue(isTrue((!isEqual(until, null))) && isTrue((isGreaterThanOrEqual(currentSince, until)))))
            {
                break;
            }
            if (isTrue(isGreaterThanOrEqual(currentSince, current)))
            {
                break;
            }
            ((IList<object>)tasks).Add(this.safeDeterministicCall(method, symbol, currentSince, maxEntriesPerRequest, timeframe, parameters));
            currentSince = subtract(this.sum(currentSince, step), 1);
        }
        object results = await promiseAll(tasks);
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(results)); postFixIncrement(ref i))
        {
            result = this.arrayConcat(result, getValue(results, i));
        }
        object uniqueResults = ((object)this.removeRepeatedElementsFromArray(result));
        object key = ((bool) isTrue((isEqual(method, "fetchOHLCV")))) ? 0 : "timestamp";
        return this.filterBySinceLimit(uniqueResults, since, limit, key);
    }

    public async virtual Task<object> fetchPaginatedCallCursor(object method, object symbol = null, object since = null, object limit = null, object parameters = null, object cursorReceived = null, object cursorSent = null, object cursorIncrement = null, object maxEntriesPerRequest = null)
    {
        parameters ??= new Dictionary<string, object>();
        object maxCalls = null;
        var maxCallsparametersVariable = this.handleOptionAndParams(parameters, method, "paginationCalls", 10);
        maxCalls = ((IList<object>)maxCallsparametersVariable)[0];
        parameters = ((IList<object>)maxCallsparametersVariable)[1];
        object maxRetries = null;
        var maxRetriesparametersVariable = this.handleOptionAndParams(parameters, method, "maxRetries", 3);
        maxRetries = ((IList<object>)maxRetriesparametersVariable)[0];
        parameters = ((IList<object>)maxRetriesparametersVariable)[1];
        var maxEntriesPerRequestparametersVariable = this.handleMaxEntriesPerRequestAndParams(method, maxEntriesPerRequest, parameters);
        maxEntriesPerRequest = ((IList<object>)maxEntriesPerRequestparametersVariable)[0];
        parameters = ((IList<object>)maxEntriesPerRequestparametersVariable)[1];
        object cursorValue = null;
        object i = 0;
        object errors = 0;
        object result = new List<object>() {};
        object timeframe = this.safeString(parameters, "timeframe");
        parameters = this.omit(parameters, "timeframe"); // reading the timeframe from the method arguments to avoid changing the signature
        while (isLessThan(i, maxCalls))
        {
            try
            {
                if (isTrue(!isEqual(cursorValue, null)))
                {
                    if (isTrue(!isEqual(cursorIncrement, null)))
                    {
                        cursorValue = add(this.parseToInt(cursorValue), cursorIncrement);
                    }
                    ((IDictionary<string,object>)parameters)[(string)cursorSent] = cursorValue;
                }
                object response = null;
                if (isTrue(isEqual(method, "fetchAccounts")))
                {
                    response = await ((Task<object>)callDynamically(this, method, new object[] { parameters }));
                } else if (isTrue(isTrue(isEqual(method, "getLeverageTiersPaginated")) || isTrue(isEqual(method, "fetchPositions"))))
                {
                    response = await ((Task<object>)callDynamically(this, method, new object[] { symbol, parameters }));
                } else if (isTrue(isEqual(method, "fetchOpenInterestHistory")))
                {
                    response = await ((Task<object>)callDynamically(this, method, new object[] { symbol, timeframe, since, maxEntriesPerRequest, parameters }));
                } else
                {
                    response = await ((Task<object>)callDynamically(this, method, new object[] { symbol, since, maxEntriesPerRequest, parameters }));
                }
                errors = 0;
                object responseLength = getArrayLength(response);
                if (isTrue(this.verbose))
                {
                    object cursorString = ((bool) isTrue((isEqual(cursorValue, null)))) ? "" : cursorValue;
                    object iteration = (add(i, 1));
                    object cursorMessage = add(add(add(add(add(add(add("Cursor pagination call ", ((object)iteration).ToString()), " method "), method), " response length "), ((object)responseLength).ToString()), " cursor "), cursorString);
                    this.log(cursorMessage);
                }
                if (isTrue(isEqual(responseLength, 0)))
                {
                    break;
                }
                result = this.arrayConcat(result, response);
                object last = this.safeDict(response, subtract(responseLength, 1));
                // cursorValue = this.safeValue (last['info'], cursorReceived);
                cursorValue = null; // search for the cursor
                for (object j = 0; isLessThan(j, responseLength); postFixIncrement(ref j))
                {
                    object index = subtract(subtract(responseLength, j), 1);
                    object entry = this.safeDict(response, index);
                    object info = this.safeDict(entry, "info");
                    object cursor = this.safeValue(info, cursorReceived);
                    if (isTrue(!isEqual(cursor, null)))
                    {
                        cursorValue = cursor;
                        break;
                    }
                }
                if (isTrue(isEqual(cursorValue, null)))
                {
                    break;
                }
                object lastTimestamp = this.safeInteger(last, "timestamp");
                if (isTrue(isTrue(!isEqual(lastTimestamp, null)) && isTrue(isLessThan(lastTimestamp, since))))
                {
                    break;
                }
            } catch(Exception e)
            {
                errors = add(errors, 1);
                if (isTrue(isGreaterThan(errors, maxRetries)))
                {
                    throw e;
                }
            }
            i = add(i, 1);
        }
        object sorted = this.sortCursorPaginatedResult(result);
        object key = ((bool) isTrue((isEqual(method, "fetchOHLCV")))) ? 0 : "timestamp";
        return this.filterBySinceLimit(sorted, since, limit, key);
    }

    public async virtual Task<object> fetchPaginatedCallIncremental(object method, object symbol = null, object since = null, object limit = null, object parameters = null, object pageKey = null, object maxEntriesPerRequest = null)
    {
        parameters ??= new Dictionary<string, object>();
        object maxCalls = null;
        var maxCallsparametersVariable = this.handleOptionAndParams(parameters, method, "paginationCalls", 10);
        maxCalls = ((IList<object>)maxCallsparametersVariable)[0];
        parameters = ((IList<object>)maxCallsparametersVariable)[1];
        object maxRetries = null;
        var maxRetriesparametersVariable = this.handleOptionAndParams(parameters, method, "maxRetries", 3);
        maxRetries = ((IList<object>)maxRetriesparametersVariable)[0];
        parameters = ((IList<object>)maxRetriesparametersVariable)[1];
        var maxEntriesPerRequestparametersVariable = this.handleMaxEntriesPerRequestAndParams(method, maxEntriesPerRequest, parameters);
        maxEntriesPerRequest = ((IList<object>)maxEntriesPerRequestparametersVariable)[0];
        parameters = ((IList<object>)maxEntriesPerRequestparametersVariable)[1];
        object i = 0;
        object errors = 0;
        object result = new List<object>() {};
        while (isLessThan(i, maxCalls))
        {
            try
            {
                ((IDictionary<string,object>)parameters)[(string)pageKey] = add(i, 1);
                object response = await ((Task<object>)callDynamically(this, method, new object[] { symbol, since, maxEntriesPerRequest, parameters }));
                errors = 0;
                object responseLength = getArrayLength(response);
                if (isTrue(this.verbose))
                {
                    object iteration = ((object)(add(i, 1))).ToString();
                    object incrementalMessage = add(add(add(add(add("Incremental pagination call ", iteration), " method "), method), " response length "), ((object)responseLength).ToString());
                    this.log(incrementalMessage);
                }
                if (isTrue(isEqual(responseLength, 0)))
                {
                    break;
                }
                result = this.arrayConcat(result, response);
            } catch(Exception e)
            {
                errors = add(errors, 1);
                if (isTrue(isGreaterThan(errors, maxRetries)))
                {
                    throw e;
                }
            }
            i = add(i, 1);
        }
        object sorted = this.sortCursorPaginatedResult(result);
        object key = ((bool) isTrue((isEqual(method, "fetchOHLCV")))) ? 0 : "timestamp";
        return this.filterBySinceLimit(sorted, since, limit, key);
    }

    public virtual object sortCursorPaginatedResult(object result)
    {
        object first = this.safeValue(result, 0);
        if (isTrue(!isEqual(first, null)))
        {
            if (isTrue(inOp(first, "timestamp")))
            {
                return this.sortBy(result, "timestamp", true);
            }
            if (isTrue(inOp(first, "id")))
            {
                return this.sortBy(result, "id", true);
            }
        }
        return result;
    }

    public virtual object removeRepeatedElementsFromArray(object input, object fallbackToTimestamp = null)
    {
        fallbackToTimestamp ??= true;
        object uniqueDic = new Dictionary<string, object>() {};
        object uniqueResult = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(input)); postFixIncrement(ref i))
        {
            object entry = getValue(input, i);
            object uniqValue = ((bool) isTrue(fallbackToTimestamp)) ? this.safeStringN(entry, new List<object>() {"id", "timestamp", 0}) : this.safeString(entry, "id");
            if (isTrue(isTrue(!isEqual(uniqValue, null)) && !isTrue((inOp(uniqueDic, uniqValue)))))
            {
                ((IDictionary<string,object>)uniqueDic)[(string)uniqValue] = 1;
                ((IList<object>)uniqueResult).Add(entry);
            }
        }
        object valuesLength = getArrayLength(uniqueResult);
        if (isTrue(isGreaterThan(valuesLength, 0)))
        {
            return ((object)uniqueResult);
        }
        return input;
    }

    public virtual object removeRepeatedTradesFromArray(object input)
    {
        object uniqueResult = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(input)); postFixIncrement(ref i))
        {
            object entry = getValue(input, i);
            object id = this.safeString(entry, "id");
            if (isTrue(isEqual(id, null)))
            {
                object price = this.safeString(entry, "price");
                object amount = this.safeString(entry, "amount");
                object timestamp = this.safeString(entry, "timestamp");
                object side = this.safeString(entry, "side");
                // unique trade identifier
                id = add(add(add(add(add(add(add("t_", ((object)timestamp).ToString()), "_"), side), "_"), price), "_"), amount);
            }
            if (isTrue(isTrue(!isEqual(id, null)) && !isTrue((inOp(uniqueResult, id)))))
            {
                ((IDictionary<string,object>)uniqueResult)[(string)id] = entry;
            }
        }
        object values = new List<object>(((IDictionary<string,object>)uniqueResult).Values);
        return ((object)values);
    }

    public virtual object handleUntilOption(object key, object request, object parameters, object multiplier = null)
    {
        multiplier ??= 1;
        object until = this.safeInteger2(parameters, "until", "till");
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)[(string)key] = this.parseToInt(multiply(until, multiplier));
            parameters = this.omit(parameters, new List<object>() {"until", "till"});
        }
        return new List<object>() {request, parameters};
    }

    public virtual object safeOpenInterest(object interest, object market = null)
    {
        object symbol = this.safeString(interest, "symbol");
        if (isTrue(isEqual(symbol, null)))
        {
            symbol = this.safeString(market, "symbol");
        }
        return this.extend(interest, new Dictionary<string, object>() {
            { "symbol", symbol },
            { "baseVolume", this.safeNumber(interest, "baseVolume") },
            { "quoteVolume", this.safeNumber(interest, "quoteVolume") },
            { "openInterestAmount", this.safeNumber(interest, "openInterestAmount") },
            { "openInterestValue", this.safeNumber(interest, "openInterestValue") },
            { "timestamp", this.safeInteger(interest, "timestamp") },
            { "datetime", this.safeString(interest, "datetime") },
            { "info", this.safeValue(interest, "info") },
        });
    }

    public virtual object parseLiquidation(object liquidation, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseLiquidation () is not supported yet")) ;
    }

    public virtual object parseLiquidations(object liquidations, object market = null, object since = null, object limit = null)
    {
        /**
         * @ignore
         * @method
         * @description parses liquidation info from the exchange response
         * @param {object[]} liquidations each item describes an instance of a liquidation event
         * @param {object} market ccxt market
         * @param {int} [since] when defined, the response items are filtered to only include items after this timestamp
         * @param {int} [limit] limits the number of items in the response
         * @returns {object[]} an array of [liquidation structures]{@link https://docs.ccxt.com/#/?id=liquidation-structure}
         */
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(liquidations)); postFixIncrement(ref i))
        {
            object entry = getValue(liquidations, i);
            object parsed = this.parseLiquidation(entry, market);
            ((IList<object>)result).Add(parsed);
        }
        object sorted = this.sortBy(result, "timestamp");
        object symbol = this.safeString(market, "symbol");
        return this.filterBySymbolSinceLimit(sorted, symbol, since, limit);
    }

    public virtual object parseGreeks(object greeks, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseGreeks () is not supported yet")) ;
    }

    public virtual object parseAllGreeks(object greeks, object symbols = null, object parameters = null)
    {
        //
        // the value of greeks is either a dict or a list
        //
        parameters ??= new Dictionary<string, object>();
        object results = new List<object>() {};
        if (isTrue(((greeks is IList<object>) || (greeks.GetType().IsGenericType && greeks.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            for (object i = 0; isLessThan(i, getArrayLength(greeks)); postFixIncrement(ref i))
            {
                object parsedTicker = this.parseGreeks(getValue(greeks, i));
                object greek = this.extend(parsedTicker, parameters);
                ((IList<object>)results).Add(greek);
            }
        } else
        {
            object marketIds = new List<object>(((IDictionary<string,object>)greeks).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
            {
                object marketId = getValue(marketIds, i);
                object market = this.safeMarket(marketId);
                object parsed = this.parseGreeks(getValue(greeks, marketId), market);
                object greek = this.extend(parsed, parameters);
                ((IList<object>)results).Add(greek);
            }
        }
        symbols = this.marketSymbols(symbols);
        return this.filterByArray(results, "symbol", symbols);
    }

    public virtual object parseOption(object chain, object currency = null, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseOption () is not supported yet")) ;
    }

    public virtual object parseOptionChain(object response, object currencyKey = null, object symbolKey = null)
    {
        object optionStructures = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object info = getValue(response, i);
            object currencyId = this.safeString(info, currencyKey);
            object currency = this.safeCurrency(currencyId);
            object marketId = this.safeString(info, symbolKey);
            object market = this.safeMarket(marketId, null, null, "option");
            ((IDictionary<string,object>)optionStructures)[(string)getValue(market, "symbol")] = this.parseOption(info, currency, market);
        }
        return optionStructures;
    }

    public virtual object parseMarginModes(object response, object symbols = null, object symbolKey = null, object marketType = null)
    {
        object marginModeStructures = new Dictionary<string, object>() {};
        if (isTrue(isEqual(marketType, null)))
        {
            marketType = "swap"; // default to swap
        }
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object info = getValue(response, i);
            object marketId = this.safeString(info, symbolKey);
            object market = this.safeMarket(marketId, null, null, marketType);
            if (isTrue(isTrue((isEqual(symbols, null))) || isTrue(this.inArray(getValue(market, "symbol"), symbols))))
            {
                ((IDictionary<string,object>)marginModeStructures)[(string)getValue(market, "symbol")] = this.parseMarginMode(info, market);
            }
        }
        return marginModeStructures;
    }

    public virtual object parseMarginMode(object marginMode, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseMarginMode () is not supported yet")) ;
    }

    public virtual object parseLeverages(object response, object symbols = null, object symbolKey = null, object marketType = null)
    {
        object leverageStructures = new Dictionary<string, object>() {};
        if (isTrue(isEqual(marketType, null)))
        {
            marketType = "swap"; // default to swap
        }
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object info = getValue(response, i);
            object marketId = this.safeString(info, symbolKey);
            object market = this.safeMarket(marketId, null, null, marketType);
            if (isTrue(isTrue((isEqual(symbols, null))) || isTrue(this.inArray(getValue(market, "symbol"), symbols))))
            {
                ((IDictionary<string,object>)leverageStructures)[(string)getValue(market, "symbol")] = this.parseLeverage(info, market);
            }
        }
        return leverageStructures;
    }

    public virtual object parseLeverage(object leverage, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseLeverage () is not supported yet")) ;
    }

    public virtual object parseConversions(object conversions, object code = null, object fromCurrencyKey = null, object toCurrencyKey = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        conversions = this.toArray(conversions);
        object result = new List<object>() {};
        object fromCurrency = null;
        object toCurrency = null;
        for (object i = 0; isLessThan(i, getArrayLength(conversions)); postFixIncrement(ref i))
        {
            object entry = getValue(conversions, i);
            object fromId = this.safeString(entry, fromCurrencyKey);
            object toId = this.safeString(entry, toCurrencyKey);
            if (isTrue(!isEqual(fromId, null)))
            {
                fromCurrency = this.safeCurrency(fromId);
            }
            if (isTrue(!isEqual(toId, null)))
            {
                toCurrency = this.safeCurrency(toId);
            }
            object conversion = this.extend(this.parseConversion(entry, fromCurrency, toCurrency), parameters);
            ((IList<object>)result).Add(conversion);
        }
        object sorted = this.sortBy(result, "timestamp");
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.safeCurrency(code);
            code = getValue(currency, "code");
        }
        if (isTrue(isEqual(code, null)))
        {
            return this.filterBySinceLimit(sorted, since, limit);
        }
        object fromConversion = this.filterBy(sorted, "fromCurrency", code);
        object toConversion = this.filterBy(sorted, "toCurrency", code);
        object both = this.arrayConcat(fromConversion, toConversion);
        return this.filterBySinceLimit(both, since, limit);
    }

    public virtual object parseConversion(object conversion, object fromCurrency = null, object toCurrency = null)
    {
        throw new NotSupported ((string)add(this.id, " parseConversion () is not supported yet")) ;
    }

    public virtual object convertExpireDate(object date)
    {
        // parse YYMMDD to datetime string
        object year = slice(date, 0, 2);
        object month = slice(date, 2, 4);
        object day = slice(date, 4, 6);
        object reconstructedDate = add(add(add(add(add(add("20", year), "-"), month), "-"), day), "T00:00:00Z");
        return reconstructedDate;
    }

    public virtual object convertExpireDateToMarketIdDate(object date)
    {
        // parse 240119 to 19JAN24
        object year = slice(date, 0, 2);
        object monthRaw = slice(date, 2, 4);
        object month = null;
        object day = slice(date, 4, 6);
        if (isTrue(isEqual(monthRaw, "01")))
        {
            month = "JAN";
        } else if (isTrue(isEqual(monthRaw, "02")))
        {
            month = "FEB";
        } else if (isTrue(isEqual(monthRaw, "03")))
        {
            month = "MAR";
        } else if (isTrue(isEqual(monthRaw, "04")))
        {
            month = "APR";
        } else if (isTrue(isEqual(monthRaw, "05")))
        {
            month = "MAY";
        } else if (isTrue(isEqual(monthRaw, "06")))
        {
            month = "JUN";
        } else if (isTrue(isEqual(monthRaw, "07")))
        {
            month = "JUL";
        } else if (isTrue(isEqual(monthRaw, "08")))
        {
            month = "AUG";
        } else if (isTrue(isEqual(monthRaw, "09")))
        {
            month = "SEP";
        } else if (isTrue(isEqual(monthRaw, "10")))
        {
            month = "OCT";
        } else if (isTrue(isEqual(monthRaw, "11")))
        {
            month = "NOV";
        } else if (isTrue(isEqual(monthRaw, "12")))
        {
            month = "DEC";
        }
        object reconstructedDate = add(add(day, month), year);
        return reconstructedDate;
    }

    public virtual object convertMarketIdExpireDate(object date)
    {
        // parse 03JAN24 to 240103.
        object monthMappping = new Dictionary<string, object>() {
            { "JAN", "01" },
            { "FEB", "02" },
            { "MAR", "03" },
            { "APR", "04" },
            { "MAY", "05" },
            { "JUN", "06" },
            { "JUL", "07" },
            { "AUG", "08" },
            { "SEP", "09" },
            { "OCT", "10" },
            { "NOV", "11" },
            { "DEC", "12" },
        };
        // if exchange omits first zero and provides i.e. '3JAN24' instead of '03JAN24'
        if (isTrue(isEqual(((string)date).Length, 6)))
        {
            date = add("0", date);
        }
        object year = slice(date, 0, 2);
        object monthName = slice(date, 2, 5);
        object month = this.safeString(monthMappping, monthName);
        object day = slice(date, 5, 7);
        object reconstructedDate = add(add(day, month), year);
        return reconstructedDate;
    }

    public async virtual Task<object> fetchPositionHistory(object symbol, object since = null, object limit = null, object parameters = null)
    {
        /**
        * @method
        * @name exchange#fetchPositionHistory
        * @description fetches the history of margin added or reduced from contract isolated positions
        * @param {string} [symbol] unified market symbol
        * @param {int} [since] timestamp in ms of the position
        * @param {int} [limit] the maximum amount of candles to fetch, default=1000
        * @param {object} params extra parameters specific to the exchange api endpoint
        * @returns {object[]} a list of [position structures]{@link https://docs.ccxt.com/#/?id=position-structure}
        */
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.has, "fetchPositionsHistory")))
        {
            object positions = await this.fetchPositionsHistory(new List<object>() {symbol}, since, limit, parameters);
            return positions;
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchPositionHistory () is not supported yet")) ;
        }
    }

    public async virtual Task<object> fetchPositionsHistory(object symbols = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchPositionsHistory () is not supported yet")) ;
    }

    public virtual object parseMarginModification(object data, object market = null)
    {
        throw new NotSupported ((string)add(this.id, " parseMarginModification() is not supported yet")) ;
    }

    public virtual object parseMarginModifications(object response, object symbols = null, object symbolKey = null, object marketType = null)
    {
        object marginModifications = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object info = getValue(response, i);
            object marketId = this.safeString(info, symbolKey);
            object market = this.safeMarket(marketId, null, null, marketType);
            if (isTrue(isTrue((isEqual(symbols, null))) || isTrue(this.inArray(getValue(market, "symbol"), symbols))))
            {
                ((IList<object>)marginModifications).Add(this.parseMarginModification(info, market));
            }
        }
        return marginModifications;
    }

    public async virtual Task<object> fetchTransfer(object id, object code = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTransfer () is not supported yet")) ;
    }

    public async virtual Task<object> fetchTransfers(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        throw new NotSupported ((string)add(this.id, " fetchTransfers () is not supported yet")) ;
    }

    public virtual void cleanUnsubscription(WebSocketClient client, object subHash, object unsubHash, object subHashIsPrefix = null)
    {
        subHashIsPrefix ??= false;
        if (isTrue(inOp(client.subscriptions, unsubHash)))
        {
            ((IDictionary<string,object>)client.subscriptions).Remove((string)unsubHash);
        }
        if (!isTrue(subHashIsPrefix))
        {
            if (isTrue(inOp(client.subscriptions, subHash)))
            {
                ((IDictionary<string,object>)client.subscriptions).Remove((string)subHash);
            }
            if (isTrue(inOp(client.futures, subHash)))
            {
                var error = new UnsubscribeError(add(add(this.id, " "), subHash));
                client.reject(error, subHash);
            }
        } else
        {
            object clientSubscriptions = new List<object>(((IDictionary<string,object>)client.subscriptions).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(clientSubscriptions)); postFixIncrement(ref i))
            {
                object sub = getValue(clientSubscriptions, i);
                if (isTrue(((string)sub).StartsWith(((string)subHash))))
                {
                    ((IDictionary<string,object>)client.subscriptions).Remove((string)sub);
                }
            }
            object clientFutures = new List<object>(((IDictionary<string, ccxt.Exchange.Future>)client.futures).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(clientFutures)); postFixIncrement(ref i))
            {
                object future = getValue(clientFutures, i);
                if (isTrue(((string)future).StartsWith(((string)subHash))))
                {
                    var error = new UnsubscribeError(add(add(this.id, " "), future));
                    client.reject(error, future);
                }
            }
        }
        // client.resolve(true, unsubHash);
    }

    public virtual void cleanCache(object subscription)
    {
        object topic = this.safeString(subscription, "topic");
        object symbols = this.safeList(subscription, "symbols", new List<object>() {});
        object symbolsLength = getArrayLength(symbols);
        if (isTrue(isEqual(topic, "ohlcv")))
        {
            object symbolsAndTimeFrames = this.safeList(subscription, "symbolsAndTimeframes", new List<object>() {});
            for (object i = 0; isLessThan(i, getArrayLength(symbolsAndTimeFrames)); postFixIncrement(ref i))
            {
                object symbolAndTimeFrame = getValue(symbolsAndTimeFrames, i);
                object symbol = this.safeString(symbolAndTimeFrame, 0);
                object timeframe = this.safeString(symbolAndTimeFrame, 1);
                if (isTrue(isTrue((!isEqual(this.ohlcvs, null))) && isTrue((inOp(this.ohlcvs, symbol)))))
                {
                    if (isTrue(inOp(getValue(this.ohlcvs, symbol), timeframe)))
                    {
                        ((IDictionary<string,object>)getValue(this.ohlcvs, symbol)).Remove((string)timeframe);
                    }
                }
            }
        } else if (isTrue(isGreaterThan(symbolsLength, 0)))
        {
            for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
            {
                object symbol = getValue(symbols, i);
                if (isTrue(isEqual(topic, "trades")))
                {
                    if (isTrue(inOp(this.trades, symbol)))
                    {
                        ((IDictionary<string,object>)this.trades).Remove((string)symbol);
                    }
                } else if (isTrue(isEqual(topic, "orderbook")))
                {
                    if (isTrue(inOp(this.orderbooks, symbol)))
                    {
                        ((IDictionary<string,object>)this.orderbooks).Remove((string)symbol);
                    }
                } else if (isTrue(isEqual(topic, "ticker")))
                {
                    if (isTrue(inOp(this.tickers, symbol)))
                    {
                        ((IDictionary<string,object>)this.tickers).Remove((string)symbol);
                    }
                }
            }
        } else
        {
            if (isTrue(isTrue(isEqual(topic, "myTrades")) && isTrue((!isEqual(this.myTrades, null)))))
            {
                this.myTrades = null;
            } else if (isTrue(isTrue(isEqual(topic, "orders")) && isTrue((!isEqual(this.orders, null)))))
            {
                this.orders = null;
            } else if (isTrue(isTrue(isEqual(topic, "ticker")) && isTrue((!isEqual(this.tickers, null)))))
            {
                object tickerSymbols = new List<object>(((IDictionary<string,object>)this.tickers).Keys);
                for (object i = 0; isLessThan(i, getArrayLength(tickerSymbols)); postFixIncrement(ref i))
                {
                    object tickerSymbol = getValue(tickerSymbols, i);
                    if (isTrue(inOp(this.tickers, tickerSymbol)))
                    {
                        ((IDictionary<string,object>)this.tickers).Remove((string)tickerSymbol);
                    }
                }
            }
        }
    }
}

