#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
币安直接API数据获取器
绕过CCXT，直接使用HTTP请求获取币安数据
"""

import requests
import json
import pandas as pd
from datetime import datetime
import time

class BinanceDirectAPI:
    def __init__(self):
        """初始化币安直接API客户端"""
        self.base_url = "https://api.binance.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def get_server_time(self):
        """获取服务器时间"""
        try:
            print("⏰ 获取服务器时间...")
            response = self.session.get(f"{self.base_url}/api/v3/time", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                server_time = datetime.fromtimestamp(data['serverTime'] / 1000)
                local_time = datetime.now()
                print(f"  ✅ 服务器时间: {server_time}")
                print(f"  📅 本地时间: {local_time}")
                print(f"  ⏱️ 时间差: {abs((server_time - local_time).total_seconds()):.1f}秒")
                return data['serverTime']
            else:
                print(f"  ❌ 获取失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"  ❌ 获取服务器时间失败: {e}")
            return None
    
    def get_exchange_info(self):
        """获取交易所信息"""
        try:
            print("\nℹ️ 获取交易所信息...")
            response = self.session.get(f"{self.base_url}/api/v3/exchangeInfo", timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ 交易所: {data.get('timezone', 'Binance')}")
                print(f"  📊 交易对数量: {len(data.get('symbols', []))}")
                
                # 统计不同类型的交易对
                symbols = data.get('symbols', [])
                usdt_pairs = [s for s in symbols if s['symbol'].endswith('USDT') and s['status'] == 'TRADING']
                btc_pairs = [s for s in symbols if s['symbol'].endswith('BTC') and s['status'] == 'TRADING']
                
                print(f"  💰 活跃USDT交易对: {len(usdt_pairs)}")
                print(f"  ₿ 活跃BTC交易对: {len(btc_pairs)}")
                
                return data
            else:
                print(f"  ❌ 获取失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"  ❌ 获取交易所信息失败: {e}")
            return None
    
    def get_24hr_tickers(self):
        """获取24小时行情统计"""
        try:
            print("\n📈 获取24小时行情统计...")
            response = self.session.get(f"{self.base_url}/api/v3/ticker/24hr", timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ 获取到 {len(data)} 个交易对的24小时行情")
                
                # 筛选USDT交易对并按交易量排序
                usdt_tickers = [
                    ticker for ticker in data 
                    if ticker['symbol'].endswith('USDT') and float(ticker['quoteVolume']) > 0
                ]
                
                # 按交易量排序
                usdt_tickers.sort(key=lambda x: float(x['quoteVolume']), reverse=True)
                
                print(f"\n🏆 交易量最大的10个USDT交易对:")
                for i, ticker in enumerate(usdt_tickers[:10], 1):
                    symbol = ticker['symbol']
                    price = float(ticker['lastPrice'])
                    volume = float(ticker['quoteVolume'])
                    change = float(ticker['priceChangePercent'])
                    
                    print(f"  {i:2d}. {symbol:12s} ${price:>12.4f} 量:${volume:>15,.0f} 涨跌:{change:>7.2f}%")
                
                return usdt_tickers[:50]  # 返回前50个
                
            else:
                print(f"  ❌ 获取失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"  ❌ 获取24小时行情失败: {e}")
            return []
    
    def get_orderbook(self, symbol, limit=20):
        """获取订单簿"""
        try:
            print(f"\n📖 获取 {symbol} 订单簿...")
            params = {'symbol': symbol, 'limit': limit}
            response = self.session.get(f"{self.base_url}/api/v3/depth", params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                bids = [[float(price), float(qty)] for price, qty in data['bids']]
                asks = [[float(price), float(qty)] for price, qty in data['asks']]
                
                print(f"  ✅ {symbol} 订单簿 (前5档):")
                print(f"    买盘 (Bids):")
                for i, (price, qty) in enumerate(bids[:5], 1):
                    print(f"      {i}. ${price:>10.4f} × {qty:>10.4f}")
                
                print(f"    卖盘 (Asks):")
                for i, (price, qty) in enumerate(asks[:5], 1):
                    print(f"      {i}. ${price:>10.4f} × {qty:>10.4f}")
                
                # 计算价差
                if bids and asks:
                    best_bid = bids[0][0]
                    best_ask = asks[0][0]
                    spread = best_ask - best_bid
                    spread_percent = (spread / best_bid) * 100
                    print(f"    价差: ${spread:.4f} ({spread_percent:.3f}%)")
                
                return {'symbol': symbol, 'bids': bids, 'asks': asks, 'timestamp': int(time.time() * 1000)}
                
            else:
                print(f"  ❌ 获取失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"  ❌ 获取 {symbol} 订单簿失败: {e}")
            return None
    
    def get_klines(self, symbol, interval='1h', limit=24):
        """获取K线数据"""
        try:
            print(f"\n📊 获取 {symbol} {interval} K线数据...")
            params = {'symbol': symbol, 'interval': interval, 'limit': limit}
            response = self.session.get(f"{self.base_url}/api/v3/klines", params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                klines = []
                for kline in data:
                    klines.append([
                        int(kline[0]),      # 开盘时间
                        float(kline[1]),    # 开盘价
                        float(kline[2]),    # 最高价
                        float(kline[3]),    # 最低价
                        float(kline[4]),    # 收盘价
                        float(kline[5]),    # 成交量
                    ])
                
                print(f"  ✅ 获取到 {len(klines)} 条K线数据")
                
                # 显示最近5条K线
                print(f"  最近5条K线:")
                for kline in klines[-5:]:
                    timestamp = kline[0]
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    open_price = kline[1]
                    high_price = kline[2]
                    low_price = kline[3]
                    close_price = kline[4]
                    volume = kline[5]
                    
                    change = ((close_price - open_price) / open_price) * 100 if open_price > 0 else 0
                    print(f"    {dt.strftime('%m-%d %H:%M')} O:{open_price:>8.2f} H:{high_price:>8.2f} L:{low_price:>8.2f} C:{close_price:>8.2f} V:{volume:>8.1f} ({change:+5.2f}%)")
                
                return klines
                
            else:
                print(f"  ❌ 获取失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"  ❌ 获取 {symbol} K线数据失败: {e}")
            return []
    
    def get_recent_trades(self, symbol, limit=100):
        """获取最近交易"""
        try:
            print(f"\n💱 获取 {symbol} 最近交易...")
            params = {'symbol': symbol, 'limit': limit}
            response = self.session.get(f"{self.base_url}/api/v3/trades", params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                trades = []
                for trade in data:
                    trades.append({
                        'id': trade['id'],
                        'price': float(trade['price']),
                        'qty': float(trade['qty']),
                        'time': trade['time'],
                        'isBuyerMaker': trade['isBuyerMaker']
                    })
                
                print(f"  ✅ 获取到 {len(trades)} 条最近交易")
                
                # 显示最近5笔交易
                print(f"  最近5笔交易:")
                for trade in trades[-5:]:
                    dt = datetime.fromtimestamp(trade['time'] / 1000)
                    side = "卖" if trade['isBuyerMaker'] else "买"
                    print(f"    {dt.strftime('%H:%M:%S')} {side} ${trade['price']:>8.4f} × {trade['qty']:>8.4f}")
                
                return trades
                
            else:
                print(f"  ❌ 获取失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"  ❌ 获取 {symbol} 最近交易失败: {e}")
            return []
    
    def save_data_to_files(self, tickers, orderbooks, klines_data):
        """保存数据到文件"""
        try:
            print(f"\n💾 保存数据到文件...")
            
            # 保存行情数据
            if tickers:
                ticker_data = []
                for ticker in tickers:
                    ticker_data.append({
                        'symbol': ticker['symbol'],
                        'price': float(ticker['lastPrice']),
                        'high_24h': float(ticker['highPrice']),
                        'low_24h': float(ticker['lowPrice']),
                        'volume_24h': float(ticker['volume']),
                        'volume_24h_usdt': float(ticker['quoteVolume']),
                        'change_24h': float(ticker['priceChange']),
                        'change_24h_percent': float(ticker['priceChangePercent']),
                        'bid_price': float(ticker['bidPrice']),
                        'ask_price': float(ticker['askPrice']),
                        'count': int(ticker['count']),
                        'open_time': int(ticker['openTime']),
                        'close_time': int(ticker['closeTime'])
                    })
                
                df = pd.DataFrame(ticker_data)
                filename = f'binance_direct_tickers_{self.timestamp}.csv'
                df.to_csv(filename, index=False, encoding='utf-8')
                print(f"  ✅ 行情数据: {filename}")
            
            # 保存订单簿数据
            if orderbooks:
                orderbook_data = []
                for orderbook in orderbooks:
                    if orderbook:
                        symbol = orderbook['symbol']
                        
                        # 买盘
                        for i, (price, qty) in enumerate(orderbook['bids'][:10]):
                            orderbook_data.append({
                                'symbol': symbol,
                                'side': 'bid',
                                'level': i + 1,
                                'price': price,
                                'quantity': qty,
                                'timestamp': orderbook['timestamp']
                            })
                        
                        # 卖盘
                        for i, (price, qty) in enumerate(orderbook['asks'][:10]):
                            orderbook_data.append({
                                'symbol': symbol,
                                'side': 'ask',
                                'level': i + 1,
                                'price': price,
                                'quantity': qty,
                                'timestamp': orderbook['timestamp']
                            })
                
                if orderbook_data:
                    df = pd.DataFrame(orderbook_data)
                    filename = f'binance_direct_orderbooks_{self.timestamp}.csv'
                    df.to_csv(filename, index=False, encoding='utf-8')
                    print(f"  ✅ 订单簿数据: {filename}")
            
            # 保存K线数据
            if klines_data:
                kline_data = []
                for symbol, klines in klines_data.items():
                    for kline in klines:
                        kline_data.append({
                            'symbol': symbol,
                            'timestamp': kline[0],
                            'datetime': datetime.fromtimestamp(kline[0] / 1000).isoformat(),
                            'open': kline[1],
                            'high': kline[2],
                            'low': kline[3],
                            'close': kline[4],
                            'volume': kline[5]
                        })
                
                if kline_data:
                    df = pd.DataFrame(kline_data)
                    filename = f'binance_direct_klines_{self.timestamp}.csv'
                    df.to_csv(filename, index=False, encoding='utf-8')
                    print(f"  ✅ K线数据: {filename}")
            
            print(f"💾 数据保存完成！")
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def collect_all_data(self):
        """收集所有数据"""
        print("🚀 开始收集币安真实数据 (直接API)")
        print(f"📅 时间: {datetime.now()}")
        print("=" * 60)
        
        try:
            # 1. 获取服务器时间
            server_time = self.get_server_time()
            if not server_time:
                print("❌ 无法获取服务器时间，程序退出")
                return None
            
            # 2. 获取交易所信息
            exchange_info = self.get_exchange_info()
            
            # 3. 获取24小时行情
            tickers = self.get_24hr_tickers()
            if not tickers:
                print("❌ 无法获取行情数据")
                return None
            
            # 4. 获取前3个交易对的订单簿
            top_symbols = [ticker['symbol'] for ticker in tickers[:3]]
            orderbooks = []
            for symbol in top_symbols:
                orderbook = self.get_orderbook(symbol)
                if orderbook:
                    orderbooks.append(orderbook)
                time.sleep(0.2)  # 避免触发速率限制
            
            # 5. 获取前2个交易对的K线数据
            klines_data = {}
            for symbol in top_symbols[:2]:
                klines = self.get_klines(symbol, '1h', 24)
                if klines:
                    klines_data[symbol] = klines
                time.sleep(0.2)
            
            # 6. 保存数据
            self.save_data_to_files(tickers, orderbooks, klines_data)
            
            print("=" * 60)
            print("🎉 真实数据收集完成！")
            print(f"  - 行情数据: {len(tickers)} 个USDT交易对")
            print(f"  - 订单簿: {len(orderbooks)} 个交易对")
            print(f"  - K线数据: {len(klines_data)} 个交易对")
            print(f"  - 数据文件前缀: binance_direct_*_{self.timestamp}.csv")
            
            return {
                'tickers': tickers,
                'orderbooks': orderbooks,
                'klines': klines_data
            }
            
        except Exception as e:
            print(f"❌ 数据收集失败: {e}")
            return None

def main():
    """主函数"""
    print("💰 币安直接API数据获取器")
    print("绕过CCXT，直接使用HTTP请求")
    print("=" * 60)
    
    api = BinanceDirectAPI()
    data = api.collect_all_data()
    
    if data:
        print("\n✅ 数据收集成功！")
        print("📁 请查看当前目录中的 CSV 文件获取详细数据")
        print("💡 这些是真实的币安数据！")
    else:
        print("\n❌ 数据收集失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")
