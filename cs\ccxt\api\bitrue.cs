// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bitrue : Exchange
{
    public bitrue (object args = null): base(args) {}

    public async Task<object> spotKlinePublicGetPublicJson (object parameters = null)
    {
        return await this.callAsync ("spotKlinePublicGetPublicJson",parameters);
    }

    public async Task<object> spotKlinePublicGetPublicCurrencyJson (object parameters = null)
    {
        return await this.callAsync ("spotKlinePublicGetPublicCurrencyJson",parameters);
    }

    public async Task<object> spotV1PublicGetPing (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetPing",parameters);
    }

    public async Task<object> spotV1PublicGetTime (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetTime",parameters);
    }

    public async Task<object> spotV1PublicGetExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetExchangeInfo",parameters);
    }

    public async Task<object> spotV1PublicGetDepth (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetDepth",parameters);
    }

    public async Task<object> spotV1PublicGetTrades (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetTrades",parameters);
    }

    public async Task<object> spotV1PublicGetHistoricalTrades (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetHistoricalTrades",parameters);
    }

    public async Task<object> spotV1PublicGetAggTrades (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetAggTrades",parameters);
    }

    public async Task<object> spotV1PublicGetTicker24hr (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetTicker24hr",parameters);
    }

    public async Task<object> spotV1PublicGetTickerPrice (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetTickerPrice",parameters);
    }

    public async Task<object> spotV1PublicGetTickerBookTicker (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetTickerBookTicker",parameters);
    }

    public async Task<object> spotV1PublicGetMarketKline (object parameters = null)
    {
        return await this.callAsync ("spotV1PublicGetMarketKline",parameters);
    }

    public async Task<object> spotV1PrivateGetOrder (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivateGetOrder",parameters);
    }

    public async Task<object> spotV1PrivateGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivateGetOpenOrders",parameters);
    }

    public async Task<object> spotV1PrivateGetAllOrders (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivateGetAllOrders",parameters);
    }

    public async Task<object> spotV1PrivateGetAccount (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivateGetAccount",parameters);
    }

    public async Task<object> spotV1PrivateGetMyTrades (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivateGetMyTrades",parameters);
    }

    public async Task<object> spotV1PrivateGetEtfNetValueSymbol (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivateGetEtfNetValueSymbol",parameters);
    }

    public async Task<object> spotV1PrivateGetWithdrawHistory (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivateGetWithdrawHistory",parameters);
    }

    public async Task<object> spotV1PrivateGetDepositHistory (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivateGetDepositHistory",parameters);
    }

    public async Task<object> spotV1PrivatePostOrder (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivatePostOrder",parameters);
    }

    public async Task<object> spotV1PrivatePostWithdrawCommit (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivatePostWithdrawCommit",parameters);
    }

    public async Task<object> spotV1PrivateDeleteOrder (object parameters = null)
    {
        return await this.callAsync ("spotV1PrivateDeleteOrder",parameters);
    }

    public async Task<object> spotV2PrivateGetMyTrades (object parameters = null)
    {
        return await this.callAsync ("spotV2PrivateGetMyTrades",parameters);
    }

    public async Task<object> fapiV1PublicGetPing (object parameters = null)
    {
        return await this.callAsync ("fapiV1PublicGetPing",parameters);
    }

    public async Task<object> fapiV1PublicGetTime (object parameters = null)
    {
        return await this.callAsync ("fapiV1PublicGetTime",parameters);
    }

    public async Task<object> fapiV1PublicGetContracts (object parameters = null)
    {
        return await this.callAsync ("fapiV1PublicGetContracts",parameters);
    }

    public async Task<object> fapiV1PublicGetDepth (object parameters = null)
    {
        return await this.callAsync ("fapiV1PublicGetDepth",parameters);
    }

    public async Task<object> fapiV1PublicGetTicker (object parameters = null)
    {
        return await this.callAsync ("fapiV1PublicGetTicker",parameters);
    }

    public async Task<object> fapiV1PublicGetKlines (object parameters = null)
    {
        return await this.callAsync ("fapiV1PublicGetKlines",parameters);
    }

    public async Task<object> fapiV2PrivateGetMyTrades (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivateGetMyTrades",parameters);
    }

    public async Task<object> fapiV2PrivateGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivateGetOpenOrders",parameters);
    }

    public async Task<object> fapiV2PrivateGetOrder (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivateGetOrder",parameters);
    }

    public async Task<object> fapiV2PrivateGetAccount (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivateGetAccount",parameters);
    }

    public async Task<object> fapiV2PrivateGetLeverageBracket (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivateGetLeverageBracket",parameters);
    }

    public async Task<object> fapiV2PrivateGetCommissionRate (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivateGetCommissionRate",parameters);
    }

    public async Task<object> fapiV2PrivateGetFuturesTransferHistory (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivateGetFuturesTransferHistory",parameters);
    }

    public async Task<object> fapiV2PrivateGetForceOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivateGetForceOrdersHistory",parameters);
    }

    public async Task<object> fapiV2PrivatePostPositionMargin (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivatePostPositionMargin",parameters);
    }

    public async Task<object> fapiV2PrivatePostLevelEdit (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivatePostLevelEdit",parameters);
    }

    public async Task<object> fapiV2PrivatePostCancel (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivatePostCancel",parameters);
    }

    public async Task<object> fapiV2PrivatePostOrder (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivatePostOrder",parameters);
    }

    public async Task<object> fapiV2PrivatePostAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivatePostAllOpenOrders",parameters);
    }

    public async Task<object> fapiV2PrivatePostFuturesTransfer (object parameters = null)
    {
        return await this.callAsync ("fapiV2PrivatePostFuturesTransfer",parameters);
    }

    public async Task<object> dapiV1PublicGetPing (object parameters = null)
    {
        return await this.callAsync ("dapiV1PublicGetPing",parameters);
    }

    public async Task<object> dapiV1PublicGetTime (object parameters = null)
    {
        return await this.callAsync ("dapiV1PublicGetTime",parameters);
    }

    public async Task<object> dapiV1PublicGetContracts (object parameters = null)
    {
        return await this.callAsync ("dapiV1PublicGetContracts",parameters);
    }

    public async Task<object> dapiV1PublicGetDepth (object parameters = null)
    {
        return await this.callAsync ("dapiV1PublicGetDepth",parameters);
    }

    public async Task<object> dapiV1PublicGetTicker (object parameters = null)
    {
        return await this.callAsync ("dapiV1PublicGetTicker",parameters);
    }

    public async Task<object> dapiV1PublicGetKlines (object parameters = null)
    {
        return await this.callAsync ("dapiV1PublicGetKlines",parameters);
    }

    public async Task<object> dapiV2PrivateGetMyTrades (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivateGetMyTrades",parameters);
    }

    public async Task<object> dapiV2PrivateGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivateGetOpenOrders",parameters);
    }

    public async Task<object> dapiV2PrivateGetOrder (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivateGetOrder",parameters);
    }

    public async Task<object> dapiV2PrivateGetAccount (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivateGetAccount",parameters);
    }

    public async Task<object> dapiV2PrivateGetLeverageBracket (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivateGetLeverageBracket",parameters);
    }

    public async Task<object> dapiV2PrivateGetCommissionRate (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivateGetCommissionRate",parameters);
    }

    public async Task<object> dapiV2PrivateGetFuturesTransferHistory (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivateGetFuturesTransferHistory",parameters);
    }

    public async Task<object> dapiV2PrivateGetForceOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivateGetForceOrdersHistory",parameters);
    }

    public async Task<object> dapiV2PrivatePostPositionMargin (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivatePostPositionMargin",parameters);
    }

    public async Task<object> dapiV2PrivatePostLevelEdit (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivatePostLevelEdit",parameters);
    }

    public async Task<object> dapiV2PrivatePostCancel (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivatePostCancel",parameters);
    }

    public async Task<object> dapiV2PrivatePostOrder (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivatePostOrder",parameters);
    }

    public async Task<object> dapiV2PrivatePostAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivatePostAllOpenOrders",parameters);
    }

    public async Task<object> dapiV2PrivatePostFuturesTransfer (object parameters = null)
    {
        return await this.callAsync ("dapiV2PrivatePostFuturesTransfer",parameters);
    }

    public async Task<object> openV1PrivatePostPoseidonApiV1ListenKey (object parameters = null)
    {
        return await this.callAsync ("openV1PrivatePostPoseidonApiV1ListenKey",parameters);
    }

    public async Task<object> openV1PrivatePutPoseidonApiV1ListenKeyListenKey (object parameters = null)
    {
        return await this.callAsync ("openV1PrivatePutPoseidonApiV1ListenKeyListenKey",parameters);
    }

    public async Task<object> openV1PrivateDeletePoseidonApiV1ListenKeyListenKey (object parameters = null)
    {
        return await this.callAsync ("openV1PrivateDeletePoseidonApiV1ListenKeyListenKey",parameters);
    }

}