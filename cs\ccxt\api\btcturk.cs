// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class btcturk : Exchange
{
    public btcturk (object args = null): base(args) {}

    public async Task<object> publicGetOrderbook (object parameters = null)
    {
        return await this.callAsync ("publicGetOrderbook",parameters);
    }

    public async Task<object> publicGetTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetTicker",parameters);
    }

    public async Task<object> publicGetTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetTrades",parameters);
    }

    public async Task<object> publicGetOhlc (object parameters = null)
    {
        return await this.callAsync ("publicGetOhlc",parameters);
    }

    public async Task<object> publicGetServerExchangeinfo (object parameters = null)
    {
        return await this.callAsync ("publicGetServerExchangeinfo",parameters);
    }

    public async Task<object> privateGetUsersBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersBalances",parameters);
    }

    public async Task<object> privateGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenOrders",parameters);
    }

    public async Task<object> privateGetAllOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetAllOrders",parameters);
    }

    public async Task<object> privateGetUsersTransactionsTrade (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersTransactionsTrade",parameters);
    }

    public async Task<object> privatePostUsersTransactionsCrypto (object parameters = null)
    {
        return await this.callAsync ("privatePostUsersTransactionsCrypto",parameters);
    }

    public async Task<object> privatePostUsersTransactionsFiat (object parameters = null)
    {
        return await this.callAsync ("privatePostUsersTransactionsFiat",parameters);
    }

    public async Task<object> privatePostOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostOrder",parameters);
    }

    public async Task<object> privatePostCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelOrder",parameters);
    }

    public async Task<object> privateDeleteOrder (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrder",parameters);
    }

    public async Task<object> graphGetOhlcs (object parameters = null)
    {
        return await this.callAsync ("graphGetOhlcs",parameters);
    }

    public async Task<object> graphGetKlinesHistory (object parameters = null)
    {
        return await this.callAsync ("graphGetKlinesHistory",parameters);
    }

}