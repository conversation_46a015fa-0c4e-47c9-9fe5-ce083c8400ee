# CCXT Certification Program · ![CCXT Certified](https://img.shields.io/badge/CCXT-certified-green.svg)

The structure of CCXT defines a good, portable and cross-compatible standard for exchanges' API interfaces, that is implemented in the CCXT Unified API. Exchanges are welcome to apply for our certification program. Certification is technically supervised and quality-assured by members of the CCXT Dev Team. That implies that an exchange having a "certified" badge is properly implemented and tested by the authors of CCXT. Certification means less bugs, more functionality, priority support and a much more stable and efficient implementation in general.

## Requirements

Getting integrated and certified requires the exchange to implement a quality API. Please, see the full list of technical requirements here: https://github.com/ccxt/ccxt/wiki/Requirements

## Contact Us

For inquiries on getting your exchange integrated, listed and certified: <EMAIL>
