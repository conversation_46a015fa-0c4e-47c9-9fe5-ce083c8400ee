// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class coinmetro : Exchange
{
    public coinmetro (object args = null): base(args) {}

    public async Task<object> publicGetDemoTemp (object parameters = null)
    {
        return await this.callAsync ("publicGetDemoTemp",parameters);
    }

    public async Task<object> publicGetExchangeCandlesPairTimeframeFromTo (object parameters = null)
    {
        return await this.callAsync ("publicGetExchangeCandlesPairTimeframeFromTo",parameters);
    }

    public async Task<object> publicGetExchangePrices (object parameters = null)
    {
        return await this.callAsync ("publicGetExchangePrices",parameters);
    }

    public async Task<object> publicGetExchangeTicksPairFrom (object parameters = null)
    {
        return await this.callAsync ("publicGetExchangeTicksPairFrom",parameters);
    }

    public async Task<object> publicGetAssets (object parameters = null)
    {
        return await this.callAsync ("publicGetAssets",parameters);
    }

    public async Task<object> publicGetMarkets (object parameters = null)
    {
        return await this.callAsync ("publicGetMarkets",parameters);
    }

    public async Task<object> publicGetExchangeBookPair (object parameters = null)
    {
        return await this.callAsync ("publicGetExchangeBookPair",parameters);
    }

    public async Task<object> publicGetExchangeBookUpdatesPairFrom (object parameters = null)
    {
        return await this.callAsync ("publicGetExchangeBookUpdatesPairFrom",parameters);
    }

    public async Task<object> privateGetUsersBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersBalances",parameters);
    }

    public async Task<object> privateGetUsersWallets (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersWallets",parameters);
    }

    public async Task<object> privateGetUsersWalletsHistorySince (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersWalletsHistorySince",parameters);
    }

    public async Task<object> privateGetExchangeOrdersStatusOrderID (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrdersStatusOrderID",parameters);
    }

    public async Task<object> privateGetExchangeOrdersActive (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrdersActive",parameters);
    }

    public async Task<object> privateGetExchangeOrdersHistorySince (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrdersHistorySince",parameters);
    }

    public async Task<object> privateGetExchangeFillsSince (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeFillsSince",parameters);
    }

    public async Task<object> privateGetExchangeMargin (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeMargin",parameters);
    }

    public async Task<object> privatePostJwt (object parameters = null)
    {
        return await this.callAsync ("privatePostJwt",parameters);
    }

    public async Task<object> privatePostJwtDevice (object parameters = null)
    {
        return await this.callAsync ("privatePostJwtDevice",parameters);
    }

    public async Task<object> privatePostDevices (object parameters = null)
    {
        return await this.callAsync ("privatePostDevices",parameters);
    }

    public async Task<object> privatePostJwtReadOnly (object parameters = null)
    {
        return await this.callAsync ("privatePostJwtReadOnly",parameters);
    }

    public async Task<object> privatePostExchangeOrdersCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeOrdersCreate",parameters);
    }

    public async Task<object> privatePostExchangeOrdersModifyOrderID (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeOrdersModifyOrderID",parameters);
    }

    public async Task<object> privatePostExchangeSwap (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeSwap",parameters);
    }

    public async Task<object> privatePostExchangeSwapConfirmSwapId (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeSwapConfirmSwapId",parameters);
    }

    public async Task<object> privatePostExchangeOrdersCloseOrderID (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeOrdersCloseOrderID",parameters);
    }

    public async Task<object> privatePostExchangeOrdersHedge (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeOrdersHedge",parameters);
    }

    public async Task<object> privatePutJwt (object parameters = null)
    {
        return await this.callAsync ("privatePutJwt",parameters);
    }

    public async Task<object> privatePutExchangeOrdersCancelOrderID (object parameters = null)
    {
        return await this.callAsync ("privatePutExchangeOrdersCancelOrderID",parameters);
    }

    public async Task<object> privatePutUsersMarginCollateral (object parameters = null)
    {
        return await this.callAsync ("privatePutUsersMarginCollateral",parameters);
    }

    public async Task<object> privatePutUsersMarginPrimaryCurrency (object parameters = null)
    {
        return await this.callAsync ("privatePutUsersMarginPrimaryCurrency",parameters);
    }

}