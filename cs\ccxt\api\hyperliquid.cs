// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class hyperliquid : Exchange
{
    public hyperliquid (object args = null): base(args) {}

    public async Task<object> publicPostInfo (object parameters = null)
    {
        return await this.callAsync ("publicPostInfo",parameters);
    }

    public async Task<object> privatePostExchange (object parameters = null)
    {
        return await this.callAsync ("privatePostExchange",parameters);
    }

}