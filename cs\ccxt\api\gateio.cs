// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class gateio : gate
{
    public gateio (object args = null): base(args) {}

    public async Task<object> publicWalletGetCurrencyChains (object parameters = null)
    {
        return await this.callAsync ("publicWalletGetCurrencyChains",parameters);
    }

    public async Task<object> publicSpotGetCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetCurrencies",parameters);
    }

    public async Task<object> publicSpotGetCurrenciesCurrency (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetCurrenciesCurrency",parameters);
    }

    public async Task<object> publicSpotGetCurrencyPairs (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetCurrencyPairs",parameters);
    }

    public async Task<object> publicSpotGetCurrencyPairsCurrencyPair (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetCurrencyPairsCurrencyPair",parameters);
    }

    public async Task<object> publicSpotGetTickers (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetTickers",parameters);
    }

    public async Task<object> publicSpotGetOrderBook (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetOrderBook",parameters);
    }

    public async Task<object> publicSpotGetTrades (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetTrades",parameters);
    }

    public async Task<object> publicSpotGetCandlesticks (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetCandlesticks",parameters);
    }

    public async Task<object> publicSpotGetTime (object parameters = null)
    {
        return await this.callAsync ("publicSpotGetTime",parameters);
    }

    public async Task<object> publicMarginGetCurrencyPairs (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetCurrencyPairs",parameters);
    }

    public async Task<object> publicMarginGetCurrencyPairsCurrencyPair (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetCurrencyPairsCurrencyPair",parameters);
    }

    public async Task<object> publicMarginGetFundingBook (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetFundingBook",parameters);
    }

    public async Task<object> publicMarginGetCrossCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetCrossCurrencies",parameters);
    }

    public async Task<object> publicMarginGetCrossCurrenciesCurrency (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetCrossCurrenciesCurrency",parameters);
    }

    public async Task<object> publicMarginGetUniCurrencyPairs (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetUniCurrencyPairs",parameters);
    }

    public async Task<object> publicMarginGetUniCurrencyPairsCurrencyPair (object parameters = null)
    {
        return await this.callAsync ("publicMarginGetUniCurrencyPairsCurrencyPair",parameters);
    }

    public async Task<object> publicFlash_swapGetCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicFlash_swapGetCurrencies",parameters);
    }

    public async Task<object> publicFuturesGetSettleContracts (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleContracts",parameters);
    }

    public async Task<object> publicFuturesGetSettleContractsContract (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleContractsContract",parameters);
    }

    public async Task<object> publicFuturesGetSettleOrderBook (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleOrderBook",parameters);
    }

    public async Task<object> publicFuturesGetSettleTrades (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleTrades",parameters);
    }

    public async Task<object> publicFuturesGetSettleCandlesticks (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleCandlesticks",parameters);
    }

    public async Task<object> publicFuturesGetSettlePremiumIndex (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettlePremiumIndex",parameters);
    }

    public async Task<object> publicFuturesGetSettleTickers (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleTickers",parameters);
    }

    public async Task<object> publicFuturesGetSettleFundingRate (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleFundingRate",parameters);
    }

    public async Task<object> publicFuturesGetSettleInsurance (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleInsurance",parameters);
    }

    public async Task<object> publicFuturesGetSettleContractStats (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleContractStats",parameters);
    }

    public async Task<object> publicFuturesGetSettleIndexConstituentsIndex (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleIndexConstituentsIndex",parameters);
    }

    public async Task<object> publicFuturesGetSettleLiqOrders (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleLiqOrders",parameters);
    }

    public async Task<object> publicFuturesGetSettleRiskLimitTiers (object parameters = null)
    {
        return await this.callAsync ("publicFuturesGetSettleRiskLimitTiers",parameters);
    }

    public async Task<object> publicDeliveryGetSettleContracts (object parameters = null)
    {
        return await this.callAsync ("publicDeliveryGetSettleContracts",parameters);
    }

    public async Task<object> publicDeliveryGetSettleContractsContract (object parameters = null)
    {
        return await this.callAsync ("publicDeliveryGetSettleContractsContract",parameters);
    }

    public async Task<object> publicDeliveryGetSettleOrderBook (object parameters = null)
    {
        return await this.callAsync ("publicDeliveryGetSettleOrderBook",parameters);
    }

    public async Task<object> publicDeliveryGetSettleTrades (object parameters = null)
    {
        return await this.callAsync ("publicDeliveryGetSettleTrades",parameters);
    }

    public async Task<object> publicDeliveryGetSettleCandlesticks (object parameters = null)
    {
        return await this.callAsync ("publicDeliveryGetSettleCandlesticks",parameters);
    }

    public async Task<object> publicDeliveryGetSettleTickers (object parameters = null)
    {
        return await this.callAsync ("publicDeliveryGetSettleTickers",parameters);
    }

    public async Task<object> publicDeliveryGetSettleInsurance (object parameters = null)
    {
        return await this.callAsync ("publicDeliveryGetSettleInsurance",parameters);
    }

    public async Task<object> publicOptionsGetUnderlyings (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetUnderlyings",parameters);
    }

    public async Task<object> publicOptionsGetExpirations (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetExpirations",parameters);
    }

    public async Task<object> publicOptionsGetContracts (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetContracts",parameters);
    }

    public async Task<object> publicOptionsGetContractsContract (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetContractsContract",parameters);
    }

    public async Task<object> publicOptionsGetSettlements (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetSettlements",parameters);
    }

    public async Task<object> publicOptionsGetSettlementsContract (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetSettlementsContract",parameters);
    }

    public async Task<object> publicOptionsGetOrderBook (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetOrderBook",parameters);
    }

    public async Task<object> publicOptionsGetTickers (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetTickers",parameters);
    }

    public async Task<object> publicOptionsGetUnderlyingTickersUnderlying (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetUnderlyingTickersUnderlying",parameters);
    }

    public async Task<object> publicOptionsGetCandlesticks (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetCandlesticks",parameters);
    }

    public async Task<object> publicOptionsGetUnderlyingCandlesticks (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetUnderlyingCandlesticks",parameters);
    }

    public async Task<object> publicOptionsGetTrades (object parameters = null)
    {
        return await this.callAsync ("publicOptionsGetTrades",parameters);
    }

    public async Task<object> publicEarnGetUniCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicEarnGetUniCurrencies",parameters);
    }

    public async Task<object> publicEarnGetUniCurrenciesCurrency (object parameters = null)
    {
        return await this.callAsync ("publicEarnGetUniCurrenciesCurrency",parameters);
    }

    public async Task<object> privateWithdrawalsPostWithdrawals (object parameters = null)
    {
        return await this.callAsync ("privateWithdrawalsPostWithdrawals",parameters);
    }

    public async Task<object> privateWithdrawalsPostPush (object parameters = null)
    {
        return await this.callAsync ("privateWithdrawalsPostPush",parameters);
    }

    public async Task<object> privateWithdrawalsDeleteWithdrawalsWithdrawalId (object parameters = null)
    {
        return await this.callAsync ("privateWithdrawalsDeleteWithdrawalsWithdrawalId",parameters);
    }

    public async Task<object> privateWalletGetDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetDepositAddress",parameters);
    }

    public async Task<object> privateWalletGetWithdrawals (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetWithdrawals",parameters);
    }

    public async Task<object> privateWalletGetDeposits (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetDeposits",parameters);
    }

    public async Task<object> privateWalletGetSubAccountTransfers (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetSubAccountTransfers",parameters);
    }

    public async Task<object> privateWalletGetOrderStatus (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetOrderStatus",parameters);
    }

    public async Task<object> privateWalletGetWithdrawStatus (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetWithdrawStatus",parameters);
    }

    public async Task<object> privateWalletGetSubAccountBalances (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetSubAccountBalances",parameters);
    }

    public async Task<object> privateWalletGetSubAccountMarginBalances (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetSubAccountMarginBalances",parameters);
    }

    public async Task<object> privateWalletGetSubAccountFuturesBalances (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetSubAccountFuturesBalances",parameters);
    }

    public async Task<object> privateWalletGetSubAccountCrossMarginBalances (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetSubAccountCrossMarginBalances",parameters);
    }

    public async Task<object> privateWalletGetSavedAddress (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetSavedAddress",parameters);
    }

    public async Task<object> privateWalletGetFee (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetFee",parameters);
    }

    public async Task<object> privateWalletGetTotalBalance (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetTotalBalance",parameters);
    }

    public async Task<object> privateWalletGetSmallBalance (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetSmallBalance",parameters);
    }

    public async Task<object> privateWalletGetSmallBalanceHistory (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetSmallBalanceHistory",parameters);
    }

    public async Task<object> privateWalletGetPush (object parameters = null)
    {
        return await this.callAsync ("privateWalletGetPush",parameters);
    }

    public async Task<object> privateWalletPostTransfers (object parameters = null)
    {
        return await this.callAsync ("privateWalletPostTransfers",parameters);
    }

    public async Task<object> privateWalletPostSubAccountTransfers (object parameters = null)
    {
        return await this.callAsync ("privateWalletPostSubAccountTransfers",parameters);
    }

    public async Task<object> privateWalletPostSubAccountToSubAccount (object parameters = null)
    {
        return await this.callAsync ("privateWalletPostSubAccountToSubAccount",parameters);
    }

    public async Task<object> privateWalletPostSmallBalance (object parameters = null)
    {
        return await this.callAsync ("privateWalletPostSmallBalance",parameters);
    }

    public async Task<object> privateSubAccountsGetSubAccounts (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsGetSubAccounts",parameters);
    }

    public async Task<object> privateSubAccountsGetSubAccountsUserId (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsGetSubAccountsUserId",parameters);
    }

    public async Task<object> privateSubAccountsGetSubAccountsUserIdKeys (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsGetSubAccountsUserIdKeys",parameters);
    }

    public async Task<object> privateSubAccountsGetSubAccountsUserIdKeysKey (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsGetSubAccountsUserIdKeysKey",parameters);
    }

    public async Task<object> privateSubAccountsPostSubAccounts (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsPostSubAccounts",parameters);
    }

    public async Task<object> privateSubAccountsPostSubAccountsUserIdKeys (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsPostSubAccountsUserIdKeys",parameters);
    }

    public async Task<object> privateSubAccountsPostSubAccountsUserIdLock (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsPostSubAccountsUserIdLock",parameters);
    }

    public async Task<object> privateSubAccountsPostSubAccountsUserIdUnlock (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsPostSubAccountsUserIdUnlock",parameters);
    }

    public async Task<object> privateSubAccountsPutSubAccountsUserIdKeysKey (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsPutSubAccountsUserIdKeysKey",parameters);
    }

    public async Task<object> privateSubAccountsDeleteSubAccountsUserIdKeysKey (object parameters = null)
    {
        return await this.callAsync ("privateSubAccountsDeleteSubAccountsUserIdKeysKey",parameters);
    }

    public async Task<object> privateUnifiedGetAccounts (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetAccounts",parameters);
    }

    public async Task<object> privateUnifiedGetAccountMode (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetAccountMode",parameters);
    }

    public async Task<object> privateUnifiedGetBorrowable (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetBorrowable",parameters);
    }

    public async Task<object> privateUnifiedGetTransferable (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetTransferable",parameters);
    }

    public async Task<object> privateUnifiedGetLoans (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetLoans",parameters);
    }

    public async Task<object> privateUnifiedGetLoanRecords (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetLoanRecords",parameters);
    }

    public async Task<object> privateUnifiedGetInterestRecords (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetInterestRecords",parameters);
    }

    public async Task<object> privateUnifiedGetEstimateRate (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetEstimateRate",parameters);
    }

    public async Task<object> privateUnifiedGetCurrencyDiscountTiers (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetCurrencyDiscountTiers",parameters);
    }

    public async Task<object> privateUnifiedGetRiskUnits (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetRiskUnits",parameters);
    }

    public async Task<object> privateUnifiedGetUnifiedMode (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetUnifiedMode",parameters);
    }

    public async Task<object> privateUnifiedGetLoanMarginTiers (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetLoanMarginTiers",parameters);
    }

    public async Task<object> privateUnifiedGetLeverageUserCurrencyConfig (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetLeverageUserCurrencyConfig",parameters);
    }

    public async Task<object> privateUnifiedGetLeverageUserCurrencySetting (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedGetLeverageUserCurrencySetting",parameters);
    }

    public async Task<object> privateUnifiedPostAccountMode (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedPostAccountMode",parameters);
    }

    public async Task<object> privateUnifiedPostLoans (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedPostLoans",parameters);
    }

    public async Task<object> privateUnifiedPostPortfolioCalculator (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedPostPortfolioCalculator",parameters);
    }

    public async Task<object> privateUnifiedPostLeverageUserCurrencySetting (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedPostLeverageUserCurrencySetting",parameters);
    }

    public async Task<object> privateUnifiedPutUnifiedMode (object parameters = null)
    {
        return await this.callAsync ("privateUnifiedPutUnifiedMode",parameters);
    }

    public async Task<object> privateSpotGetFee (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetFee",parameters);
    }

    public async Task<object> privateSpotGetBatchFee (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetBatchFee",parameters);
    }

    public async Task<object> privateSpotGetAccounts (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetAccounts",parameters);
    }

    public async Task<object> privateSpotGetAccountBook (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetAccountBook",parameters);
    }

    public async Task<object> privateSpotGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetOpenOrders",parameters);
    }

    public async Task<object> privateSpotGetOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetOrders",parameters);
    }

    public async Task<object> privateSpotGetOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetOrdersOrderId",parameters);
    }

    public async Task<object> privateSpotGetMyTrades (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetMyTrades",parameters);
    }

    public async Task<object> privateSpotGetPriceOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetPriceOrders",parameters);
    }

    public async Task<object> privateSpotGetPriceOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateSpotGetPriceOrdersOrderId",parameters);
    }

    public async Task<object> privateSpotPostBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostBatchOrders",parameters);
    }

    public async Task<object> privateSpotPostCrossLiquidateOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostCrossLiquidateOrders",parameters);
    }

    public async Task<object> privateSpotPostOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostOrders",parameters);
    }

    public async Task<object> privateSpotPostCancelBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostCancelBatchOrders",parameters);
    }

    public async Task<object> privateSpotPostCountdownCancelAll (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostCountdownCancelAll",parameters);
    }

    public async Task<object> privateSpotPostAmendBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostAmendBatchOrders",parameters);
    }

    public async Task<object> privateSpotPostPriceOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotPostPriceOrders",parameters);
    }

    public async Task<object> privateSpotDeleteOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotDeleteOrders",parameters);
    }

    public async Task<object> privateSpotDeleteOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateSpotDeleteOrdersOrderId",parameters);
    }

    public async Task<object> privateSpotDeletePriceOrders (object parameters = null)
    {
        return await this.callAsync ("privateSpotDeletePriceOrders",parameters);
    }

    public async Task<object> privateSpotDeletePriceOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateSpotDeletePriceOrdersOrderId",parameters);
    }

    public async Task<object> privateSpotPatchOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateSpotPatchOrdersOrderId",parameters);
    }

    public async Task<object> privateMarginGetAccounts (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetAccounts",parameters);
    }

    public async Task<object> privateMarginGetAccountBook (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetAccountBook",parameters);
    }

    public async Task<object> privateMarginGetFundingAccounts (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetFundingAccounts",parameters);
    }

    public async Task<object> privateMarginGetAutoRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetAutoRepay",parameters);
    }

    public async Task<object> privateMarginGetTransferable (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetTransferable",parameters);
    }

    public async Task<object> privateMarginGetLoans (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetLoans",parameters);
    }

    public async Task<object> privateMarginGetLoansLoanId (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetLoansLoanId",parameters);
    }

    public async Task<object> privateMarginGetLoansLoanIdRepayment (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetLoansLoanIdRepayment",parameters);
    }

    public async Task<object> privateMarginGetLoanRecords (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetLoanRecords",parameters);
    }

    public async Task<object> privateMarginGetLoanRecordsLoanRecordId (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetLoanRecordsLoanRecordId",parameters);
    }

    public async Task<object> privateMarginGetBorrowable (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetBorrowable",parameters);
    }

    public async Task<object> privateMarginGetCrossAccounts (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetCrossAccounts",parameters);
    }

    public async Task<object> privateMarginGetCrossAccountBook (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetCrossAccountBook",parameters);
    }

    public async Task<object> privateMarginGetCrossLoans (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetCrossLoans",parameters);
    }

    public async Task<object> privateMarginGetCrossLoansLoanId (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetCrossLoansLoanId",parameters);
    }

    public async Task<object> privateMarginGetCrossRepayments (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetCrossRepayments",parameters);
    }

    public async Task<object> privateMarginGetCrossInterestRecords (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetCrossInterestRecords",parameters);
    }

    public async Task<object> privateMarginGetCrossTransferable (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetCrossTransferable",parameters);
    }

    public async Task<object> privateMarginGetCrossEstimateRate (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetCrossEstimateRate",parameters);
    }

    public async Task<object> privateMarginGetCrossBorrowable (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetCrossBorrowable",parameters);
    }

    public async Task<object> privateMarginGetUniEstimateRate (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetUniEstimateRate",parameters);
    }

    public async Task<object> privateMarginGetUniLoans (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetUniLoans",parameters);
    }

    public async Task<object> privateMarginGetUniLoanRecords (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetUniLoanRecords",parameters);
    }

    public async Task<object> privateMarginGetUniInterestRecords (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetUniInterestRecords",parameters);
    }

    public async Task<object> privateMarginGetUniBorrowable (object parameters = null)
    {
        return await this.callAsync ("privateMarginGetUniBorrowable",parameters);
    }

    public async Task<object> privateMarginPostAutoRepay (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostAutoRepay",parameters);
    }

    public async Task<object> privateMarginPostLoans (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostLoans",parameters);
    }

    public async Task<object> privateMarginPostMergedLoans (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostMergedLoans",parameters);
    }

    public async Task<object> privateMarginPostLoansLoanIdRepayment (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostLoansLoanIdRepayment",parameters);
    }

    public async Task<object> privateMarginPostCrossLoans (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostCrossLoans",parameters);
    }

    public async Task<object> privateMarginPostCrossRepayments (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostCrossRepayments",parameters);
    }

    public async Task<object> privateMarginPostUniLoans (object parameters = null)
    {
        return await this.callAsync ("privateMarginPostUniLoans",parameters);
    }

    public async Task<object> privateMarginPatchLoansLoanId (object parameters = null)
    {
        return await this.callAsync ("privateMarginPatchLoansLoanId",parameters);
    }

    public async Task<object> privateMarginPatchLoanRecordsLoanRecordId (object parameters = null)
    {
        return await this.callAsync ("privateMarginPatchLoanRecordsLoanRecordId",parameters);
    }

    public async Task<object> privateMarginDeleteLoansLoanId (object parameters = null)
    {
        return await this.callAsync ("privateMarginDeleteLoansLoanId",parameters);
    }

    public async Task<object> privateFlash_swapGetCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateFlash_swapGetCurrencies",parameters);
    }

    public async Task<object> privateFlash_swapGetCurrencyPairs (object parameters = null)
    {
        return await this.callAsync ("privateFlash_swapGetCurrencyPairs",parameters);
    }

    public async Task<object> privateFlash_swapGetOrders (object parameters = null)
    {
        return await this.callAsync ("privateFlash_swapGetOrders",parameters);
    }

    public async Task<object> privateFlash_swapGetOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateFlash_swapGetOrdersOrderId",parameters);
    }

    public async Task<object> privateFlash_swapPostOrders (object parameters = null)
    {
        return await this.callAsync ("privateFlash_swapPostOrders",parameters);
    }

    public async Task<object> privateFlash_swapPostOrdersPreview (object parameters = null)
    {
        return await this.callAsync ("privateFlash_swapPostOrdersPreview",parameters);
    }

    public async Task<object> privateFuturesGetSettleAccounts (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleAccounts",parameters);
    }

    public async Task<object> privateFuturesGetSettleAccountBook (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleAccountBook",parameters);
    }

    public async Task<object> privateFuturesGetSettlePositions (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettlePositions",parameters);
    }

    public async Task<object> privateFuturesGetSettlePositionsContract (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettlePositionsContract",parameters);
    }

    public async Task<object> privateFuturesGetSettleDualCompPositionsContract (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleDualCompPositionsContract",parameters);
    }

    public async Task<object> privateFuturesGetSettleOrders (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleOrders",parameters);
    }

    public async Task<object> privateFuturesGetSettleOrdersTimerange (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleOrdersTimerange",parameters);
    }

    public async Task<object> privateFuturesGetSettleOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleOrdersOrderId",parameters);
    }

    public async Task<object> privateFuturesGetSettleMyTrades (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleMyTrades",parameters);
    }

    public async Task<object> privateFuturesGetSettleMyTradesTimerange (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleMyTradesTimerange",parameters);
    }

    public async Task<object> privateFuturesGetSettlePositionClose (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettlePositionClose",parameters);
    }

    public async Task<object> privateFuturesGetSettleLiquidates (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleLiquidates",parameters);
    }

    public async Task<object> privateFuturesGetSettleAutoDeleverages (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleAutoDeleverages",parameters);
    }

    public async Task<object> privateFuturesGetSettleFee (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleFee",parameters);
    }

    public async Task<object> privateFuturesGetSettleRiskLimitTiers (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettleRiskLimitTiers",parameters);
    }

    public async Task<object> privateFuturesGetSettlePriceOrders (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettlePriceOrders",parameters);
    }

    public async Task<object> privateFuturesGetSettlePriceOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateFuturesGetSettlePriceOrdersOrderId",parameters);
    }

    public async Task<object> privateFuturesPostSettlePositionsContractMargin (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettlePositionsContractMargin",parameters);
    }

    public async Task<object> privateFuturesPostSettlePositionsContractLeverage (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettlePositionsContractLeverage",parameters);
    }

    public async Task<object> privateFuturesPostSettlePositionsContractRiskLimit (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettlePositionsContractRiskLimit",parameters);
    }

    public async Task<object> privateFuturesPostSettleDualMode (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettleDualMode",parameters);
    }

    public async Task<object> privateFuturesPostSettleDualCompPositionsContractMargin (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettleDualCompPositionsContractMargin",parameters);
    }

    public async Task<object> privateFuturesPostSettleDualCompPositionsContractLeverage (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettleDualCompPositionsContractLeverage",parameters);
    }

    public async Task<object> privateFuturesPostSettleDualCompPositionsContractRiskLimit (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettleDualCompPositionsContractRiskLimit",parameters);
    }

    public async Task<object> privateFuturesPostSettleOrders (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettleOrders",parameters);
    }

    public async Task<object> privateFuturesPostSettleBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettleBatchOrders",parameters);
    }

    public async Task<object> privateFuturesPostSettleCountdownCancelAll (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettleCountdownCancelAll",parameters);
    }

    public async Task<object> privateFuturesPostSettleBatchCancelOrders (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettleBatchCancelOrders",parameters);
    }

    public async Task<object> privateFuturesPostSettlePriceOrders (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPostSettlePriceOrders",parameters);
    }

    public async Task<object> privateFuturesPutSettleOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateFuturesPutSettleOrdersOrderId",parameters);
    }

    public async Task<object> privateFuturesDeleteSettleOrders (object parameters = null)
    {
        return await this.callAsync ("privateFuturesDeleteSettleOrders",parameters);
    }

    public async Task<object> privateFuturesDeleteSettleOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateFuturesDeleteSettleOrdersOrderId",parameters);
    }

    public async Task<object> privateFuturesDeleteSettlePriceOrders (object parameters = null)
    {
        return await this.callAsync ("privateFuturesDeleteSettlePriceOrders",parameters);
    }

    public async Task<object> privateFuturesDeleteSettlePriceOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateFuturesDeleteSettlePriceOrdersOrderId",parameters);
    }

    public async Task<object> privateDeliveryGetSettleAccounts (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettleAccounts",parameters);
    }

    public async Task<object> privateDeliveryGetSettleAccountBook (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettleAccountBook",parameters);
    }

    public async Task<object> privateDeliveryGetSettlePositions (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettlePositions",parameters);
    }

    public async Task<object> privateDeliveryGetSettlePositionsContract (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettlePositionsContract",parameters);
    }

    public async Task<object> privateDeliveryGetSettleOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettleOrders",parameters);
    }

    public async Task<object> privateDeliveryGetSettleOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettleOrdersOrderId",parameters);
    }

    public async Task<object> privateDeliveryGetSettleMyTrades (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettleMyTrades",parameters);
    }

    public async Task<object> privateDeliveryGetSettlePositionClose (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettlePositionClose",parameters);
    }

    public async Task<object> privateDeliveryGetSettleLiquidates (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettleLiquidates",parameters);
    }

    public async Task<object> privateDeliveryGetSettleSettlements (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettleSettlements",parameters);
    }

    public async Task<object> privateDeliveryGetSettlePriceOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettlePriceOrders",parameters);
    }

    public async Task<object> privateDeliveryGetSettlePriceOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryGetSettlePriceOrdersOrderId",parameters);
    }

    public async Task<object> privateDeliveryPostSettlePositionsContractMargin (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryPostSettlePositionsContractMargin",parameters);
    }

    public async Task<object> privateDeliveryPostSettlePositionsContractLeverage (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryPostSettlePositionsContractLeverage",parameters);
    }

    public async Task<object> privateDeliveryPostSettlePositionsContractRiskLimit (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryPostSettlePositionsContractRiskLimit",parameters);
    }

    public async Task<object> privateDeliveryPostSettleOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryPostSettleOrders",parameters);
    }

    public async Task<object> privateDeliveryPostSettlePriceOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryPostSettlePriceOrders",parameters);
    }

    public async Task<object> privateDeliveryDeleteSettleOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryDeleteSettleOrders",parameters);
    }

    public async Task<object> privateDeliveryDeleteSettleOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryDeleteSettleOrdersOrderId",parameters);
    }

    public async Task<object> privateDeliveryDeleteSettlePriceOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryDeleteSettlePriceOrders",parameters);
    }

    public async Task<object> privateDeliveryDeleteSettlePriceOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateDeliveryDeleteSettlePriceOrdersOrderId",parameters);
    }

    public async Task<object> privateOptionsGetMySettlements (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetMySettlements",parameters);
    }

    public async Task<object> privateOptionsGetAccounts (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetAccounts",parameters);
    }

    public async Task<object> privateOptionsGetAccountBook (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetAccountBook",parameters);
    }

    public async Task<object> privateOptionsGetPositions (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetPositions",parameters);
    }

    public async Task<object> privateOptionsGetPositionsContract (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetPositionsContract",parameters);
    }

    public async Task<object> privateOptionsGetPositionClose (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetPositionClose",parameters);
    }

    public async Task<object> privateOptionsGetOrders (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetOrders",parameters);
    }

    public async Task<object> privateOptionsGetOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetOrdersOrderId",parameters);
    }

    public async Task<object> privateOptionsGetMyTrades (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetMyTrades",parameters);
    }

    public async Task<object> privateOptionsGetMmp (object parameters = null)
    {
        return await this.callAsync ("privateOptionsGetMmp",parameters);
    }

    public async Task<object> privateOptionsPostOrders (object parameters = null)
    {
        return await this.callAsync ("privateOptionsPostOrders",parameters);
    }

    public async Task<object> privateOptionsPostCountdownCancelAll (object parameters = null)
    {
        return await this.callAsync ("privateOptionsPostCountdownCancelAll",parameters);
    }

    public async Task<object> privateOptionsPostMmp (object parameters = null)
    {
        return await this.callAsync ("privateOptionsPostMmp",parameters);
    }

    public async Task<object> privateOptionsPostMmpReset (object parameters = null)
    {
        return await this.callAsync ("privateOptionsPostMmpReset",parameters);
    }

    public async Task<object> privateOptionsDeleteOrders (object parameters = null)
    {
        return await this.callAsync ("privateOptionsDeleteOrders",parameters);
    }

    public async Task<object> privateOptionsDeleteOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateOptionsDeleteOrdersOrderId",parameters);
    }

    public async Task<object> privateEarnGetUniCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetUniCurrencies",parameters);
    }

    public async Task<object> privateEarnGetUniCurrenciesCurrency (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetUniCurrenciesCurrency",parameters);
    }

    public async Task<object> privateEarnGetUniLends (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetUniLends",parameters);
    }

    public async Task<object> privateEarnGetUniLendRecords (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetUniLendRecords",parameters);
    }

    public async Task<object> privateEarnGetUniInterestsCurrency (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetUniInterestsCurrency",parameters);
    }

    public async Task<object> privateEarnGetUniInterestRecords (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetUniInterestRecords",parameters);
    }

    public async Task<object> privateEarnGetUniInterestStatusCurrency (object parameters = null)
    {
        return await this.callAsync ("privateEarnGetUniInterestStatusCurrency",parameters);
    }

    public async Task<object> privateEarnPostUniLends (object parameters = null)
    {
        return await this.callAsync ("privateEarnPostUniLends",parameters);
    }

    public async Task<object> privateEarnPutUniInterestReinvest (object parameters = null)
    {
        return await this.callAsync ("privateEarnPutUniInterestReinvest",parameters);
    }

    public async Task<object> privateEarnPatchUniLends (object parameters = null)
    {
        return await this.callAsync ("privateEarnPatchUniLends",parameters);
    }

    public async Task<object> privateLoanGetCollateralOrders (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetCollateralOrders",parameters);
    }

    public async Task<object> privateLoanGetCollateralOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetCollateralOrdersOrderId",parameters);
    }

    public async Task<object> privateLoanGetCollateralRepayRecords (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetCollateralRepayRecords",parameters);
    }

    public async Task<object> privateLoanGetCollateralCollaterals (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetCollateralCollaterals",parameters);
    }

    public async Task<object> privateLoanGetCollateralTotalAmount (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetCollateralTotalAmount",parameters);
    }

    public async Task<object> privateLoanGetCollateralLtv (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetCollateralLtv",parameters);
    }

    public async Task<object> privateLoanGetCollateralCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetCollateralCurrencies",parameters);
    }

    public async Task<object> privateLoanGetMultiCollateralOrders (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetMultiCollateralOrders",parameters);
    }

    public async Task<object> privateLoanGetMultiCollateralOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetMultiCollateralOrdersOrderId",parameters);
    }

    public async Task<object> privateLoanGetMultiCollateralRepay (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetMultiCollateralRepay",parameters);
    }

    public async Task<object> privateLoanGetMultiCollateralMortgage (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetMultiCollateralMortgage",parameters);
    }

    public async Task<object> privateLoanGetMultiCollateralCurrencyQuota (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetMultiCollateralCurrencyQuota",parameters);
    }

    public async Task<object> privateLoanGetMultiCollateralCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetMultiCollateralCurrencies",parameters);
    }

    public async Task<object> privateLoanGetMultiCollateralLtv (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetMultiCollateralLtv",parameters);
    }

    public async Task<object> privateLoanGetMultiCollateralFixedRate (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetMultiCollateralFixedRate",parameters);
    }

    public async Task<object> privateLoanGetMultiCollateralCurrentRate (object parameters = null)
    {
        return await this.callAsync ("privateLoanGetMultiCollateralCurrentRate",parameters);
    }

    public async Task<object> privateLoanPostCollateralOrders (object parameters = null)
    {
        return await this.callAsync ("privateLoanPostCollateralOrders",parameters);
    }

    public async Task<object> privateLoanPostCollateralRepay (object parameters = null)
    {
        return await this.callAsync ("privateLoanPostCollateralRepay",parameters);
    }

    public async Task<object> privateLoanPostCollateralCollaterals (object parameters = null)
    {
        return await this.callAsync ("privateLoanPostCollateralCollaterals",parameters);
    }

    public async Task<object> privateLoanPostMultiCollateralOrders (object parameters = null)
    {
        return await this.callAsync ("privateLoanPostMultiCollateralOrders",parameters);
    }

    public async Task<object> privateLoanPostMultiCollateralRepay (object parameters = null)
    {
        return await this.callAsync ("privateLoanPostMultiCollateralRepay",parameters);
    }

    public async Task<object> privateLoanPostMultiCollateralMortgage (object parameters = null)
    {
        return await this.callAsync ("privateLoanPostMultiCollateralMortgage",parameters);
    }

    public async Task<object> privateAccountGetDetail (object parameters = null)
    {
        return await this.callAsync ("privateAccountGetDetail",parameters);
    }

    public async Task<object> privateAccountGetRateLimit (object parameters = null)
    {
        return await this.callAsync ("privateAccountGetRateLimit",parameters);
    }

    public async Task<object> privateAccountGetStpGroups (object parameters = null)
    {
        return await this.callAsync ("privateAccountGetStpGroups",parameters);
    }

    public async Task<object> privateAccountGetStpGroupsStpIdUsers (object parameters = null)
    {
        return await this.callAsync ("privateAccountGetStpGroupsStpIdUsers",parameters);
    }

    public async Task<object> privateAccountGetStpGroupsDebitFee (object parameters = null)
    {
        return await this.callAsync ("privateAccountGetStpGroupsDebitFee",parameters);
    }

    public async Task<object> privateAccountPostStpGroups (object parameters = null)
    {
        return await this.callAsync ("privateAccountPostStpGroups",parameters);
    }

    public async Task<object> privateAccountPostStpGroupsStpIdUsers (object parameters = null)
    {
        return await this.callAsync ("privateAccountPostStpGroupsStpIdUsers",parameters);
    }

    public async Task<object> privateAccountDeleteStpGroupsStpIdUsers (object parameters = null)
    {
        return await this.callAsync ("privateAccountDeleteStpGroupsStpIdUsers",parameters);
    }

    public async Task<object> privateRebateGetAgencyTransactionHistory (object parameters = null)
    {
        return await this.callAsync ("privateRebateGetAgencyTransactionHistory",parameters);
    }

    public async Task<object> privateRebateGetAgencyCommissionHistory (object parameters = null)
    {
        return await this.callAsync ("privateRebateGetAgencyCommissionHistory",parameters);
    }

}