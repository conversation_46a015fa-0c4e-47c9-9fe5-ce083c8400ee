// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class coinsph : Exchange
{
    public coinsph (object args = null): base(args) {}

    public async Task<object> publicGetOpenapiV1Ping (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiV1Ping",parameters);
    }

    public async Task<object> publicGetOpenapiV1Time (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiV1Time",parameters);
    }

    public async Task<object> publicGetOpenapiQuoteV1Ticker24hr (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiQuoteV1Ticker24hr",parameters);
    }

    public async Task<object> publicGetOpenapiQuoteV1TickerPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiQuoteV1TickerPrice",parameters);
    }

    public async Task<object> publicGetOpenapiQuoteV1TickerBookTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiQuoteV1TickerBookTicker",parameters);
    }

    public async Task<object> publicGetOpenapiV1ExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiV1ExchangeInfo",parameters);
    }

    public async Task<object> publicGetOpenapiQuoteV1Depth (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiQuoteV1Depth",parameters);
    }

    public async Task<object> publicGetOpenapiQuoteV1Klines (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiQuoteV1Klines",parameters);
    }

    public async Task<object> publicGetOpenapiQuoteV1Trades (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiQuoteV1Trades",parameters);
    }

    public async Task<object> publicGetOpenapiV1Pairs (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiV1Pairs",parameters);
    }

    public async Task<object> publicGetOpenapiQuoteV1AvgPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetOpenapiQuoteV1AvgPrice",parameters);
    }

    public async Task<object> privateGetOpenapiWalletV1ConfigGetall (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiWalletV1ConfigGetall",parameters);
    }

    public async Task<object> privateGetOpenapiWalletV1DepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiWalletV1DepositAddress",parameters);
    }

    public async Task<object> privateGetOpenapiWalletV1DepositHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiWalletV1DepositHistory",parameters);
    }

    public async Task<object> privateGetOpenapiWalletV1WithdrawHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiWalletV1WithdrawHistory",parameters);
    }

    public async Task<object> privateGetOpenapiV1Account (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiV1Account",parameters);
    }

    public async Task<object> privateGetOpenapiV1OpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiV1OpenOrders",parameters);
    }

    public async Task<object> privateGetOpenapiV1AssetTradeFee (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiV1AssetTradeFee",parameters);
    }

    public async Task<object> privateGetOpenapiV1Order (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiV1Order",parameters);
    }

    public async Task<object> privateGetOpenapiV1HistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiV1HistoryOrders",parameters);
    }

    public async Task<object> privateGetOpenapiV1MyTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiV1MyTrades",parameters);
    }

    public async Task<object> privateGetOpenapiV1CapitalDepositHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiV1CapitalDepositHistory",parameters);
    }

    public async Task<object> privateGetOpenapiV1CapitalWithdrawHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiV1CapitalWithdrawHistory",parameters);
    }

    public async Task<object> privateGetOpenapiV3PaymentRequestGetPaymentRequest (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiV3PaymentRequestGetPaymentRequest",parameters);
    }

    public async Task<object> privateGetMerchantApiV1GetInvoices (object parameters = null)
    {
        return await this.callAsync ("privateGetMerchantApiV1GetInvoices",parameters);
    }

    public async Task<object> privateGetOpenapiAccountV3CryptoAccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiAccountV3CryptoAccounts",parameters);
    }

    public async Task<object> privateGetOpenapiTransferV3TransfersId (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenapiTransferV3TransfersId",parameters);
    }

    public async Task<object> privatePostOpenapiWalletV1WithdrawApply (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiWalletV1WithdrawApply",parameters);
    }

    public async Task<object> privatePostOpenapiV1OrderTest (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiV1OrderTest",parameters);
    }

    public async Task<object> privatePostOpenapiV1Order (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiV1Order",parameters);
    }

    public async Task<object> privatePostOpenapiV1CapitalWithdrawApply (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiV1CapitalWithdrawApply",parameters);
    }

    public async Task<object> privatePostOpenapiV1CapitalDepositApply (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiV1CapitalDepositApply",parameters);
    }

    public async Task<object> privatePostOpenapiV3PaymentRequestPaymentRequests (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiV3PaymentRequestPaymentRequests",parameters);
    }

    public async Task<object> privatePostOpenapiV3PaymentRequestDeletePaymentRequest (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiV3PaymentRequestDeletePaymentRequest",parameters);
    }

    public async Task<object> privatePostOpenapiV3PaymentRequestPaymentRequestReminder (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiV3PaymentRequestPaymentRequestReminder",parameters);
    }

    public async Task<object> privatePostOpenapiV1UserDataStream (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiV1UserDataStream",parameters);
    }

    public async Task<object> privatePostMerchantApiV1Invoices (object parameters = null)
    {
        return await this.callAsync ("privatePostMerchantApiV1Invoices",parameters);
    }

    public async Task<object> privatePostMerchantApiV1InvoicesCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostMerchantApiV1InvoicesCancel",parameters);
    }

    public async Task<object> privatePostOpenapiConvertV1GetSupportedTradingPairs (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiConvertV1GetSupportedTradingPairs",parameters);
    }

    public async Task<object> privatePostOpenapiConvertV1GetQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiConvertV1GetQuote",parameters);
    }

    public async Task<object> privatePostOpenapiConvertV1AccpetQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiConvertV1AccpetQuote",parameters);
    }

    public async Task<object> privatePostOpenapiFiatV1SupportChannel (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiFiatV1SupportChannel",parameters);
    }

    public async Task<object> privatePostOpenapiFiatV1CashOut (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiFiatV1CashOut",parameters);
    }

    public async Task<object> privatePostOpenapiFiatV1History (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiFiatV1History",parameters);
    }

    public async Task<object> privatePostOpenapiMigrationV4Sellorder (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiMigrationV4Sellorder",parameters);
    }

    public async Task<object> privatePostOpenapiMigrationV4ValidateField (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiMigrationV4ValidateField",parameters);
    }

    public async Task<object> privatePostOpenapiTransferV3Transfers (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenapiTransferV3Transfers",parameters);
    }

    public async Task<object> privateDeleteOpenapiV1Order (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOpenapiV1Order",parameters);
    }

    public async Task<object> privateDeleteOpenapiV1OpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOpenapiV1OpenOrders",parameters);
    }

    public async Task<object> privateDeleteOpenapiV1UserDataStream (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOpenapiV1UserDataStream",parameters);
    }

}