name: Go
on:
  workflow_dispatch:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
jobs:
  build:
    runs-on: ubuntu-latest
    if: ${{ !startsWith(github.event.head_commit.message, '[Automated changes]') && !contains(github.event.head_commit.message, 'Merge branch ''master'' of https://github.com/ccxt/ccxt') }}
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4
        if: github.ref == 'refs/heads/master'
        with:
          token: ${{ secrets.GH_TOKEN }}
          fetch-depth: 2
      - uses: actions/checkout@v4
        if: github.ref != 'refs/heads/master'
        with:
          fetch-depth: 2
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21.x"
      - name: Setup Node.js
        uses: actions/setup-node@v3
      - name: Install npm dependencies
        run: npm ci --include=dev
      - name: Determine modified files
        run: ./utils/init_actions.sh
      - name: Check tsc version
        run: tsc --version
      - name: Install tsx
        run: npm install -g tsx
      - name: export exchanges
        run: npm run export-exchanges
      - name: Generate implicit API
        run: npm run emitAPI -- --go
      - name: Transpile To Go
        if: env.important_modified == 'true'
        run: tsx build/goTranspiler.ts
      - name: Transpile to Go (specific)
        if: env.important_modified == 'false'
        run: |
          cleaned_rest_files=$(echo ${{ env.rest_files }} | tr -s ' ')
          for exchange in $cleaned_rest_files; do
            tsx build/goTranspiler.ts -- $exchange
          done
      # - name: Install dependencies
      #   run: cd go && go get ./ccxt && cd ..
      - name: Build
        run: cd go && go build ./v4 && cd ..
      - name: Build tests
        run: cd go && go build ./tests/main.go && cd ..
      - name: Test GO Types
        if: env.important_modified == 'true'
        run: npm run test-types-go
      - name: Run Base Tests
        if: env.important_modified == 'true'
        run: npm run test-base-rest-go
      - name: Run Id Tests
        if: env.important_modified == 'true'
        run: npm run id-tests-go
      - name: Request tests
        if: env.important_modified == 'true'
        run: npm run request-go
      - name: Request tests (specific)
        if: env.important_modified == 'false'
        run: |
          cleaned_rest_files=$(echo ${{ env.rest_files }} | tr -s ' ')
          for exchange in $cleaned_rest_files; do
            npm run request-go -- $exchange
          done
      - name: Response Tests
        if: env.important_modified == 'true'
        run: npm run response-go
      - name: Response tests (specific)
        if: env.important_modified == 'false'
        run: |
          cleaned_rest_files=$(echo ${{ env.rest_files }} | tr -s ' ')
          for exchange in $cleaned_rest_files; do
            npm run response-go -- $exchange
          done

      # - name: Upload Go Files
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: go-files
      #     path: go/
      - name: Upload Go Files
        uses: actions/upload-artifact@v4
        with:
          name: go-files
          path: go/
      - name: Upload shared_env.txt
        uses: actions/upload-artifact@v4
        with:
            name: shared_env
            path: shared_env.txt

      - name: Push GO changes to master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        if: github.ref == 'refs/heads/master'
        run: |
            git config --global user.name "github-actions[bot]"
            git config --global user.email "github-actions[bot]@users.noreply.github.com"
            rm -f ./go/tests/main
            rm -f ./go/v4/ccxt
            rm -f ./go/main
            git add go/
            git commit -m "[Automated changes] GO files" || echo "No changes to commit."
            git remote set-url origin https://${GITHUB_TOKEN}@github.com/${{ github.repository }}
            git config --global pull.rebase false
            git pull --rebase --autostash origin master
            git push

  live-tests:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Download shared_env
        uses: actions/download-artifact@v4
        with:
          name: shared_env
      # - name: Download Go Files
      #   uses: actions/download-artifact@v4
      #   with:
      #     name: go-files
      #     path: go/
      # - uses: actions/checkout@v4
      - name: Download Go Binary
        uses: actions/download-artifact@v4
        with:
          name: go-files
          path: go/
      - name: Restore shared_env
        run: ./utils/restore_shared_env.sh
      - name: Build tests
        run: cd  go/ && go build ./tests/main.go
      - name: Install npm dependencies
        run: npm ci --include=dev
      - name: Export exchanges
        run: npm run export-exchanges
      # - name: Install dependencies
      #   run: go get ./go/v4
      # - name: Build
      #   run: go build ./go/v4
      # - name: Build tests
      #   run: go build ./go/tests/main.go
      # - name: Adjust permissions
      #   run: cd go && chmod +x main
      - name: Build
        run: cd go && go build ./v4 && cd ..
      - name: Build tests
        run: cd go && go build ./tests/main.go && cd ..
      - name: Live tests
        if: env.important_modified == 'true'
        run: node run-tests --go --useProxy
      - name: Live tests (specific)
        if: env.important_modified == 'false'
        run: ./run-tests-simul.sh --go "${{ env.rest_files }}"


