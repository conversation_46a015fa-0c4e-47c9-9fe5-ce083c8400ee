// CCXT JavaScript 示例
// 演示如何使用 CCXT 库获取交易所信息和市场数据

import ccxt from './js/ccxt.js';

async function main() {
    console.log('=== CCXT JavaScript 示例 ===\n');
    
    // 显示 CCXT 版本和支持的交易所数量
    console.log(`CCXT 版本: ${ccxt.version}`);
    console.log(`支持的交易所数量: ${Object.keys(ccxt.exchanges).length}\n`);
    
    // 显示前10个交易所
    console.log('前10个支持的交易所:');
    Object.keys(ccxt.exchanges).slice(0, 10).forEach((exchange, index) => {
        console.log(`${index + 1}. ${exchange}`);
    });
    console.log('');
    
    // 创建一个交易所实例（使用 Binance 作为示例）
    const exchange = new ccxt.binance({
        sandbox: true, // 使用测试环境
        enableRateLimit: true,
    });
    
    try {
        console.log(`=== ${exchange.name} 交易所信息 ===`);
        console.log(`交易所 ID: ${exchange.id}`);
        console.log(`API 版本: ${exchange.version || 'N/A'}`);
        console.log(`是否支持 CORS: ${exchange.cors ? '是' : '否'}`);
        console.log(`速率限制: ${exchange.rateLimit}ms\n`);
        
        // 获取市场信息
        console.log('正在获取市场信息...');
        const markets = await exchange.loadMarkets();
        const marketCount = Object.keys(markets).length;
        console.log(`可用交易对数量: ${marketCount}`);
        
        // 显示前5个交易对
        console.log('\n前5个交易对:');
        Object.keys(markets).slice(0, 5).forEach((symbol, index) => {
            const market = markets[symbol];
            console.log(`${index + 1}. ${symbol} (${market.base}/${market.quote})`);
        });
        
        // 获取 BTC/USDT 的行情数据（如果可用）
        if (markets['BTC/USDT']) {
            console.log('\n=== BTC/USDT 行情数据 ===');
            const ticker = await exchange.fetchTicker('BTC/USDT');
            console.log(`最新价格: $${ticker.last}`);
            console.log(`24h 最高价: $${ticker.high}`);
            console.log(`24h 最低价: $${ticker.low}`);
            console.log(`24h 成交量: ${ticker.baseVolume} BTC`);
            console.log(`24h 涨跌幅: ${ticker.percentage ? ticker.percentage.toFixed(2) + '%' : 'N/A'}`);
        }
        
    } catch (error) {
        console.log('获取数据时出错:', error.message);
        console.log('这可能是因为网络连接问题或API限制');
    }
    
    console.log('\n=== 示例完成 ===');
    console.log('更多示例请查看 examples/ 目录');
}

// 运行示例
main().catch(console.error);
