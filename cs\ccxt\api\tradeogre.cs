// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class tradeogre : Exchange
{
    public tradeogre (object args = null): base(args) {}

    public async Task<object> publicGetMarkets (object parameters = null)
    {
        return await this.callAsync ("publicGetMarkets",parameters);
    }

    public async Task<object> publicGetOrdersMarket (object parameters = null)
    {
        return await this.callAsync ("publicGetOrdersMarket",parameters);
    }

    public async Task<object> publicGetTickerMarket (object parameters = null)
    {
        return await this.callAsync ("publicGetTickerMarket",parameters);
    }

    public async Task<object> publicGetHistoryMarket (object parameters = null)
    {
        return await this.callAsync ("publicGetHistoryMarket",parameters);
    }

    public async Task<object> publicGetChartIntervalMarketTimestamp (object parameters = null)
    {
        return await this.callAsync ("publicGetChartIntervalMarketTimestamp",parameters);
    }

    public async Task<object> publicGetChartIntervalMarket (object parameters = null)
    {
        return await this.callAsync ("publicGetChartIntervalMarket",parameters);
    }

    public async Task<object> privateGetAccountBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBalances",parameters);
    }

    public async Task<object> privateGetAccountOrderUuid (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountOrderUuid",parameters);
    }

    public async Task<object> privatePostOrderBuy (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderBuy",parameters);
    }

    public async Task<object> privatePostOrderSell (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderSell",parameters);
    }

    public async Task<object> privatePostOrderCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderCancel",parameters);
    }

    public async Task<object> privatePostOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrders",parameters);
    }

    public async Task<object> privatePostAccountOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountOrders",parameters);
    }

    public async Task<object> privatePostAccountBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountBalance",parameters);
    }

}