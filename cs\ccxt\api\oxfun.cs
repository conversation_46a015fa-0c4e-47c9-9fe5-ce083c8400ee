// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class oxfun : Exchange
{
    public oxfun (object args = null): base(args) {}

    public async Task<object> publicGetV3Markets (object parameters = null)
    {
        return await this.callAsync ("publicGetV3Markets",parameters);
    }

    public async Task<object> publicGetV3Assets (object parameters = null)
    {
        return await this.callAsync ("publicGetV3Assets",parameters);
    }

    public async Task<object> publicGetV3Tickers (object parameters = null)
    {
        return await this.callAsync ("publicGetV3Tickers",parameters);
    }

    public async Task<object> publicGetV3FundingEstimates (object parameters = null)
    {
        return await this.callAsync ("publicGetV3FundingEstimates",parameters);
    }

    public async Task<object> publicGetV3Candles (object parameters = null)
    {
        return await this.callAsync ("publicGetV3Candles",parameters);
    }

    public async Task<object> publicGetV3Depth (object parameters = null)
    {
        return await this.callAsync ("publicGetV3Depth",parameters);
    }

    public async Task<object> publicGetV3MarketsOperational (object parameters = null)
    {
        return await this.callAsync ("publicGetV3MarketsOperational",parameters);
    }

    public async Task<object> publicGetV3ExchangeTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetV3ExchangeTrades",parameters);
    }

    public async Task<object> publicGetV3FundingRates (object parameters = null)
    {
        return await this.callAsync ("publicGetV3FundingRates",parameters);
    }

    public async Task<object> publicGetV3LeverageTiers (object parameters = null)
    {
        return await this.callAsync ("publicGetV3LeverageTiers",parameters);
    }

    public async Task<object> privateGetV3Account (object parameters = null)
    {
        return await this.callAsync ("privateGetV3Account",parameters);
    }

    public async Task<object> privateGetV3AccountNames (object parameters = null)
    {
        return await this.callAsync ("privateGetV3AccountNames",parameters);
    }

    public async Task<object> privateGetV3Wallet (object parameters = null)
    {
        return await this.callAsync ("privateGetV3Wallet",parameters);
    }

    public async Task<object> privateGetV3Transfer (object parameters = null)
    {
        return await this.callAsync ("privateGetV3Transfer",parameters);
    }

    public async Task<object> privateGetV3Balances (object parameters = null)
    {
        return await this.callAsync ("privateGetV3Balances",parameters);
    }

    public async Task<object> privateGetV3Positions (object parameters = null)
    {
        return await this.callAsync ("privateGetV3Positions",parameters);
    }

    public async Task<object> privateGetV3Funding (object parameters = null)
    {
        return await this.callAsync ("privateGetV3Funding",parameters);
    }

    public async Task<object> privateGetV3DepositAddresses (object parameters = null)
    {
        return await this.callAsync ("privateGetV3DepositAddresses",parameters);
    }

    public async Task<object> privateGetV3Deposit (object parameters = null)
    {
        return await this.callAsync ("privateGetV3Deposit",parameters);
    }

    public async Task<object> privateGetV3WithdrawalAddresses (object parameters = null)
    {
        return await this.callAsync ("privateGetV3WithdrawalAddresses",parameters);
    }

    public async Task<object> privateGetV3Withdrawal (object parameters = null)
    {
        return await this.callAsync ("privateGetV3Withdrawal",parameters);
    }

    public async Task<object> privateGetV3WithdrawalFees (object parameters = null)
    {
        return await this.callAsync ("privateGetV3WithdrawalFees",parameters);
    }

    public async Task<object> privateGetV3OrdersStatus (object parameters = null)
    {
        return await this.callAsync ("privateGetV3OrdersStatus",parameters);
    }

    public async Task<object> privateGetV3OrdersWorking (object parameters = null)
    {
        return await this.callAsync ("privateGetV3OrdersWorking",parameters);
    }

    public async Task<object> privateGetV3Trades (object parameters = null)
    {
        return await this.callAsync ("privateGetV3Trades",parameters);
    }

    public async Task<object> privatePostV3Transfer (object parameters = null)
    {
        return await this.callAsync ("privatePostV3Transfer",parameters);
    }

    public async Task<object> privatePostV3Withdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostV3Withdrawal",parameters);
    }

    public async Task<object> privatePostV3OrdersPlace (object parameters = null)
    {
        return await this.callAsync ("privatePostV3OrdersPlace",parameters);
    }

    public async Task<object> privateDeleteV3OrdersCancel (object parameters = null)
    {
        return await this.callAsync ("privateDeleteV3OrdersCancel",parameters);
    }

    public async Task<object> privateDeleteV3OrdersCancelAll (object parameters = null)
    {
        return await this.callAsync ("privateDeleteV3OrdersCancelAll",parameters);
    }

}