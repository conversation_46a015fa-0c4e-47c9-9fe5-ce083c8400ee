// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bitteam : Exchange
{
    public bitteam (object args = null): base(args) {}

    public async Task<object> historyGetApiTwHistoryPairNameResolution (object parameters = null)
    {
        return await this.callAsync ("historyGetApiTwHistoryPairNameResolution",parameters);
    }

    public async Task<object> publicGetTradeApiAsset (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiAsset",parameters);
    }

    public async Task<object> publicGetTradeApiCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiCurrencies",parameters);
    }

    public async Task<object> publicGetTradeApiOrderbooksSymbol (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiOrderbooksSymbol",parameters);
    }

    public async Task<object> publicGetTradeApiOrders (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiOrders",parameters);
    }

    public async Task<object> publicGetTradeApiPairName (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiPairName",parameters);
    }

    public async Task<object> publicGetTradeApiPairs (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiPairs",parameters);
    }

    public async Task<object> publicGetTradeApiPairsPrecisions (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiPairsPrecisions",parameters);
    }

    public async Task<object> publicGetTradeApiRates (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiRates",parameters);
    }

    public async Task<object> publicGetTradeApiTradeId (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiTradeId",parameters);
    }

    public async Task<object> publicGetTradeApiTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiTrades",parameters);
    }

    public async Task<object> publicGetTradeApiCcxtPairs (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiCcxtPairs",parameters);
    }

    public async Task<object> publicGetTradeApiCmcAssets (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiCmcAssets",parameters);
    }

    public async Task<object> publicGetTradeApiCmcOrderbookPair (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiCmcOrderbookPair",parameters);
    }

    public async Task<object> publicGetTradeApiCmcSummary (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiCmcSummary",parameters);
    }

    public async Task<object> publicGetTradeApiCmcTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiCmcTicker",parameters);
    }

    public async Task<object> publicGetTradeApiCmcTradesPair (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeApiCmcTradesPair",parameters);
    }

    public async Task<object> privateGetTradeApiCcxtBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeApiCcxtBalance",parameters);
    }

    public async Task<object> privateGetTradeApiCcxtOrderId (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeApiCcxtOrderId",parameters);
    }

    public async Task<object> privateGetTradeApiCcxtOrdersOfUser (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeApiCcxtOrdersOfUser",parameters);
    }

    public async Task<object> privateGetTradeApiCcxtTradesOfUser (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeApiCcxtTradesOfUser",parameters);
    }

    public async Task<object> privateGetTradeApiTransactionsOfUser (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeApiTransactionsOfUser",parameters);
    }

    public async Task<object> privatePostTradeApiCcxtCancelAllOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeApiCcxtCancelAllOrder",parameters);
    }

    public async Task<object> privatePostTradeApiCcxtCancelorder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeApiCcxtCancelorder",parameters);
    }

    public async Task<object> privatePostTradeApiCcxtOrdercreate (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeApiCcxtOrdercreate",parameters);
    }

}