// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class whitebit : Exchange
{
    public whitebit (object args = null): base(args) {}

    public async Task<object> webGetV1Healthcheck (object parameters = null)
    {
        return await this.callAsync ("webGetV1Healthcheck",parameters);
    }

    public async Task<object> v1PublicGetMarkets (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetMarkets",parameters);
    }

    public async Task<object> v1PublicGetTickers (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetTickers",parameters);
    }

    public async Task<object> v1PublicGetTicker (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetTicker",parameters);
    }

    public async Task<object> v1PublicGetSymbols (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetSymbols",parameters);
    }

    public async Task<object> v1PublicGetDepthResult (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetDepthResult",parameters);
    }

    public async Task<object> v1PublicGetHistory (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetHistory",parameters);
    }

    public async Task<object> v1PublicGetKline (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetKline",parameters);
    }

    public async Task<object> v1PrivatePostAccountBalance (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAccountBalance",parameters);
    }

    public async Task<object> v1PrivatePostOrderNew (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostOrderNew",parameters);
    }

    public async Task<object> v1PrivatePostOrderCancel (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostOrderCancel",parameters);
    }

    public async Task<object> v1PrivatePostOrders (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostOrders",parameters);
    }

    public async Task<object> v1PrivatePostAccountOrderHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAccountOrderHistory",parameters);
    }

    public async Task<object> v1PrivatePostAccountExecutedHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAccountExecutedHistory",parameters);
    }

    public async Task<object> v1PrivatePostAccountExecutedHistoryAll (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAccountExecutedHistoryAll",parameters);
    }

    public async Task<object> v1PrivatePostAccountOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAccountOrder",parameters);
    }

    public async Task<object> v2PublicGetMarkets (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetMarkets",parameters);
    }

    public async Task<object> v2PublicGetTicker (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetTicker",parameters);
    }

    public async Task<object> v2PublicGetAssets (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetAssets",parameters);
    }

    public async Task<object> v2PublicGetFee (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetFee",parameters);
    }

    public async Task<object> v2PublicGetDepthMarket (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetDepthMarket",parameters);
    }

    public async Task<object> v2PublicGetTradesMarket (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetTradesMarket",parameters);
    }

    public async Task<object> v4PublicGetAssets (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetAssets",parameters);
    }

    public async Task<object> v4PublicGetCollateralMarkets (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetCollateralMarkets",parameters);
    }

    public async Task<object> v4PublicGetFee (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetFee",parameters);
    }

    public async Task<object> v4PublicGetOrderbookDepthMarket (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetOrderbookDepthMarket",parameters);
    }

    public async Task<object> v4PublicGetOrderbookMarket (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetOrderbookMarket",parameters);
    }

    public async Task<object> v4PublicGetTicker (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetTicker",parameters);
    }

    public async Task<object> v4PublicGetTradesMarket (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetTradesMarket",parameters);
    }

    public async Task<object> v4PublicGetTime (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetTime",parameters);
    }

    public async Task<object> v4PublicGetPing (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetPing",parameters);
    }

    public async Task<object> v4PublicGetMarkets (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetMarkets",parameters);
    }

    public async Task<object> v4PublicGetFutures (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetFutures",parameters);
    }

    public async Task<object> v4PublicGetPlatformStatus (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetPlatformStatus",parameters);
    }

    public async Task<object> v4PublicGetMiningPool (object parameters = null)
    {
        return await this.callAsync ("v4PublicGetMiningPool",parameters);
    }

    public async Task<object> v4PrivatePostCollateralAccountBalance (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostCollateralAccountBalance",parameters);
    }

    public async Task<object> v4PrivatePostCollateralAccountBalanceSummary (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostCollateralAccountBalanceSummary",parameters);
    }

    public async Task<object> v4PrivatePostCollateralAccountPositionsHistory (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostCollateralAccountPositionsHistory",parameters);
    }

    public async Task<object> v4PrivatePostCollateralAccountLeverage (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostCollateralAccountLeverage",parameters);
    }

    public async Task<object> v4PrivatePostCollateralAccountPositionsOpen (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostCollateralAccountPositionsOpen",parameters);
    }

    public async Task<object> v4PrivatePostCollateralAccountSummary (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostCollateralAccountSummary",parameters);
    }

    public async Task<object> v4PrivatePostCollateralAccountFundingHistory (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostCollateralAccountFundingHistory",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountAddress (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountAddress",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountBalance (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountBalance",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountCreateNewAddress (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountCreateNewAddress",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountCodes (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountCodes",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountCodesApply (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountCodesApply",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountCodesMy (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountCodesMy",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountCodesHistory (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountCodesHistory",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountFiatDepositUrl (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountFiatDepositUrl",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountHistory (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountHistory",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountWithdraw (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountWithdraw",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountWithdrawPay (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountWithdrawPay",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountTransfer (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountTransfer",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountSmartPlans (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountSmartPlans",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountSmartInvestment (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountSmartInvestment",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountSmartInvestmentClose (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountSmartInvestmentClose",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountSmartInvestments (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountSmartInvestments",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountFee (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountFee",parameters);
    }

    public async Task<object> v4PrivatePostMainAccountSmartInterestPaymentHistory (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMainAccountSmartInterestPaymentHistory",parameters);
    }

    public async Task<object> v4PrivatePostTradeAccountBalance (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostTradeAccountBalance",parameters);
    }

    public async Task<object> v4PrivatePostTradeAccountExecutedHistory (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostTradeAccountExecutedHistory",parameters);
    }

    public async Task<object> v4PrivatePostTradeAccountOrder (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostTradeAccountOrder",parameters);
    }

    public async Task<object> v4PrivatePostTradeAccountOrderHistory (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostTradeAccountOrderHistory",parameters);
    }

    public async Task<object> v4PrivatePostOrderCollateralLimit (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderCollateralLimit",parameters);
    }

    public async Task<object> v4PrivatePostOrderCollateralMarket (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderCollateralMarket",parameters);
    }

    public async Task<object> v4PrivatePostOrderCollateralStopLimit (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderCollateralStopLimit",parameters);
    }

    public async Task<object> v4PrivatePostOrderCollateralTriggerMarket (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderCollateralTriggerMarket",parameters);
    }

    public async Task<object> v4PrivatePostOrderCollateralBulk (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderCollateralBulk",parameters);
    }

    public async Task<object> v4PrivatePostOrderNew (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderNew",parameters);
    }

    public async Task<object> v4PrivatePostOrderMarket (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderMarket",parameters);
    }

    public async Task<object> v4PrivatePostOrderStockMarket (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderStockMarket",parameters);
    }

    public async Task<object> v4PrivatePostOrderStopLimit (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderStopLimit",parameters);
    }

    public async Task<object> v4PrivatePostOrderStopMarket (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderStopMarket",parameters);
    }

    public async Task<object> v4PrivatePostOrderCancel (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderCancel",parameters);
    }

    public async Task<object> v4PrivatePostOrderCancelAll (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderCancelAll",parameters);
    }

    public async Task<object> v4PrivatePostOrderKillSwitch (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderKillSwitch",parameters);
    }

    public async Task<object> v4PrivatePostOrderKillSwitchStatus (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderKillSwitchStatus",parameters);
    }

    public async Task<object> v4PrivatePostOrderBulk (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderBulk",parameters);
    }

    public async Task<object> v4PrivatePostOrderModify (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderModify",parameters);
    }

    public async Task<object> v4PrivatePostOrderConditionalCancel (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderConditionalCancel",parameters);
    }

    public async Task<object> v4PrivatePostOrders (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrders",parameters);
    }

    public async Task<object> v4PrivatePostOcoOrders (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOcoOrders",parameters);
    }

    public async Task<object> v4PrivatePostOrderCollateralOco (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderCollateralOco",parameters);
    }

    public async Task<object> v4PrivatePostOrderOcoCancel (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderOcoCancel",parameters);
    }

    public async Task<object> v4PrivatePostOrderOtoCancel (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostOrderOtoCancel",parameters);
    }

    public async Task<object> v4PrivatePostProfileWebsocketToken (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostProfileWebsocketToken",parameters);
    }

    public async Task<object> v4PrivatePostConvertEstimate (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostConvertEstimate",parameters);
    }

    public async Task<object> v4PrivatePostConvertConfirm (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostConvertConfirm",parameters);
    }

    public async Task<object> v4PrivatePostConvertHistory (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostConvertHistory",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountCreate (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountCreate",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountDelete (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountDelete",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountEdit (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountEdit",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountList (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountList",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountTransfer (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountTransfer",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountBlock (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountBlock",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountUnblock (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountUnblock",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountBalances (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountBalances",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountTransferHistory (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountTransferHistory",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountApiKeyCreate (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountApiKeyCreate",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountApiKeyEdit (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountApiKeyEdit",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountApiKeyDelete (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountApiKeyDelete",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountApiKeyList (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountApiKeyList",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountApiKeyReset (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountApiKeyReset",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountApiKeyIpAddressList (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountApiKeyIpAddressList",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountApiKeyIpAddressCreate (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountApiKeyIpAddressCreate",parameters);
    }

    public async Task<object> v4PrivatePostSubAccountApiKeyIpAddressDelete (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostSubAccountApiKeyIpAddressDelete",parameters);
    }

    public async Task<object> v4PrivatePostMiningRewards (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMiningRewards",parameters);
    }

    public async Task<object> v4PrivatePostMarketFee (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostMarketFee",parameters);
    }

    public async Task<object> v4PrivatePostConditionalOrders (object parameters = null)
    {
        return await this.callAsync ("v4PrivatePostConditionalOrders",parameters);
    }

}