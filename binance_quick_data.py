#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
币安快速数据获取器
快速获取币安交易所核心数据
"""

import ccxt
import asyncio
import json
import pandas as pd
from datetime import datetime
import sys
import os

# 添加当前目录到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'python'))

async def get_binance_data():
    """获取币安核心数据"""
    
    # 创建币安交易所实例
    exchange = ccxt.binance({
        'enableRateLimit': True,
        'timeout': 30000,
    })
    
    try:
        print("🔗 连接币安交易所...")
        
        # 1. 获取所有市场信息
        print("📊 获取市场信息...")
        markets = await exchange.load_markets()
        print(f"✅ 获取到 {len(markets)} 个交易对")
        
        # 2. 获取所有行情数据
        print("📈 获取行情数据...")
        tickers = await exchange.fetch_tickers()
        print(f"✅ 获取到 {len(tickers)} 个币种行情")
        
        # 3. 筛选出交易量最大的前20个USDT交易对
        usdt_tickers = {k: v for k, v in tickers.items() if k.endswith('/USDT') and v['quoteVolume']}
        top_20_usdt = sorted(usdt_tickers.items(), key=lambda x: x[1]['quoteVolume'], reverse=True)[:20]
        
        print(f"📊 交易量最大的20个USDT交易对:")
        for i, (symbol, ticker) in enumerate(top_20_usdt, 1):
            volume = ticker['quoteVolume']
            price = ticker['last']
            change = ticker['percentage']
            print(f"  {i:2d}. {symbol:12s} 价格: ${price:>10.4f} 24h量: ${volume:>15,.0f} 涨跌: {change:>6.2f}%")
        
        # 4. 获取主要币种的订单簿
        print("\n📖 获取主要币种订单簿...")
        major_symbols = [symbol for symbol, _ in top_20_usdt[:5]]  # 前5个
        orderbooks = {}
        
        for symbol in major_symbols:
            try:
                orderbook = await exchange.fetch_order_book(symbol, 10)
                orderbooks[symbol] = orderbook
                print(f"  ✅ {symbol} 订单簿获取成功")
                await asyncio.sleep(0.1)
            except Exception as e:
                print(f"  ❌ {symbol} 订单簿获取失败: {e}")
        
        # 5. 获取BTC/USDT的K线数据
        print("\n📊 获取BTC/USDT K线数据...")
        try:
            btc_1h = await exchange.fetch_ohlcv('BTC/USDT', '1h', limit=24)  # 24小时
            btc_1d = await exchange.fetch_ohlcv('BTC/USDT', '1d', limit=7)   # 7天
            print(f"  ✅ 获取到 {len(btc_1h)} 条1小时K线和 {len(btc_1d)} 条日K线")
        except Exception as e:
            print(f"  ❌ K线数据获取失败: {e}")
            btc_1h = btc_1d = []
        
        # 6. 保存数据到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存行情数据
        ticker_data = []
        for symbol, ticker in tickers.items():
            if ticker['last'] and ticker['quoteVolume']:  # 只保存有效数据
                ticker_data.append({
                    'symbol': symbol,
                    'price': ticker['last'],
                    'high_24h': ticker['high'],
                    'low_24h': ticker['low'],
                    'volume_24h': ticker['baseVolume'],
                    'volume_24h_usdt': ticker['quoteVolume'],
                    'change_24h': ticker['change'],
                    'change_24h_percent': ticker['percentage'],
                    'timestamp': ticker['timestamp'],
                    'datetime': ticker['datetime']
                })
        
        df_tickers = pd.DataFrame(ticker_data)
        df_tickers.to_csv(f'binance_tickers_{timestamp}.csv', index=False, encoding='utf-8')
        print(f"\n💾 行情数据已保存到 binance_tickers_{timestamp}.csv")
        
        # 保存K线数据
        if btc_1h:
            kline_data = []
            for ohlcv in btc_1h:
                kline_data.append({
                    'timestamp': ohlcv[0],
                    'datetime': datetime.fromtimestamp(ohlcv[0] / 1000).isoformat(),
                    'open': ohlcv[1],
                    'high': ohlcv[2],
                    'low': ohlcv[3],
                    'close': ohlcv[4],
                    'volume': ohlcv[5],
                    'timeframe': '1h'
                })
            
            for ohlcv in btc_1d:
                kline_data.append({
                    'timestamp': ohlcv[0],
                    'datetime': datetime.fromtimestamp(ohlcv[0] / 1000).isoformat(),
                    'open': ohlcv[1],
                    'high': ohlcv[2],
                    'low': ohlcv[3],
                    'close': ohlcv[4],
                    'volume': ohlcv[5],
                    'timeframe': '1d'
                })
            
            df_klines = pd.DataFrame(kline_data)
            df_klines.to_csv(f'binance_btc_klines_{timestamp}.csv', index=False, encoding='utf-8')
            print(f"💾 BTC K线数据已保存到 binance_btc_klines_{timestamp}.csv")
        
        # 保存订单簿数据
        if orderbooks:
            orderbook_data = []
            for symbol, orderbook in orderbooks.items():
                # 买单
                for i, (price, amount) in enumerate(orderbook['bids'][:5]):
                    orderbook_data.append({
                        'symbol': symbol,
                        'side': 'bid',
                        'level': i + 1,
                        'price': price,
                        'amount': amount,
                        'timestamp': orderbook['timestamp']
                    })
                
                # 卖单
                for i, (price, amount) in enumerate(orderbook['asks'][:5]):
                    orderbook_data.append({
                        'symbol': symbol,
                        'side': 'ask',
                        'level': i + 1,
                        'price': price,
                        'amount': amount,
                        'timestamp': orderbook['timestamp']
                    })
            
            df_orderbooks = pd.DataFrame(orderbook_data)
            df_orderbooks.to_csv(f'binance_orderbooks_{timestamp}.csv', index=False, encoding='utf-8')
            print(f"💾 订单簿数据已保存到 binance_orderbooks_{timestamp}.csv")
        
        # 显示统计信息
        print("\n" + "="*60)
        print("📊 数据获取完成！统计信息:")
        print(f"  - 总交易对数量: {len(markets)}")
        print(f"  - 有效行情数量: {len(ticker_data)}")
        print(f"  - USDT交易对数量: {len(usdt_tickers)}")
        print(f"  - 订单簿数量: {len(orderbooks)}")
        print(f"  - BTC K线数据: {len(btc_1h) + len(btc_1d)} 条")
        
        # 显示市值最大的币种
        print(f"\n🏆 交易量最大的5个USDT交易对:")
        for i, (symbol, ticker) in enumerate(top_20_usdt[:5], 1):
            print(f"  {i}. {symbol}: ${ticker['last']:.4f} (24h量: ${ticker['quoteVolume']:,.0f})")
        
        return {
            'markets': markets,
            'tickers': tickers,
            'orderbooks': orderbooks,
            'btc_klines': {'1h': btc_1h, '1d': btc_1d},
            'top_usdt_pairs': top_20_usdt
        }
        
    except Exception as e:
        print(f"❌ 获取数据时出错: {e}")
        return None
    
    finally:
        await exchange.close()

def sync_get_basic_data():
    """同步获取基础数据"""
    print("🔗 币安基础数据获取器 (同步版本)")
    
    exchange = ccxt.binance({
        'enableRateLimit': True,
    })
    
    try:
        # 获取市场信息
        print("📊 获取市场信息...")
        markets = exchange.load_markets()
        print(f"✅ 获取到 {len(markets)} 个交易对")
        
        # 获取BTC/USDT行情
        print("📈 获取BTC/USDT行情...")
        btc_ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"  BTC价格: ${btc_ticker['last']}")
        print(f"  24h涨跌: {btc_ticker['percentage']:.2f}%")
        print(f"  24h成交量: {btc_ticker['baseVolume']:.2f} BTC")
        
        # 获取前10个主要币种行情
        major_symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'XRP/USDT']
        print(f"\n📊 主要币种行情:")
        for symbol in major_symbols:
            try:
                ticker = exchange.fetch_ticker(symbol)
                print(f"  {symbol:10s}: ${ticker['last']:>8.4f} ({ticker['percentage']:>6.2f}%)")
            except:
                print(f"  {symbol:10s}: 获取失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 币安数据获取器")
    print("选择模式:")
    print("1. 完整数据获取 (异步，包含所有数据)")
    print("2. 基础数据获取 (同步，快速获取)")
    
    try:
        choice = input("请选择 (1 或 2): ").strip()
        
        if choice == '2':
            sync_get_basic_data()
        else:
            await get_binance_data()
            
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"💥 程序运行出错: {e}")
