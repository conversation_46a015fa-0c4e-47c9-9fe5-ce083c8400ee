#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
币安真实数据获取器
获取币安交易所真实的公开数据
"""

import ccxt
import asyncio
import json
import pandas as pd
from datetime import datetime
import time
import sys
import os

# 添加当前目录到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'python'))

class BinanceRealDataCollector:
    def __init__(self):
        """初始化币安数据收集器"""
        self.exchange = ccxt.binance({
            'enableRateLimit': True,
            'timeout': 30000,
            'options': {
                'defaultType': 'spot',  # 现货交易
            }
        })
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    async def test_connection(self):
        """测试连接"""
        try:
            print("🔗 测试币安API连接...")
            # 获取服务器时间来测试连接
            server_time = await self.exchange.fetch_time()
            print(f"✅ 连接成功！服务器时间: {datetime.fromtimestamp(server_time/1000)}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def get_exchange_info(self):
        """获取交易所基本信息"""
        try:
            print("ℹ️ 获取交易所信息...")
            
            # 获取交易所状态
            status = await self.exchange.fetch_status()
            print(f"  交易所状态: {status['status']}")
            print(f"  更新时间: {status['updated']}")
            
            # 获取支持的功能
            has = self.exchange.has
            print(f"  支持功能:")
            important_features = [
                'fetchTicker', 'fetchTickers', 'fetchOrderBook', 
                'fetchTrades', 'fetchOHLCV', 'fetchMarkets'
            ]
            
            for feature in important_features:
                status_icon = "✅" if has.get(feature) else "❌"
                print(f"    {status_icon} {feature}")
            
            return True
        except Exception as e:
            print(f"❌ 获取交易所信息失败: {e}")
            return False
    
    async def get_all_markets(self):
        """获取所有市场信息"""
        try:
            print("📊 获取市场信息...")
            markets = await self.exchange.load_markets()
            
            # 统计不同类型的市场
            spot_markets = [s for s, m in markets.items() if m.get('spot', False)]
            usdt_markets = [s for s in spot_markets if s.endswith('/USDT')]
            btc_markets = [s for s in spot_markets if s.endswith('/BTC')]
            
            print(f"✅ 总交易对: {len(markets)}")
            print(f"  现货交易对: {len(spot_markets)}")
            print(f"  USDT交易对: {len(usdt_markets)}")
            print(f"  BTC交易对: {len(btc_markets)}")
            
            # 保存市场数据
            market_data = []
            for symbol, market in markets.items():
                if market.get('spot', False):  # 只保存现货市场
                    market_data.append({
                        'symbol': symbol,
                        'base': market['base'],
                        'quote': market['quote'],
                        'active': market['active'],
                        'type': market.get('type', 'spot'),
                        'precision_amount': market['precision']['amount'],
                        'precision_price': market['precision']['price'],
                        'min_amount': market['limits']['amount']['min'],
                        'max_amount': market['limits']['amount']['max'],
                        'min_cost': market['limits']['cost']['min'],
                    })
            
            df = pd.DataFrame(market_data)
            df.to_csv(f'binance_markets_{self.timestamp}.csv', index=False, encoding='utf-8')
            print(f"💾 市场数据已保存到 binance_markets_{self.timestamp}.csv")
            
            return markets
            
        except Exception as e:
            print(f"❌ 获取市场信息失败: {e}")
            return {}
    
    async def get_top_tickers(self, limit=50):
        """获取交易量最大的行情数据"""
        try:
            print(f"📈 获取前{limit}个交易量最大的USDT交易对行情...")
            
            # 获取所有USDT交易对的行情
            all_tickers = await self.exchange.fetch_tickers()
            
            # 筛选USDT交易对并按交易量排序
            usdt_tickers = {}
            for symbol, ticker in all_tickers.items():
                if (symbol.endswith('/USDT') and 
                    ticker.get('quoteVolume') and 
                    ticker.get('last')):
                    usdt_tickers[symbol] = ticker
            
            # 按24小时交易量排序
            sorted_tickers = sorted(
                usdt_tickers.items(), 
                key=lambda x: x[1]['quoteVolume'], 
                reverse=True
            )[:limit]
            
            print(f"✅ 获取到 {len(sorted_tickers)} 个USDT交易对行情")
            
            # 显示前10个
            print(f"\n🏆 交易量最大的10个USDT交易对:")
            for i, (symbol, ticker) in enumerate(sorted_tickers[:10], 1):
                volume = ticker['quoteVolume']
                price = ticker['last']
                change = ticker['percentage'] or 0
                print(f"  {i:2d}. {symbol:15s} ${price:>12.4f} 量:${volume:>15,.0f} 涨跌:{change:>7.2f}%")
            
            # 保存数据
            ticker_data = []
            for symbol, ticker in sorted_tickers:
                ticker_data.append({
                    'symbol': symbol,
                    'price': ticker['last'],
                    'high_24h': ticker['high'],
                    'low_24h': ticker['low'],
                    'volume_24h_base': ticker['baseVolume'],
                    'volume_24h_quote': ticker['quoteVolume'],
                    'change_24h': ticker['change'],
                    'change_24h_percent': ticker['percentage'],
                    'bid': ticker['bid'],
                    'ask': ticker['ask'],
                    'timestamp': ticker['timestamp'],
                    'datetime': ticker['datetime']
                })
            
            df = pd.DataFrame(ticker_data)
            df.to_csv(f'binance_top_tickers_{self.timestamp}.csv', index=False, encoding='utf-8')
            print(f"💾 行情数据已保存到 binance_top_tickers_{self.timestamp}.csv")
            
            return dict(sorted_tickers)
            
        except Exception as e:
            print(f"❌ 获取行情数据失败: {e}")
            return {}
    
    async def get_orderbooks(self, symbols, limit=20):
        """获取订单簿数据"""
        try:
            print(f"📖 获取 {len(symbols)} 个交易对的订单簿...")
            orderbooks = {}
            
            for i, symbol in enumerate(symbols, 1):
                try:
                    print(f"  {i}/{len(symbols)} 获取 {symbol} 订单簿...")
                    orderbook = await self.exchange.fetch_order_book(symbol, limit)
                    orderbooks[symbol] = orderbook
                    
                    # 显示订单簿信息
                    if orderbook['bids'] and orderbook['asks']:
                        best_bid = orderbook['bids'][0][0]
                        best_ask = orderbook['asks'][0][0]
                        spread = best_ask - best_bid
                        spread_percent = (spread / best_bid) * 100
                        print(f"    最佳买价: ${best_bid:.4f}, 最佳卖价: ${best_ask:.4f}, 价差: {spread_percent:.3f}%")
                    
                    await asyncio.sleep(0.1)  # 避免触发速率限制
                    
                except Exception as e:
                    print(f"    ❌ 获取 {symbol} 订单簿失败: {e}")
                    continue
            
            print(f"✅ 成功获取 {len(orderbooks)} 个订单簿")
            
            # 保存订单簿数据
            if orderbooks:
                orderbook_data = []
                for symbol, orderbook in orderbooks.items():
                    # 保存前10档买卖盘
                    for i, (price, amount) in enumerate(orderbook['bids'][:10]):
                        orderbook_data.append({
                            'symbol': symbol,
                            'side': 'bid',
                            'level': i + 1,
                            'price': price,
                            'amount': amount,
                            'timestamp': orderbook['timestamp']
                        })
                    
                    for i, (price, amount) in enumerate(orderbook['asks'][:10]):
                        orderbook_data.append({
                            'symbol': symbol,
                            'side': 'ask',
                            'level': i + 1,
                            'price': price,
                            'amount': amount,
                            'timestamp': orderbook['timestamp']
                        })
                
                df = pd.DataFrame(orderbook_data)
                df.to_csv(f'binance_orderbooks_{self.timestamp}.csv', index=False, encoding='utf-8')
                print(f"💾 订单簿数据已保存到 binance_orderbooks_{self.timestamp}.csv")
            
            return orderbooks
            
        except Exception as e:
            print(f"❌ 获取订单簿失败: {e}")
            return {}
    
    async def get_kline_data(self, symbols, timeframe='1h', limit=24):
        """获取K线数据"""
        try:
            print(f"📊 获取 {len(symbols)} 个交易对的{timeframe}K线数据...")
            kline_data_all = {}
            
            for i, symbol in enumerate(symbols, 1):
                try:
                    print(f"  {i}/{len(symbols)} 获取 {symbol} K线...")
                    klines = await self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
                    kline_data_all[symbol] = klines
                    
                    if klines:
                        latest = klines[-1]
                        latest_time = datetime.fromtimestamp(latest[0] / 1000)
                        print(f"    最新K线: {latest_time} O:{latest[1]:.4f} H:{latest[2]:.4f} L:{latest[3]:.4f} C:{latest[4]:.4f}")
                    
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    print(f"    ❌ 获取 {symbol} K线失败: {e}")
                    continue
            
            print(f"✅ 成功获取 {len(kline_data_all)} 个交易对的K线数据")
            
            # 保存K线数据
            if kline_data_all:
                all_klines = []
                for symbol, klines in kline_data_all.items():
                    for kline in klines:
                        all_klines.append({
                            'symbol': symbol,
                            'timestamp': kline[0],
                            'datetime': datetime.fromtimestamp(kline[0] / 1000).isoformat(),
                            'open': kline[1],
                            'high': kline[2],
                            'low': kline[3],
                            'close': kline[4],
                            'volume': kline[5],
                            'timeframe': timeframe
                        })
                
                df = pd.DataFrame(all_klines)
                df.to_csv(f'binance_klines_{timeframe}_{self.timestamp}.csv', index=False, encoding='utf-8')
                print(f"💾 K线数据已保存到 binance_klines_{timeframe}_{self.timestamp}.csv")
            
            return kline_data_all
            
        except Exception as e:
            print(f"❌ 获取K线数据失败: {e}")
            return {}
    
    async def collect_real_data(self):
        """收集真实数据"""
        print("🚀 开始收集币安真实数据...")
        print(f"📅 时间: {datetime.now()}")
        print("=" * 60)
        
        try:
            # 1. 测试连接
            if not await self.test_connection():
                return None
            
            # 2. 获取交易所信息
            await self.get_exchange_info()
            
            # 3. 获取市场信息
            markets = await self.get_all_markets()
            if not markets:
                return None
            
            # 4. 获取热门交易对行情
            top_tickers = await self.get_top_tickers(50)
            if not top_tickers:
                return None
            
            # 5. 获取前5个交易对的订单簿
            top_5_symbols = list(top_tickers.keys())[:5]
            orderbooks = await self.get_orderbooks(top_5_symbols)
            
            # 6. 获取前3个交易对的K线数据
            top_3_symbols = list(top_tickers.keys())[:3]
            klines_1h = await self.get_kline_data(top_3_symbols, '1h', 24)
            klines_1d = await self.get_kline_data(top_3_symbols, '1d', 7)
            
            print("=" * 60)
            print("🎉 真实数据收集完成！")
            print(f"  - 市场数据: {len(markets)} 个交易对")
            print(f"  - 行情数据: {len(top_tickers)} 个热门交易对")
            print(f"  - 订单簿: {len(orderbooks)} 个交易对")
            print(f"  - 1小时K线: {len(klines_1h)} 个交易对")
            print(f"  - 1日K线: {len(klines_1d)} 个交易对")
            print(f"  - 数据文件前缀: binance_*_{self.timestamp}.csv")
            
            return {
                'markets': markets,
                'tickers': top_tickers,
                'orderbooks': orderbooks,
                'klines_1h': klines_1h,
                'klines_1d': klines_1d
            }
            
        except Exception as e:
            print(f"❌ 数据收集失败: {e}")
            return None
        
        finally:
            await self.exchange.close()

async def main():
    """主函数"""
    print("💰 币安真实数据获取器")
    print("=" * 60)
    
    collector = BinanceRealDataCollector()
    data = await collector.collect_real_data()
    
    if data:
        print("\n✅ 数据收集成功！")
        print("📁 请查看当前目录中的 CSV 文件获取详细数据")
    else:
        print("\n❌ 数据收集失败，请检查网络连接")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")
        print("请检查网络连接和防火墙设置")
