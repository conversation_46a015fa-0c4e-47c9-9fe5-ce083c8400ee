// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class delta : Exchange
{
    public delta (object args = null): base(args) {}

    public async Task<object> publicGetAssets (object parameters = null)
    {
        return await this.callAsync ("publicGetAssets",parameters);
    }

    public async Task<object> publicGetIndices (object parameters = null)
    {
        return await this.callAsync ("publicGetIndices",parameters);
    }

    public async Task<object> publicGetProducts (object parameters = null)
    {
        return await this.callAsync ("publicGetProducts",parameters);
    }

    public async Task<object> publicGetProductsSymbol (object parameters = null)
    {
        return await this.callAsync ("publicGetProductsSymbol",parameters);
    }

    public async Task<object> publicGetTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetTickers",parameters);
    }

    public async Task<object> publicGetTickersSymbol (object parameters = null)
    {
        return await this.callAsync ("publicGetTickersSymbol",parameters);
    }

    public async Task<object> publicGetL2orderbookSymbol (object parameters = null)
    {
        return await this.callAsync ("publicGetL2orderbookSymbol",parameters);
    }

    public async Task<object> publicGetTradesSymbol (object parameters = null)
    {
        return await this.callAsync ("publicGetTradesSymbol",parameters);
    }

    public async Task<object> publicGetStats (object parameters = null)
    {
        return await this.callAsync ("publicGetStats",parameters);
    }

    public async Task<object> publicGetHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetHistoryCandles",parameters);
    }

    public async Task<object> publicGetHistorySparklines (object parameters = null)
    {
        return await this.callAsync ("publicGetHistorySparklines",parameters);
    }

    public async Task<object> publicGetSettings (object parameters = null)
    {
        return await this.callAsync ("publicGetSettings",parameters);
    }

    public async Task<object> privateGetOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetOrders",parameters);
    }

    public async Task<object> privateGetProductsProductIdOrdersLeverage (object parameters = null)
    {
        return await this.callAsync ("privateGetProductsProductIdOrdersLeverage",parameters);
    }

    public async Task<object> privateGetPositionsMargined (object parameters = null)
    {
        return await this.callAsync ("privateGetPositionsMargined",parameters);
    }

    public async Task<object> privateGetPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetPositions",parameters);
    }

    public async Task<object> privateGetOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersHistory",parameters);
    }

    public async Task<object> privateGetFills (object parameters = null)
    {
        return await this.callAsync ("privateGetFills",parameters);
    }

    public async Task<object> privateGetFillsHistoryDownloadCsv (object parameters = null)
    {
        return await this.callAsync ("privateGetFillsHistoryDownloadCsv",parameters);
    }

    public async Task<object> privateGetWalletBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetWalletBalances",parameters);
    }

    public async Task<object> privateGetWalletTransactions (object parameters = null)
    {
        return await this.callAsync ("privateGetWalletTransactions",parameters);
    }

    public async Task<object> privateGetWalletTransactionsDownload (object parameters = null)
    {
        return await this.callAsync ("privateGetWalletTransactionsDownload",parameters);
    }

    public async Task<object> privateGetWalletsSubAccountsTransferHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetWalletsSubAccountsTransferHistory",parameters);
    }

    public async Task<object> privateGetUsersTradingPreferences (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersTradingPreferences",parameters);
    }

    public async Task<object> privateGetSubAccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetSubAccounts",parameters);
    }

    public async Task<object> privateGetProfile (object parameters = null)
    {
        return await this.callAsync ("privateGetProfile",parameters);
    }

    public async Task<object> privateGetDepositsAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetDepositsAddress",parameters);
    }

    public async Task<object> privateGetOrdersLeverage (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersLeverage",parameters);
    }

    public async Task<object> privatePostOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrders",parameters);
    }

    public async Task<object> privatePostOrdersBracket (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersBracket",parameters);
    }

    public async Task<object> privatePostOrdersBatch (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersBatch",parameters);
    }

    public async Task<object> privatePostProductsProductIdOrdersLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostProductsProductIdOrdersLeverage",parameters);
    }

    public async Task<object> privatePostPositionsChangeMargin (object parameters = null)
    {
        return await this.callAsync ("privatePostPositionsChangeMargin",parameters);
    }

    public async Task<object> privatePostPositionsCloseAll (object parameters = null)
    {
        return await this.callAsync ("privatePostPositionsCloseAll",parameters);
    }

    public async Task<object> privatePostWalletsSubAccountBalanceTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostWalletsSubAccountBalanceTransfer",parameters);
    }

    public async Task<object> privatePostOrdersCancelAfter (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersCancelAfter",parameters);
    }

    public async Task<object> privatePostOrdersLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersLeverage",parameters);
    }

    public async Task<object> privatePutOrders (object parameters = null)
    {
        return await this.callAsync ("privatePutOrders",parameters);
    }

    public async Task<object> privatePutOrdersBracket (object parameters = null)
    {
        return await this.callAsync ("privatePutOrdersBracket",parameters);
    }

    public async Task<object> privatePutOrdersBatch (object parameters = null)
    {
        return await this.callAsync ("privatePutOrdersBatch",parameters);
    }

    public async Task<object> privatePutPositionsAutoTopup (object parameters = null)
    {
        return await this.callAsync ("privatePutPositionsAutoTopup",parameters);
    }

    public async Task<object> privatePutUsersUpdateMmp (object parameters = null)
    {
        return await this.callAsync ("privatePutUsersUpdateMmp",parameters);
    }

    public async Task<object> privatePutUsersResetMmp (object parameters = null)
    {
        return await this.callAsync ("privatePutUsersResetMmp",parameters);
    }

    public async Task<object> privateDeleteOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrders",parameters);
    }

    public async Task<object> privateDeleteOrdersAll (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrdersAll",parameters);
    }

    public async Task<object> privateDeleteOrdersBatch (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrdersBatch",parameters);
    }

}