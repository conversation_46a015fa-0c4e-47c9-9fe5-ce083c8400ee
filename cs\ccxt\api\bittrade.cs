// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bittrade : Exchange
{
    public bittrade (object args = null): base(args) {}

    public async Task<object> v2PublicGetReferenceCurrencies (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetReferenceCurrencies",parameters);
    }

    public async Task<object> v2PublicGetMarketStatus (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetMarketStatus",parameters);
    }

    public async Task<object> v2PrivateGetAccountLedger (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountLedger",parameters);
    }

    public async Task<object> v2PrivateGetAccountWithdrawQuota (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountWithdrawQuota",parameters);
    }

    public async Task<object> v2PrivateGetAccountWithdrawAddress (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountWithdrawAddress",parameters);
    }

    public async Task<object> v2PrivateGetAccountDepositAddress (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountDepositAddress",parameters);
    }

    public async Task<object> v2PrivateGetAccountRepayment (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountRepayment",parameters);
    }

    public async Task<object> v2PrivateGetReferenceTransactFeeRate (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetReferenceTransactFeeRate",parameters);
    }

    public async Task<object> v2PrivateGetAccountAssetValuation (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountAssetValuation",parameters);
    }

    public async Task<object> v2PrivateGetPointAccount (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetPointAccount",parameters);
    }

    public async Task<object> v2PrivateGetSubUserUserList (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetSubUserUserList",parameters);
    }

    public async Task<object> v2PrivateGetSubUserUserState (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetSubUserUserState",parameters);
    }

    public async Task<object> v2PrivateGetSubUserAccountList (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetSubUserAccountList",parameters);
    }

    public async Task<object> v2PrivateGetSubUserDepositAddress (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetSubUserDepositAddress",parameters);
    }

    public async Task<object> v2PrivateGetSubUserQueryDeposit (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetSubUserQueryDeposit",parameters);
    }

    public async Task<object> v2PrivateGetUserApiKey (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetUserApiKey",parameters);
    }

    public async Task<object> v2PrivateGetUserUid (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetUserUid",parameters);
    }

    public async Task<object> v2PrivateGetAlgoOrdersOpening (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAlgoOrdersOpening",parameters);
    }

    public async Task<object> v2PrivateGetAlgoOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAlgoOrdersHistory",parameters);
    }

    public async Task<object> v2PrivateGetAlgoOrdersSpecific (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAlgoOrdersSpecific",parameters);
    }

    public async Task<object> v2PrivateGetC2cOffers (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetC2cOffers",parameters);
    }

    public async Task<object> v2PrivateGetC2cOffer (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetC2cOffer",parameters);
    }

    public async Task<object> v2PrivateGetC2cTransactions (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetC2cTransactions",parameters);
    }

    public async Task<object> v2PrivateGetC2cRepayment (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetC2cRepayment",parameters);
    }

    public async Task<object> v2PrivateGetC2cAccount (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetC2cAccount",parameters);
    }

    public async Task<object> v2PrivateGetEtpReference (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetEtpReference",parameters);
    }

    public async Task<object> v2PrivateGetEtpTransactions (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetEtpTransactions",parameters);
    }

    public async Task<object> v2PrivateGetEtpTransaction (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetEtpTransaction",parameters);
    }

    public async Task<object> v2PrivateGetEtpRebalance (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetEtpRebalance",parameters);
    }

    public async Task<object> v2PrivateGetEtpLimit (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetEtpLimit",parameters);
    }

    public async Task<object> v2PrivatePostAccountTransfer (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountTransfer",parameters);
    }

    public async Task<object> v2PrivatePostAccountRepayment (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountRepayment",parameters);
    }

    public async Task<object> v2PrivatePostPointTransfer (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostPointTransfer",parameters);
    }

    public async Task<object> v2PrivatePostSubUserManagement (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostSubUserManagement",parameters);
    }

    public async Task<object> v2PrivatePostSubUserCreation (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostSubUserCreation",parameters);
    }

    public async Task<object> v2PrivatePostSubUserTradableMarket (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostSubUserTradableMarket",parameters);
    }

    public async Task<object> v2PrivatePostSubUserTransferability (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostSubUserTransferability",parameters);
    }

    public async Task<object> v2PrivatePostSubUserApiKeyGeneration (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostSubUserApiKeyGeneration",parameters);
    }

    public async Task<object> v2PrivatePostSubUserApiKeyModification (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostSubUserApiKeyModification",parameters);
    }

    public async Task<object> v2PrivatePostSubUserApiKeyDeletion (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostSubUserApiKeyDeletion",parameters);
    }

    public async Task<object> v2PrivatePostSubUserDeductMode (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostSubUserDeductMode",parameters);
    }

    public async Task<object> v2PrivatePostAlgoOrders (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAlgoOrders",parameters);
    }

    public async Task<object> v2PrivatePostAlgoOrdersCancelAllAfter (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAlgoOrdersCancelAllAfter",parameters);
    }

    public async Task<object> v2PrivatePostAlgoOrdersCancellation (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAlgoOrdersCancellation",parameters);
    }

    public async Task<object> v2PrivatePostC2cOffer (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostC2cOffer",parameters);
    }

    public async Task<object> v2PrivatePostC2cCancellation (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostC2cCancellation",parameters);
    }

    public async Task<object> v2PrivatePostC2cCancelAll (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostC2cCancelAll",parameters);
    }

    public async Task<object> v2PrivatePostC2cRepayment (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostC2cRepayment",parameters);
    }

    public async Task<object> v2PrivatePostC2cTransfer (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostC2cTransfer",parameters);
    }

    public async Task<object> v2PrivatePostEtpCreation (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostEtpCreation",parameters);
    }

    public async Task<object> v2PrivatePostEtpRedemption (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostEtpRedemption",parameters);
    }

    public async Task<object> v2PrivatePostEtpTransactIdCancel (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostEtpTransactIdCancel",parameters);
    }

    public async Task<object> v2PrivatePostEtpBatchCancel (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostEtpBatchCancel",parameters);
    }

    public async Task<object> marketGetHistoryKline (object parameters = null)
    {
        return await this.callAsync ("marketGetHistoryKline",parameters);
    }

    public async Task<object> marketGetDetailMerged (object parameters = null)
    {
        return await this.callAsync ("marketGetDetailMerged",parameters);
    }

    public async Task<object> marketGetDepth (object parameters = null)
    {
        return await this.callAsync ("marketGetDepth",parameters);
    }

    public async Task<object> marketGetTrade (object parameters = null)
    {
        return await this.callAsync ("marketGetTrade",parameters);
    }

    public async Task<object> marketGetHistoryTrade (object parameters = null)
    {
        return await this.callAsync ("marketGetHistoryTrade",parameters);
    }

    public async Task<object> marketGetDetail (object parameters = null)
    {
        return await this.callAsync ("marketGetDetail",parameters);
    }

    public async Task<object> marketGetTickers (object parameters = null)
    {
        return await this.callAsync ("marketGetTickers",parameters);
    }

    public async Task<object> marketGetEtp (object parameters = null)
    {
        return await this.callAsync ("marketGetEtp",parameters);
    }

    public async Task<object> publicGetCommonSymbols (object parameters = null)
    {
        return await this.callAsync ("publicGetCommonSymbols",parameters);
    }

    public async Task<object> publicGetCommonCurrencys (object parameters = null)
    {
        return await this.callAsync ("publicGetCommonCurrencys",parameters);
    }

    public async Task<object> publicGetCommonTimestamp (object parameters = null)
    {
        return await this.callAsync ("publicGetCommonTimestamp",parameters);
    }

    public async Task<object> publicGetCommonExchange (object parameters = null)
    {
        return await this.callAsync ("publicGetCommonExchange",parameters);
    }

    public async Task<object> publicGetSettingsCurrencys (object parameters = null)
    {
        return await this.callAsync ("publicGetSettingsCurrencys",parameters);
    }

    public async Task<object> privateGetAccountAccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountAccounts",parameters);
    }

    public async Task<object> privateGetAccountAccountsIdBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountAccountsIdBalance",parameters);
    }

    public async Task<object> privateGetAccountAccountsSubUid (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountAccountsSubUid",parameters);
    }

    public async Task<object> privateGetAccountHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountHistory",parameters);
    }

    public async Task<object> privateGetCrossMarginLoanInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetCrossMarginLoanInfo",parameters);
    }

    public async Task<object> privateGetMarginLoanInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetMarginLoanInfo",parameters);
    }

    public async Task<object> privateGetFeeFeeRateGet (object parameters = null)
    {
        return await this.callAsync ("privateGetFeeFeeRateGet",parameters);
    }

    public async Task<object> privateGetOrderOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetOrderOpenOrders",parameters);
    }

    public async Task<object> privateGetOrderOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetOrderOrders",parameters);
    }

    public async Task<object> privateGetOrderOrdersId (object parameters = null)
    {
        return await this.callAsync ("privateGetOrderOrdersId",parameters);
    }

    public async Task<object> privateGetOrderOrdersIdMatchresults (object parameters = null)
    {
        return await this.callAsync ("privateGetOrderOrdersIdMatchresults",parameters);
    }

    public async Task<object> privateGetOrderOrdersGetClientOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetOrderOrdersGetClientOrder",parameters);
    }

    public async Task<object> privateGetOrderHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetOrderHistory",parameters);
    }

    public async Task<object> privateGetOrderMatchresults (object parameters = null)
    {
        return await this.callAsync ("privateGetOrderMatchresults",parameters);
    }

    public async Task<object> privateGetQueryDepositWithdraw (object parameters = null)
    {
        return await this.callAsync ("privateGetQueryDepositWithdraw",parameters);
    }

    public async Task<object> privateGetMarginLoanOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetMarginLoanOrders",parameters);
    }

    public async Task<object> privateGetMarginAccountsBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetMarginAccountsBalance",parameters);
    }

    public async Task<object> privateGetCrossMarginLoanOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetCrossMarginLoanOrders",parameters);
    }

    public async Task<object> privateGetCrossMarginAccountsBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetCrossMarginAccountsBalance",parameters);
    }

    public async Task<object> privateGetPointsActions (object parameters = null)
    {
        return await this.callAsync ("privateGetPointsActions",parameters);
    }

    public async Task<object> privateGetPointsOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetPointsOrders",parameters);
    }

    public async Task<object> privateGetSubuserAggregateBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetSubuserAggregateBalance",parameters);
    }

    public async Task<object> privateGetStableCoinExchangeRate (object parameters = null)
    {
        return await this.callAsync ("privateGetStableCoinExchangeRate",parameters);
    }

    public async Task<object> privateGetStableCoinQuote (object parameters = null)
    {
        return await this.callAsync ("privateGetStableCoinQuote",parameters);
    }

    public async Task<object> privatePostAccountTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountTransfer",parameters);
    }

    public async Task<object> privatePostFuturesTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostFuturesTransfer",parameters);
    }

    public async Task<object> privatePostOrderBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderBatchOrders",parameters);
    }

    public async Task<object> privatePostOrderOrdersPlace (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderOrdersPlace",parameters);
    }

    public async Task<object> privatePostOrderOrdersSubmitCancelClientOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderOrdersSubmitCancelClientOrder",parameters);
    }

    public async Task<object> privatePostOrderOrdersBatchCancelOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderOrdersBatchCancelOpenOrders",parameters);
    }

    public async Task<object> privatePostOrderOrdersIdSubmitcancel (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderOrdersIdSubmitcancel",parameters);
    }

    public async Task<object> privatePostOrderOrdersBatchcancel (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderOrdersBatchcancel",parameters);
    }

    public async Task<object> privatePostDwWithdrawApiCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostDwWithdrawApiCreate",parameters);
    }

    public async Task<object> privatePostDwWithdrawVirtualIdCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostDwWithdrawVirtualIdCancel",parameters);
    }

    public async Task<object> privatePostDwTransferInMargin (object parameters = null)
    {
        return await this.callAsync ("privatePostDwTransferInMargin",parameters);
    }

    public async Task<object> privatePostDwTransferOutMargin (object parameters = null)
    {
        return await this.callAsync ("privatePostDwTransferOutMargin",parameters);
    }

    public async Task<object> privatePostMarginOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostMarginOrders",parameters);
    }

    public async Task<object> privatePostMarginOrdersIdRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostMarginOrdersIdRepay",parameters);
    }

    public async Task<object> privatePostCrossMarginTransferIn (object parameters = null)
    {
        return await this.callAsync ("privatePostCrossMarginTransferIn",parameters);
    }

    public async Task<object> privatePostCrossMarginTransferOut (object parameters = null)
    {
        return await this.callAsync ("privatePostCrossMarginTransferOut",parameters);
    }

    public async Task<object> privatePostCrossMarginOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostCrossMarginOrders",parameters);
    }

    public async Task<object> privatePostCrossMarginOrdersIdRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostCrossMarginOrdersIdRepay",parameters);
    }

    public async Task<object> privatePostStableCoinExchange (object parameters = null)
    {
        return await this.callAsync ("privatePostStableCoinExchange",parameters);
    }

    public async Task<object> privatePostSubuserTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostSubuserTransfer",parameters);
    }

}