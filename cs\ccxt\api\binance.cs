// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class binance : Exchange
{
    public binance (object args = null): base(args) {}

    public async Task<object> sapiGetCopyTradingFuturesUserStatus (object parameters = null)
    {
        return await this.callAsync ("sapiGetCopyTradingFuturesUserStatus",parameters);
    }

    public async Task<object> sapiGetCopyTradingFuturesLeadSymbol (object parameters = null)
    {
        return await this.callAsync ("sapiGetCopyTradingFuturesLeadSymbol",parameters);
    }

    public async Task<object> sapiGetSystemStatus (object parameters = null)
    {
        return await this.callAsync ("sapiGetSystemStatus",parameters);
    }

    public async Task<object> sapiGetAccountSnapshot (object parameters = null)
    {
        return await this.callAsync ("sapiGetAccountSnapshot",parameters);
    }

    public async Task<object> sapiGetAccountInfo (object parameters = null)
    {
        return await this.callAsync ("sapiGetAccountInfo",parameters);
    }

    public async Task<object> sapiGetMarginAsset (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginAsset",parameters);
    }

    public async Task<object> sapiGetMarginPair (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginPair",parameters);
    }

    public async Task<object> sapiGetMarginAllAssets (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginAllAssets",parameters);
    }

    public async Task<object> sapiGetMarginAllPairs (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginAllPairs",parameters);
    }

    public async Task<object> sapiGetMarginPriceIndex (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginPriceIndex",parameters);
    }

    public async Task<object> sapiGetSpotDelistSchedule (object parameters = null)
    {
        return await this.callAsync ("sapiGetSpotDelistSchedule",parameters);
    }

    public async Task<object> sapiGetAssetAssetDividend (object parameters = null)
    {
        return await this.callAsync ("sapiGetAssetAssetDividend",parameters);
    }

    public async Task<object> sapiGetAssetDribblet (object parameters = null)
    {
        return await this.callAsync ("sapiGetAssetDribblet",parameters);
    }

    public async Task<object> sapiGetAssetTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiGetAssetTransfer",parameters);
    }

    public async Task<object> sapiGetAssetAssetDetail (object parameters = null)
    {
        return await this.callAsync ("sapiGetAssetAssetDetail",parameters);
    }

    public async Task<object> sapiGetAssetTradeFee (object parameters = null)
    {
        return await this.callAsync ("sapiGetAssetTradeFee",parameters);
    }

    public async Task<object> sapiGetAssetLedgerTransferCloudMiningQueryByPage (object parameters = null)
    {
        return await this.callAsync ("sapiGetAssetLedgerTransferCloudMiningQueryByPage",parameters);
    }

    public async Task<object> sapiGetAssetConvertTransferQueryByPage (object parameters = null)
    {
        return await this.callAsync ("sapiGetAssetConvertTransferQueryByPage",parameters);
    }

    public async Task<object> sapiGetAssetWalletBalance (object parameters = null)
    {
        return await this.callAsync ("sapiGetAssetWalletBalance",parameters);
    }

    public async Task<object> sapiGetAssetCustodyTransferHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetAssetCustodyTransferHistory",parameters);
    }

    public async Task<object> sapiGetMarginBorrowRepay (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginBorrowRepay",parameters);
    }

    public async Task<object> sapiGetMarginLoan (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginLoan",parameters);
    }

    public async Task<object> sapiGetMarginRepay (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginRepay",parameters);
    }

    public async Task<object> sapiGetMarginAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginAccount",parameters);
    }

    public async Task<object> sapiGetMarginTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginTransfer",parameters);
    }

    public async Task<object> sapiGetMarginInterestHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginInterestHistory",parameters);
    }

    public async Task<object> sapiGetMarginForceLiquidationRec (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginForceLiquidationRec",parameters);
    }

    public async Task<object> sapiGetMarginOrder (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginOrder",parameters);
    }

    public async Task<object> sapiGetMarginOpenOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginOpenOrders",parameters);
    }

    public async Task<object> sapiGetMarginAllOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginAllOrders",parameters);
    }

    public async Task<object> sapiGetMarginMyTrades (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginMyTrades",parameters);
    }

    public async Task<object> sapiGetMarginMaxBorrowable (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginMaxBorrowable",parameters);
    }

    public async Task<object> sapiGetMarginMaxTransferable (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginMaxTransferable",parameters);
    }

    public async Task<object> sapiGetMarginTradeCoeff (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginTradeCoeff",parameters);
    }

    public async Task<object> sapiGetMarginIsolatedTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginIsolatedTransfer",parameters);
    }

    public async Task<object> sapiGetMarginIsolatedAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginIsolatedAccount",parameters);
    }

    public async Task<object> sapiGetMarginIsolatedPair (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginIsolatedPair",parameters);
    }

    public async Task<object> sapiGetMarginIsolatedAllPairs (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginIsolatedAllPairs",parameters);
    }

    public async Task<object> sapiGetMarginIsolatedAccountLimit (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginIsolatedAccountLimit",parameters);
    }

    public async Task<object> sapiGetMarginInterestRateHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginInterestRateHistory",parameters);
    }

    public async Task<object> sapiGetMarginOrderList (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginOrderList",parameters);
    }

    public async Task<object> sapiGetMarginAllOrderList (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginAllOrderList",parameters);
    }

    public async Task<object> sapiGetMarginOpenOrderList (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginOpenOrderList",parameters);
    }

    public async Task<object> sapiGetMarginCrossMarginData (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginCrossMarginData",parameters);
    }

    public async Task<object> sapiGetMarginIsolatedMarginData (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginIsolatedMarginData",parameters);
    }

    public async Task<object> sapiGetMarginIsolatedMarginTier (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginIsolatedMarginTier",parameters);
    }

    public async Task<object> sapiGetMarginRateLimitOrder (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginRateLimitOrder",parameters);
    }

    public async Task<object> sapiGetMarginDribblet (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginDribblet",parameters);
    }

    public async Task<object> sapiGetMarginDust (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginDust",parameters);
    }

    public async Task<object> sapiGetMarginCrossMarginCollateralRatio (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginCrossMarginCollateralRatio",parameters);
    }

    public async Task<object> sapiGetMarginExchangeSmallLiability (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginExchangeSmallLiability",parameters);
    }

    public async Task<object> sapiGetMarginExchangeSmallLiabilityHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginExchangeSmallLiabilityHistory",parameters);
    }

    public async Task<object> sapiGetMarginNextHourlyInterestRate (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginNextHourlyInterestRate",parameters);
    }

    public async Task<object> sapiGetMarginCapitalFlow (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginCapitalFlow",parameters);
    }

    public async Task<object> sapiGetMarginDelistSchedule (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginDelistSchedule",parameters);
    }

    public async Task<object> sapiGetMarginAvailableInventory (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginAvailableInventory",parameters);
    }

    public async Task<object> sapiGetMarginLeverageBracket (object parameters = null)
    {
        return await this.callAsync ("sapiGetMarginLeverageBracket",parameters);
    }

    public async Task<object> sapiGetLoanVipLoanableData (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanVipLoanableData",parameters);
    }

    public async Task<object> sapiGetLoanVipCollateralData (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanVipCollateralData",parameters);
    }

    public async Task<object> sapiGetLoanVipRequestData (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanVipRequestData",parameters);
    }

    public async Task<object> sapiGetLoanVipRequestInterestRate (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanVipRequestInterestRate",parameters);
    }

    public async Task<object> sapiGetLoanIncome (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanIncome",parameters);
    }

    public async Task<object> sapiGetLoanOngoingOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanOngoingOrders",parameters);
    }

    public async Task<object> sapiGetLoanLtvAdjustmentHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanLtvAdjustmentHistory",parameters);
    }

    public async Task<object> sapiGetLoanBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanBorrowHistory",parameters);
    }

    public async Task<object> sapiGetLoanRepayHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanRepayHistory",parameters);
    }

    public async Task<object> sapiGetLoanLoanableData (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanLoanableData",parameters);
    }

    public async Task<object> sapiGetLoanCollateralData (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanCollateralData",parameters);
    }

    public async Task<object> sapiGetLoanRepayCollateralRate (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanRepayCollateralRate",parameters);
    }

    public async Task<object> sapiGetLoanFlexibleOngoingOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanFlexibleOngoingOrders",parameters);
    }

    public async Task<object> sapiGetLoanFlexibleBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanFlexibleBorrowHistory",parameters);
    }

    public async Task<object> sapiGetLoanFlexibleRepayHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanFlexibleRepayHistory",parameters);
    }

    public async Task<object> sapiGetLoanFlexibleLtvAdjustmentHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanFlexibleLtvAdjustmentHistory",parameters);
    }

    public async Task<object> sapiGetLoanVipOngoingOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanVipOngoingOrders",parameters);
    }

    public async Task<object> sapiGetLoanVipRepayHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanVipRepayHistory",parameters);
    }

    public async Task<object> sapiGetLoanVipCollateralAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetLoanVipCollateralAccount",parameters);
    }

    public async Task<object> sapiGetFiatOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetFiatOrders",parameters);
    }

    public async Task<object> sapiGetFiatPayments (object parameters = null)
    {
        return await this.callAsync ("sapiGetFiatPayments",parameters);
    }

    public async Task<object> sapiGetFuturesTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiGetFuturesTransfer",parameters);
    }

    public async Task<object> sapiGetFuturesHistDataLink (object parameters = null)
    {
        return await this.callAsync ("sapiGetFuturesHistDataLink",parameters);
    }

    public async Task<object> sapiGetRebateTaxQuery (object parameters = null)
    {
        return await this.callAsync ("sapiGetRebateTaxQuery",parameters);
    }

    public async Task<object> sapiGetCapitalConfigGetall (object parameters = null)
    {
        return await this.callAsync ("sapiGetCapitalConfigGetall",parameters);
    }

    public async Task<object> sapiGetCapitalDepositAddress (object parameters = null)
    {
        return await this.callAsync ("sapiGetCapitalDepositAddress",parameters);
    }

    public async Task<object> sapiGetCapitalDepositAddressList (object parameters = null)
    {
        return await this.callAsync ("sapiGetCapitalDepositAddressList",parameters);
    }

    public async Task<object> sapiGetCapitalDepositHisrec (object parameters = null)
    {
        return await this.callAsync ("sapiGetCapitalDepositHisrec",parameters);
    }

    public async Task<object> sapiGetCapitalDepositSubAddress (object parameters = null)
    {
        return await this.callAsync ("sapiGetCapitalDepositSubAddress",parameters);
    }

    public async Task<object> sapiGetCapitalDepositSubHisrec (object parameters = null)
    {
        return await this.callAsync ("sapiGetCapitalDepositSubHisrec",parameters);
    }

    public async Task<object> sapiGetCapitalWithdrawHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetCapitalWithdrawHistory",parameters);
    }

    public async Task<object> sapiGetCapitalWithdrawAddressList (object parameters = null)
    {
        return await this.callAsync ("sapiGetCapitalWithdrawAddressList",parameters);
    }

    public async Task<object> sapiGetCapitalContractConvertibleCoins (object parameters = null)
    {
        return await this.callAsync ("sapiGetCapitalContractConvertibleCoins",parameters);
    }

    public async Task<object> sapiGetConvertTradeFlow (object parameters = null)
    {
        return await this.callAsync ("sapiGetConvertTradeFlow",parameters);
    }

    public async Task<object> sapiGetConvertExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("sapiGetConvertExchangeInfo",parameters);
    }

    public async Task<object> sapiGetConvertAssetInfo (object parameters = null)
    {
        return await this.callAsync ("sapiGetConvertAssetInfo",parameters);
    }

    public async Task<object> sapiGetConvertOrderStatus (object parameters = null)
    {
        return await this.callAsync ("sapiGetConvertOrderStatus",parameters);
    }

    public async Task<object> sapiGetConvertLimitQueryOpenOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetConvertLimitQueryOpenOrders",parameters);
    }

    public async Task<object> sapiGetAccountStatus (object parameters = null)
    {
        return await this.callAsync ("sapiGetAccountStatus",parameters);
    }

    public async Task<object> sapiGetAccountApiTradingStatus (object parameters = null)
    {
        return await this.callAsync ("sapiGetAccountApiTradingStatus",parameters);
    }

    public async Task<object> sapiGetAccountApiRestrictionsIpRestriction (object parameters = null)
    {
        return await this.callAsync ("sapiGetAccountApiRestrictionsIpRestriction",parameters);
    }

    public async Task<object> sapiGetBnbBurn (object parameters = null)
    {
        return await this.callAsync ("sapiGetBnbBurn",parameters);
    }

    public async Task<object> sapiGetSubAccountFuturesAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountFuturesAccount",parameters);
    }

    public async Task<object> sapiGetSubAccountFuturesAccountSummary (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountFuturesAccountSummary",parameters);
    }

    public async Task<object> sapiGetSubAccountFuturesPositionRisk (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountFuturesPositionRisk",parameters);
    }

    public async Task<object> sapiGetSubAccountFuturesInternalTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountFuturesInternalTransfer",parameters);
    }

    public async Task<object> sapiGetSubAccountList (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountList",parameters);
    }

    public async Task<object> sapiGetSubAccountMarginAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountMarginAccount",parameters);
    }

    public async Task<object> sapiGetSubAccountMarginAccountSummary (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountMarginAccountSummary",parameters);
    }

    public async Task<object> sapiGetSubAccountSpotSummary (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountSpotSummary",parameters);
    }

    public async Task<object> sapiGetSubAccountStatus (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountStatus",parameters);
    }

    public async Task<object> sapiGetSubAccountSubTransferHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountSubTransferHistory",parameters);
    }

    public async Task<object> sapiGetSubAccountTransferSubUserHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountTransferSubUserHistory",parameters);
    }

    public async Task<object> sapiGetSubAccountUniversalTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountUniversalTransfer",parameters);
    }

    public async Task<object> sapiGetSubAccountApiRestrictionsIpRestrictionThirdPartyList (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountApiRestrictionsIpRestrictionThirdPartyList",parameters);
    }

    public async Task<object> sapiGetSubAccountTransactionStatistics (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountTransactionStatistics",parameters);
    }

    public async Task<object> sapiGetSubAccountSubAccountApiIpRestriction (object parameters = null)
    {
        return await this.callAsync ("sapiGetSubAccountSubAccountApiIpRestriction",parameters);
    }

    public async Task<object> sapiGetManagedSubaccountAsset (object parameters = null)
    {
        return await this.callAsync ("sapiGetManagedSubaccountAsset",parameters);
    }

    public async Task<object> sapiGetManagedSubaccountAccountSnapshot (object parameters = null)
    {
        return await this.callAsync ("sapiGetManagedSubaccountAccountSnapshot",parameters);
    }

    public async Task<object> sapiGetManagedSubaccountQueryTransLogForInvestor (object parameters = null)
    {
        return await this.callAsync ("sapiGetManagedSubaccountQueryTransLogForInvestor",parameters);
    }

    public async Task<object> sapiGetManagedSubaccountQueryTransLogForTradeParent (object parameters = null)
    {
        return await this.callAsync ("sapiGetManagedSubaccountQueryTransLogForTradeParent",parameters);
    }

    public async Task<object> sapiGetManagedSubaccountFetchFutureAsset (object parameters = null)
    {
        return await this.callAsync ("sapiGetManagedSubaccountFetchFutureAsset",parameters);
    }

    public async Task<object> sapiGetManagedSubaccountMarginAsset (object parameters = null)
    {
        return await this.callAsync ("sapiGetManagedSubaccountMarginAsset",parameters);
    }

    public async Task<object> sapiGetManagedSubaccountInfo (object parameters = null)
    {
        return await this.callAsync ("sapiGetManagedSubaccountInfo",parameters);
    }

    public async Task<object> sapiGetManagedSubaccountDepositAddress (object parameters = null)
    {
        return await this.callAsync ("sapiGetManagedSubaccountDepositAddress",parameters);
    }

    public async Task<object> sapiGetManagedSubaccountQueryTransLog (object parameters = null)
    {
        return await this.callAsync ("sapiGetManagedSubaccountQueryTransLog",parameters);
    }

    public async Task<object> sapiGetLendingDailyProductList (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingDailyProductList",parameters);
    }

    public async Task<object> sapiGetLendingDailyUserLeftQuota (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingDailyUserLeftQuota",parameters);
    }

    public async Task<object> sapiGetLendingDailyUserRedemptionQuota (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingDailyUserRedemptionQuota",parameters);
    }

    public async Task<object> sapiGetLendingDailyTokenPosition (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingDailyTokenPosition",parameters);
    }

    public async Task<object> sapiGetLendingUnionAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingUnionAccount",parameters);
    }

    public async Task<object> sapiGetLendingUnionPurchaseRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingUnionPurchaseRecord",parameters);
    }

    public async Task<object> sapiGetLendingUnionRedemptionRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingUnionRedemptionRecord",parameters);
    }

    public async Task<object> sapiGetLendingUnionInterestHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingUnionInterestHistory",parameters);
    }

    public async Task<object> sapiGetLendingProjectList (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingProjectList",parameters);
    }

    public async Task<object> sapiGetLendingProjectPositionList (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingProjectPositionList",parameters);
    }

    public async Task<object> sapiGetEthStakingEthHistoryStakingHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetEthStakingEthHistoryStakingHistory",parameters);
    }

    public async Task<object> sapiGetEthStakingEthHistoryRedemptionHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetEthStakingEthHistoryRedemptionHistory",parameters);
    }

    public async Task<object> sapiGetEthStakingEthHistoryRewardsHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetEthStakingEthHistoryRewardsHistory",parameters);
    }

    public async Task<object> sapiGetEthStakingEthQuota (object parameters = null)
    {
        return await this.callAsync ("sapiGetEthStakingEthQuota",parameters);
    }

    public async Task<object> sapiGetEthStakingEthHistoryRateHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetEthStakingEthHistoryRateHistory",parameters);
    }

    public async Task<object> sapiGetEthStakingAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetEthStakingAccount",parameters);
    }

    public async Task<object> sapiGetEthStakingWbethHistoryWrapHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetEthStakingWbethHistoryWrapHistory",parameters);
    }

    public async Task<object> sapiGetEthStakingWbethHistoryUnwrapHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetEthStakingWbethHistoryUnwrapHistory",parameters);
    }

    public async Task<object> sapiGetEthStakingEthHistoryWbethRewardsHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetEthStakingEthHistoryWbethRewardsHistory",parameters);
    }

    public async Task<object> sapiGetSolStakingSolHistoryStakingHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetSolStakingSolHistoryStakingHistory",parameters);
    }

    public async Task<object> sapiGetSolStakingSolHistoryRedemptionHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetSolStakingSolHistoryRedemptionHistory",parameters);
    }

    public async Task<object> sapiGetSolStakingSolHistoryBnsolRewardsHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetSolStakingSolHistoryBnsolRewardsHistory",parameters);
    }

    public async Task<object> sapiGetSolStakingSolHistoryRateHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetSolStakingSolHistoryRateHistory",parameters);
    }

    public async Task<object> sapiGetSolStakingAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetSolStakingAccount",parameters);
    }

    public async Task<object> sapiGetSolStakingSolQuota (object parameters = null)
    {
        return await this.callAsync ("sapiGetSolStakingSolQuota",parameters);
    }

    public async Task<object> sapiGetMiningPubAlgoList (object parameters = null)
    {
        return await this.callAsync ("sapiGetMiningPubAlgoList",parameters);
    }

    public async Task<object> sapiGetMiningPubCoinList (object parameters = null)
    {
        return await this.callAsync ("sapiGetMiningPubCoinList",parameters);
    }

    public async Task<object> sapiGetMiningWorkerDetail (object parameters = null)
    {
        return await this.callAsync ("sapiGetMiningWorkerDetail",parameters);
    }

    public async Task<object> sapiGetMiningWorkerList (object parameters = null)
    {
        return await this.callAsync ("sapiGetMiningWorkerList",parameters);
    }

    public async Task<object> sapiGetMiningPaymentList (object parameters = null)
    {
        return await this.callAsync ("sapiGetMiningPaymentList",parameters);
    }

    public async Task<object> sapiGetMiningStatisticsUserStatus (object parameters = null)
    {
        return await this.callAsync ("sapiGetMiningStatisticsUserStatus",parameters);
    }

    public async Task<object> sapiGetMiningStatisticsUserList (object parameters = null)
    {
        return await this.callAsync ("sapiGetMiningStatisticsUserList",parameters);
    }

    public async Task<object> sapiGetMiningPaymentUid (object parameters = null)
    {
        return await this.callAsync ("sapiGetMiningPaymentUid",parameters);
    }

    public async Task<object> sapiGetBswapPools (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapPools",parameters);
    }

    public async Task<object> sapiGetBswapLiquidity (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapLiquidity",parameters);
    }

    public async Task<object> sapiGetBswapLiquidityOps (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapLiquidityOps",parameters);
    }

    public async Task<object> sapiGetBswapQuote (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapQuote",parameters);
    }

    public async Task<object> sapiGetBswapSwap (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapSwap",parameters);
    }

    public async Task<object> sapiGetBswapPoolConfigure (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapPoolConfigure",parameters);
    }

    public async Task<object> sapiGetBswapAddLiquidityPreview (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapAddLiquidityPreview",parameters);
    }

    public async Task<object> sapiGetBswapRemoveLiquidityPreview (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapRemoveLiquidityPreview",parameters);
    }

    public async Task<object> sapiGetBswapUnclaimedRewards (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapUnclaimedRewards",parameters);
    }

    public async Task<object> sapiGetBswapClaimedHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetBswapClaimedHistory",parameters);
    }

    public async Task<object> sapiGetBlvtTokenInfo (object parameters = null)
    {
        return await this.callAsync ("sapiGetBlvtTokenInfo",parameters);
    }

    public async Task<object> sapiGetBlvtSubscribeRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetBlvtSubscribeRecord",parameters);
    }

    public async Task<object> sapiGetBlvtRedeemRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetBlvtRedeemRecord",parameters);
    }

    public async Task<object> sapiGetBlvtUserLimit (object parameters = null)
    {
        return await this.callAsync ("sapiGetBlvtUserLimit",parameters);
    }

    public async Task<object> sapiGetApiReferralIfNewUser (object parameters = null)
    {
        return await this.callAsync ("sapiGetApiReferralIfNewUser",parameters);
    }

    public async Task<object> sapiGetApiReferralCustomization (object parameters = null)
    {
        return await this.callAsync ("sapiGetApiReferralCustomization",parameters);
    }

    public async Task<object> sapiGetApiReferralUserCustomization (object parameters = null)
    {
        return await this.callAsync ("sapiGetApiReferralUserCustomization",parameters);
    }

    public async Task<object> sapiGetApiReferralRebateRecentRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetApiReferralRebateRecentRecord",parameters);
    }

    public async Task<object> sapiGetApiReferralRebateHistoricalRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetApiReferralRebateHistoricalRecord",parameters);
    }

    public async Task<object> sapiGetApiReferralKickbackRecentRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetApiReferralKickbackRecentRecord",parameters);
    }

    public async Task<object> sapiGetApiReferralKickbackHistoricalRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetApiReferralKickbackHistoricalRecord",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccountApi (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccountApi",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccount",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccountApiCommissionFutures (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccountApiCommissionFutures",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccountApiCommissionCoinFutures (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccountApiCommissionCoinFutures",parameters);
    }

    public async Task<object> sapiGetBrokerInfo (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerInfo",parameters);
    }

    public async Task<object> sapiGetBrokerTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerTransfer",parameters);
    }

    public async Task<object> sapiGetBrokerTransferFutures (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerTransferFutures",parameters);
    }

    public async Task<object> sapiGetBrokerRebateRecentRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerRebateRecentRecord",parameters);
    }

    public async Task<object> sapiGetBrokerRebateHistoricalRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerRebateHistoricalRecord",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccountBnbBurnStatus (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccountBnbBurnStatus",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccountDepositHist (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccountDepositHist",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccountSpotSummary (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccountSpotSummary",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccountMarginSummary (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccountMarginSummary",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccountFuturesSummary (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccountFuturesSummary",parameters);
    }

    public async Task<object> sapiGetBrokerRebateFuturesRecentRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerRebateFuturesRecentRecord",parameters);
    }

    public async Task<object> sapiGetBrokerSubAccountApiIpRestriction (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerSubAccountApiIpRestriction",parameters);
    }

    public async Task<object> sapiGetBrokerUniversalTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiGetBrokerUniversalTransfer",parameters);
    }

    public async Task<object> sapiGetAccountApiRestrictions (object parameters = null)
    {
        return await this.callAsync ("sapiGetAccountApiRestrictions",parameters);
    }

    public async Task<object> sapiGetC2cOrderMatchListUserOrderHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetC2cOrderMatchListUserOrderHistory",parameters);
    }

    public async Task<object> sapiGetNftHistoryTransactions (object parameters = null)
    {
        return await this.callAsync ("sapiGetNftHistoryTransactions",parameters);
    }

    public async Task<object> sapiGetNftHistoryDeposit (object parameters = null)
    {
        return await this.callAsync ("sapiGetNftHistoryDeposit",parameters);
    }

    public async Task<object> sapiGetNftHistoryWithdraw (object parameters = null)
    {
        return await this.callAsync ("sapiGetNftHistoryWithdraw",parameters);
    }

    public async Task<object> sapiGetNftUserGetAsset (object parameters = null)
    {
        return await this.callAsync ("sapiGetNftUserGetAsset",parameters);
    }

    public async Task<object> sapiGetPayTransactions (object parameters = null)
    {
        return await this.callAsync ("sapiGetPayTransactions",parameters);
    }

    public async Task<object> sapiGetGiftcardVerify (object parameters = null)
    {
        return await this.callAsync ("sapiGetGiftcardVerify",parameters);
    }

    public async Task<object> sapiGetGiftcardCryptographyRsaPublicKey (object parameters = null)
    {
        return await this.callAsync ("sapiGetGiftcardCryptographyRsaPublicKey",parameters);
    }

    public async Task<object> sapiGetGiftcardBuyCodeTokenLimit (object parameters = null)
    {
        return await this.callAsync ("sapiGetGiftcardBuyCodeTokenLimit",parameters);
    }

    public async Task<object> sapiGetAlgoSpotOpenOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetAlgoSpotOpenOrders",parameters);
    }

    public async Task<object> sapiGetAlgoSpotHistoricalOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetAlgoSpotHistoricalOrders",parameters);
    }

    public async Task<object> sapiGetAlgoSpotSubOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetAlgoSpotSubOrders",parameters);
    }

    public async Task<object> sapiGetAlgoFuturesOpenOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetAlgoFuturesOpenOrders",parameters);
    }

    public async Task<object> sapiGetAlgoFuturesHistoricalOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetAlgoFuturesHistoricalOrders",parameters);
    }

    public async Task<object> sapiGetAlgoFuturesSubOrders (object parameters = null)
    {
        return await this.callAsync ("sapiGetAlgoFuturesSubOrders",parameters);
    }

    public async Task<object> sapiGetPortfolioAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioAccount",parameters);
    }

    public async Task<object> sapiGetPortfolioCollateralRate (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioCollateralRate",parameters);
    }

    public async Task<object> sapiGetPortfolioPmLoan (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioPmLoan",parameters);
    }

    public async Task<object> sapiGetPortfolioInterestHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioInterestHistory",parameters);
    }

    public async Task<object> sapiGetPortfolioAssetIndexPrice (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioAssetIndexPrice",parameters);
    }

    public async Task<object> sapiGetPortfolioRepayFuturesSwitch (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioRepayFuturesSwitch",parameters);
    }

    public async Task<object> sapiGetPortfolioMarginAssetLeverage (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioMarginAssetLeverage",parameters);
    }

    public async Task<object> sapiGetPortfolioBalance (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioBalance",parameters);
    }

    public async Task<object> sapiGetPortfolioNegativeBalanceExchangeRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioNegativeBalanceExchangeRecord",parameters);
    }

    public async Task<object> sapiGetPortfolioPmloanHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetPortfolioPmloanHistory",parameters);
    }

    public async Task<object> sapiGetStakingProductList (object parameters = null)
    {
        return await this.callAsync ("sapiGetStakingProductList",parameters);
    }

    public async Task<object> sapiGetStakingPosition (object parameters = null)
    {
        return await this.callAsync ("sapiGetStakingPosition",parameters);
    }

    public async Task<object> sapiGetStakingStakingRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetStakingStakingRecord",parameters);
    }

    public async Task<object> sapiGetStakingPersonalLeftQuota (object parameters = null)
    {
        return await this.callAsync ("sapiGetStakingPersonalLeftQuota",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestTargetAssetList (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestTargetAssetList",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestTargetAssetRoiList (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestTargetAssetRoiList",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestAllAsset (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestAllAsset",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestSourceAssetList (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestSourceAssetList",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestPlanList (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestPlanList",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestPlanId (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestPlanId",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestHistoryList (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestHistoryList",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestIndexInfo (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestIndexInfo",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestIndexUserSummary (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestIndexUserSummary",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestOneOffStatus (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestOneOffStatus",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestRedeemHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestRedeemHistory",parameters);
    }

    public async Task<object> sapiGetLendingAutoInvestRebalanceHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetLendingAutoInvestRebalanceHistory",parameters);
    }

    public async Task<object> sapiGetSimpleEarnFlexibleList (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnFlexibleList",parameters);
    }

    public async Task<object> sapiGetSimpleEarnLockedList (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnLockedList",parameters);
    }

    public async Task<object> sapiGetSimpleEarnFlexiblePersonalLeftQuota (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnFlexiblePersonalLeftQuota",parameters);
    }

    public async Task<object> sapiGetSimpleEarnLockedPersonalLeftQuota (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnLockedPersonalLeftQuota",parameters);
    }

    public async Task<object> sapiGetSimpleEarnFlexibleSubscriptionPreview (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnFlexibleSubscriptionPreview",parameters);
    }

    public async Task<object> sapiGetSimpleEarnLockedSubscriptionPreview (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnLockedSubscriptionPreview",parameters);
    }

    public async Task<object> sapiGetSimpleEarnFlexibleHistoryRateHistory (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnFlexibleHistoryRateHistory",parameters);
    }

    public async Task<object> sapiGetSimpleEarnFlexiblePosition (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnFlexiblePosition",parameters);
    }

    public async Task<object> sapiGetSimpleEarnLockedPosition (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnLockedPosition",parameters);
    }

    public async Task<object> sapiGetSimpleEarnAccount (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnAccount",parameters);
    }

    public async Task<object> sapiGetSimpleEarnFlexibleHistorySubscriptionRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnFlexibleHistorySubscriptionRecord",parameters);
    }

    public async Task<object> sapiGetSimpleEarnLockedHistorySubscriptionRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnLockedHistorySubscriptionRecord",parameters);
    }

    public async Task<object> sapiGetSimpleEarnFlexibleHistoryRedemptionRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnFlexibleHistoryRedemptionRecord",parameters);
    }

    public async Task<object> sapiGetSimpleEarnLockedHistoryRedemptionRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnLockedHistoryRedemptionRecord",parameters);
    }

    public async Task<object> sapiGetSimpleEarnFlexibleHistoryRewardsRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnFlexibleHistoryRewardsRecord",parameters);
    }

    public async Task<object> sapiGetSimpleEarnLockedHistoryRewardsRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnLockedHistoryRewardsRecord",parameters);
    }

    public async Task<object> sapiGetSimpleEarnFlexibleHistoryCollateralRecord (object parameters = null)
    {
        return await this.callAsync ("sapiGetSimpleEarnFlexibleHistoryCollateralRecord",parameters);
    }

    public async Task<object> sapiGetDciProductList (object parameters = null)
    {
        return await this.callAsync ("sapiGetDciProductList",parameters);
    }

    public async Task<object> sapiGetDciProductPositions (object parameters = null)
    {
        return await this.callAsync ("sapiGetDciProductPositions",parameters);
    }

    public async Task<object> sapiGetDciProductAccounts (object parameters = null)
    {
        return await this.callAsync ("sapiGetDciProductAccounts",parameters);
    }

    public async Task<object> sapiPostAssetDust (object parameters = null)
    {
        return await this.callAsync ("sapiPostAssetDust",parameters);
    }

    public async Task<object> sapiPostAssetDustBtc (object parameters = null)
    {
        return await this.callAsync ("sapiPostAssetDustBtc",parameters);
    }

    public async Task<object> sapiPostAssetTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostAssetTransfer",parameters);
    }

    public async Task<object> sapiPostAssetGetFundingAsset (object parameters = null)
    {
        return await this.callAsync ("sapiPostAssetGetFundingAsset",parameters);
    }

    public async Task<object> sapiPostAssetConvertTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostAssetConvertTransfer",parameters);
    }

    public async Task<object> sapiPostAccountDisableFastWithdrawSwitch (object parameters = null)
    {
        return await this.callAsync ("sapiPostAccountDisableFastWithdrawSwitch",parameters);
    }

    public async Task<object> sapiPostAccountEnableFastWithdrawSwitch (object parameters = null)
    {
        return await this.callAsync ("sapiPostAccountEnableFastWithdrawSwitch",parameters);
    }

    public async Task<object> sapiPostCapitalWithdrawApply (object parameters = null)
    {
        return await this.callAsync ("sapiPostCapitalWithdrawApply",parameters);
    }

    public async Task<object> sapiPostCapitalContractConvertibleCoins (object parameters = null)
    {
        return await this.callAsync ("sapiPostCapitalContractConvertibleCoins",parameters);
    }

    public async Task<object> sapiPostCapitalDepositCreditApply (object parameters = null)
    {
        return await this.callAsync ("sapiPostCapitalDepositCreditApply",parameters);
    }

    public async Task<object> sapiPostMarginBorrowRepay (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginBorrowRepay",parameters);
    }

    public async Task<object> sapiPostMarginTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginTransfer",parameters);
    }

    public async Task<object> sapiPostMarginLoan (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginLoan",parameters);
    }

    public async Task<object> sapiPostMarginRepay (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginRepay",parameters);
    }

    public async Task<object> sapiPostMarginOrder (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginOrder",parameters);
    }

    public async Task<object> sapiPostMarginOrderOco (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginOrderOco",parameters);
    }

    public async Task<object> sapiPostMarginDust (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginDust",parameters);
    }

    public async Task<object> sapiPostMarginExchangeSmallLiability (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginExchangeSmallLiability",parameters);
    }

    public async Task<object> sapiPostMarginIsolatedTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginIsolatedTransfer",parameters);
    }

    public async Task<object> sapiPostMarginIsolatedAccount (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginIsolatedAccount",parameters);
    }

    public async Task<object> sapiPostMarginMaxLeverage (object parameters = null)
    {
        return await this.callAsync ("sapiPostMarginMaxLeverage",parameters);
    }

    public async Task<object> sapiPostBnbBurn (object parameters = null)
    {
        return await this.callAsync ("sapiPostBnbBurn",parameters);
    }

    public async Task<object> sapiPostSubAccountVirtualSubAccount (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountVirtualSubAccount",parameters);
    }

    public async Task<object> sapiPostSubAccountMarginTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountMarginTransfer",parameters);
    }

    public async Task<object> sapiPostSubAccountMarginEnable (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountMarginEnable",parameters);
    }

    public async Task<object> sapiPostSubAccountFuturesEnable (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountFuturesEnable",parameters);
    }

    public async Task<object> sapiPostSubAccountFuturesTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountFuturesTransfer",parameters);
    }

    public async Task<object> sapiPostSubAccountFuturesInternalTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountFuturesInternalTransfer",parameters);
    }

    public async Task<object> sapiPostSubAccountTransferSubToSub (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountTransferSubToSub",parameters);
    }

    public async Task<object> sapiPostSubAccountTransferSubToMaster (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountTransferSubToMaster",parameters);
    }

    public async Task<object> sapiPostSubAccountUniversalTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountUniversalTransfer",parameters);
    }

    public async Task<object> sapiPostSubAccountOptionsEnable (object parameters = null)
    {
        return await this.callAsync ("sapiPostSubAccountOptionsEnable",parameters);
    }

    public async Task<object> sapiPostManagedSubaccountDeposit (object parameters = null)
    {
        return await this.callAsync ("sapiPostManagedSubaccountDeposit",parameters);
    }

    public async Task<object> sapiPostManagedSubaccountWithdraw (object parameters = null)
    {
        return await this.callAsync ("sapiPostManagedSubaccountWithdraw",parameters);
    }

    public async Task<object> sapiPostUserDataStream (object parameters = null)
    {
        return await this.callAsync ("sapiPostUserDataStream",parameters);
    }

    public async Task<object> sapiPostUserDataStreamIsolated (object parameters = null)
    {
        return await this.callAsync ("sapiPostUserDataStreamIsolated",parameters);
    }

    public async Task<object> sapiPostFuturesTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostFuturesTransfer",parameters);
    }

    public async Task<object> sapiPostLendingCustomizedFixedPurchase (object parameters = null)
    {
        return await this.callAsync ("sapiPostLendingCustomizedFixedPurchase",parameters);
    }

    public async Task<object> sapiPostLendingDailyPurchase (object parameters = null)
    {
        return await this.callAsync ("sapiPostLendingDailyPurchase",parameters);
    }

    public async Task<object> sapiPostLendingDailyRedeem (object parameters = null)
    {
        return await this.callAsync ("sapiPostLendingDailyRedeem",parameters);
    }

    public async Task<object> sapiPostBswapLiquidityAdd (object parameters = null)
    {
        return await this.callAsync ("sapiPostBswapLiquidityAdd",parameters);
    }

    public async Task<object> sapiPostBswapLiquidityRemove (object parameters = null)
    {
        return await this.callAsync ("sapiPostBswapLiquidityRemove",parameters);
    }

    public async Task<object> sapiPostBswapSwap (object parameters = null)
    {
        return await this.callAsync ("sapiPostBswapSwap",parameters);
    }

    public async Task<object> sapiPostBswapClaimRewards (object parameters = null)
    {
        return await this.callAsync ("sapiPostBswapClaimRewards",parameters);
    }

    public async Task<object> sapiPostBlvtSubscribe (object parameters = null)
    {
        return await this.callAsync ("sapiPostBlvtSubscribe",parameters);
    }

    public async Task<object> sapiPostBlvtRedeem (object parameters = null)
    {
        return await this.callAsync ("sapiPostBlvtRedeem",parameters);
    }

    public async Task<object> sapiPostApiReferralCustomization (object parameters = null)
    {
        return await this.callAsync ("sapiPostApiReferralCustomization",parameters);
    }

    public async Task<object> sapiPostApiReferralUserCustomization (object parameters = null)
    {
        return await this.callAsync ("sapiPostApiReferralUserCustomization",parameters);
    }

    public async Task<object> sapiPostApiReferralRebateHistoricalRecord (object parameters = null)
    {
        return await this.callAsync ("sapiPostApiReferralRebateHistoricalRecord",parameters);
    }

    public async Task<object> sapiPostApiReferralKickbackHistoricalRecord (object parameters = null)
    {
        return await this.callAsync ("sapiPostApiReferralKickbackHistoricalRecord",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccount (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccount",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountMargin (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountMargin",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountFutures (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountFutures",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountApi (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountApi",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountApiPermission (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountApiPermission",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountApiCommission (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountApiCommission",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountApiCommissionFutures (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountApiCommissionFutures",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountApiCommissionCoinFutures (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountApiCommissionCoinFutures",parameters);
    }

    public async Task<object> sapiPostBrokerTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerTransfer",parameters);
    }

    public async Task<object> sapiPostBrokerTransferFutures (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerTransferFutures",parameters);
    }

    public async Task<object> sapiPostBrokerRebateHistoricalRecord (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerRebateHistoricalRecord",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountBnbBurnSpot (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountBnbBurnSpot",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountBnbBurnMarginInterest (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountBnbBurnMarginInterest",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountBlvt (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountBlvt",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountApiIpRestriction (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountApiIpRestriction",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountApiIpRestrictionIpList (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountApiIpRestrictionIpList",parameters);
    }

    public async Task<object> sapiPostBrokerUniversalTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerUniversalTransfer",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountApiPermissionUniversalTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountApiPermissionUniversalTransfer",parameters);
    }

    public async Task<object> sapiPostBrokerSubAccountApiPermissionVanillaOptions (object parameters = null)
    {
        return await this.callAsync ("sapiPostBrokerSubAccountApiPermissionVanillaOptions",parameters);
    }

    public async Task<object> sapiPostGiftcardCreateCode (object parameters = null)
    {
        return await this.callAsync ("sapiPostGiftcardCreateCode",parameters);
    }

    public async Task<object> sapiPostGiftcardRedeemCode (object parameters = null)
    {
        return await this.callAsync ("sapiPostGiftcardRedeemCode",parameters);
    }

    public async Task<object> sapiPostGiftcardBuyCode (object parameters = null)
    {
        return await this.callAsync ("sapiPostGiftcardBuyCode",parameters);
    }

    public async Task<object> sapiPostAlgoSpotNewOrderTwap (object parameters = null)
    {
        return await this.callAsync ("sapiPostAlgoSpotNewOrderTwap",parameters);
    }

    public async Task<object> sapiPostAlgoFuturesNewOrderVp (object parameters = null)
    {
        return await this.callAsync ("sapiPostAlgoFuturesNewOrderVp",parameters);
    }

    public async Task<object> sapiPostAlgoFuturesNewOrderTwap (object parameters = null)
    {
        return await this.callAsync ("sapiPostAlgoFuturesNewOrderTwap",parameters);
    }

    public async Task<object> sapiPostStakingPurchase (object parameters = null)
    {
        return await this.callAsync ("sapiPostStakingPurchase",parameters);
    }

    public async Task<object> sapiPostStakingRedeem (object parameters = null)
    {
        return await this.callAsync ("sapiPostStakingRedeem",parameters);
    }

    public async Task<object> sapiPostStakingSetAutoStaking (object parameters = null)
    {
        return await this.callAsync ("sapiPostStakingSetAutoStaking",parameters);
    }

    public async Task<object> sapiPostEthStakingEthStake (object parameters = null)
    {
        return await this.callAsync ("sapiPostEthStakingEthStake",parameters);
    }

    public async Task<object> sapiPostEthStakingEthRedeem (object parameters = null)
    {
        return await this.callAsync ("sapiPostEthStakingEthRedeem",parameters);
    }

    public async Task<object> sapiPostEthStakingWbethWrap (object parameters = null)
    {
        return await this.callAsync ("sapiPostEthStakingWbethWrap",parameters);
    }

    public async Task<object> sapiPostSolStakingSolStake (object parameters = null)
    {
        return await this.callAsync ("sapiPostSolStakingSolStake",parameters);
    }

    public async Task<object> sapiPostSolStakingSolRedeem (object parameters = null)
    {
        return await this.callAsync ("sapiPostSolStakingSolRedeem",parameters);
    }

    public async Task<object> sapiPostMiningHashTransferConfig (object parameters = null)
    {
        return await this.callAsync ("sapiPostMiningHashTransferConfig",parameters);
    }

    public async Task<object> sapiPostMiningHashTransferConfigCancel (object parameters = null)
    {
        return await this.callAsync ("sapiPostMiningHashTransferConfigCancel",parameters);
    }

    public async Task<object> sapiPostPortfolioRepay (object parameters = null)
    {
        return await this.callAsync ("sapiPostPortfolioRepay",parameters);
    }

    public async Task<object> sapiPostLoanVipRenew (object parameters = null)
    {
        return await this.callAsync ("sapiPostLoanVipRenew",parameters);
    }

    public async Task<object> sapiPostLoanVipBorrow (object parameters = null)
    {
        return await this.callAsync ("sapiPostLoanVipBorrow",parameters);
    }

    public async Task<object> sapiPostLoanBorrow (object parameters = null)
    {
        return await this.callAsync ("sapiPostLoanBorrow",parameters);
    }

    public async Task<object> sapiPostLoanRepay (object parameters = null)
    {
        return await this.callAsync ("sapiPostLoanRepay",parameters);
    }

    public async Task<object> sapiPostLoanAdjustLtv (object parameters = null)
    {
        return await this.callAsync ("sapiPostLoanAdjustLtv",parameters);
    }

    public async Task<object> sapiPostLoanCustomizeMarginCall (object parameters = null)
    {
        return await this.callAsync ("sapiPostLoanCustomizeMarginCall",parameters);
    }

    public async Task<object> sapiPostLoanFlexibleRepay (object parameters = null)
    {
        return await this.callAsync ("sapiPostLoanFlexibleRepay",parameters);
    }

    public async Task<object> sapiPostLoanFlexibleAdjustLtv (object parameters = null)
    {
        return await this.callAsync ("sapiPostLoanFlexibleAdjustLtv",parameters);
    }

    public async Task<object> sapiPostLoanVipRepay (object parameters = null)
    {
        return await this.callAsync ("sapiPostLoanVipRepay",parameters);
    }

    public async Task<object> sapiPostConvertGetQuote (object parameters = null)
    {
        return await this.callAsync ("sapiPostConvertGetQuote",parameters);
    }

    public async Task<object> sapiPostConvertAcceptQuote (object parameters = null)
    {
        return await this.callAsync ("sapiPostConvertAcceptQuote",parameters);
    }

    public async Task<object> sapiPostConvertLimitPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("sapiPostConvertLimitPlaceOrder",parameters);
    }

    public async Task<object> sapiPostConvertLimitCancelOrder (object parameters = null)
    {
        return await this.callAsync ("sapiPostConvertLimitCancelOrder",parameters);
    }

    public async Task<object> sapiPostPortfolioAutoCollection (object parameters = null)
    {
        return await this.callAsync ("sapiPostPortfolioAutoCollection",parameters);
    }

    public async Task<object> sapiPostPortfolioAssetCollection (object parameters = null)
    {
        return await this.callAsync ("sapiPostPortfolioAssetCollection",parameters);
    }

    public async Task<object> sapiPostPortfolioBnbTransfer (object parameters = null)
    {
        return await this.callAsync ("sapiPostPortfolioBnbTransfer",parameters);
    }

    public async Task<object> sapiPostPortfolioRepayFuturesSwitch (object parameters = null)
    {
        return await this.callAsync ("sapiPostPortfolioRepayFuturesSwitch",parameters);
    }

    public async Task<object> sapiPostPortfolioRepayFuturesNegativeBalance (object parameters = null)
    {
        return await this.callAsync ("sapiPostPortfolioRepayFuturesNegativeBalance",parameters);
    }

    public async Task<object> sapiPostPortfolioMint (object parameters = null)
    {
        return await this.callAsync ("sapiPostPortfolioMint",parameters);
    }

    public async Task<object> sapiPostPortfolioRedeem (object parameters = null)
    {
        return await this.callAsync ("sapiPostPortfolioRedeem",parameters);
    }

    public async Task<object> sapiPostLendingAutoInvestPlanAdd (object parameters = null)
    {
        return await this.callAsync ("sapiPostLendingAutoInvestPlanAdd",parameters);
    }

    public async Task<object> sapiPostLendingAutoInvestPlanEdit (object parameters = null)
    {
        return await this.callAsync ("sapiPostLendingAutoInvestPlanEdit",parameters);
    }

    public async Task<object> sapiPostLendingAutoInvestPlanEditStatus (object parameters = null)
    {
        return await this.callAsync ("sapiPostLendingAutoInvestPlanEditStatus",parameters);
    }

    public async Task<object> sapiPostLendingAutoInvestOneOff (object parameters = null)
    {
        return await this.callAsync ("sapiPostLendingAutoInvestOneOff",parameters);
    }

    public async Task<object> sapiPostLendingAutoInvestRedeem (object parameters = null)
    {
        return await this.callAsync ("sapiPostLendingAutoInvestRedeem",parameters);
    }

    public async Task<object> sapiPostSimpleEarnFlexibleSubscribe (object parameters = null)
    {
        return await this.callAsync ("sapiPostSimpleEarnFlexibleSubscribe",parameters);
    }

    public async Task<object> sapiPostSimpleEarnLockedSubscribe (object parameters = null)
    {
        return await this.callAsync ("sapiPostSimpleEarnLockedSubscribe",parameters);
    }

    public async Task<object> sapiPostSimpleEarnFlexibleRedeem (object parameters = null)
    {
        return await this.callAsync ("sapiPostSimpleEarnFlexibleRedeem",parameters);
    }

    public async Task<object> sapiPostSimpleEarnLockedRedeem (object parameters = null)
    {
        return await this.callAsync ("sapiPostSimpleEarnLockedRedeem",parameters);
    }

    public async Task<object> sapiPostSimpleEarnFlexibleSetAutoSubscribe (object parameters = null)
    {
        return await this.callAsync ("sapiPostSimpleEarnFlexibleSetAutoSubscribe",parameters);
    }

    public async Task<object> sapiPostSimpleEarnLockedSetAutoSubscribe (object parameters = null)
    {
        return await this.callAsync ("sapiPostSimpleEarnLockedSetAutoSubscribe",parameters);
    }

    public async Task<object> sapiPostSimpleEarnLockedSetRedeemOption (object parameters = null)
    {
        return await this.callAsync ("sapiPostSimpleEarnLockedSetRedeemOption",parameters);
    }

    public async Task<object> sapiPostDciProductSubscribe (object parameters = null)
    {
        return await this.callAsync ("sapiPostDciProductSubscribe",parameters);
    }

    public async Task<object> sapiPostDciProductAutoCompoundEdit (object parameters = null)
    {
        return await this.callAsync ("sapiPostDciProductAutoCompoundEdit",parameters);
    }

    public async Task<object> sapiPutUserDataStream (object parameters = null)
    {
        return await this.callAsync ("sapiPutUserDataStream",parameters);
    }

    public async Task<object> sapiPutUserDataStreamIsolated (object parameters = null)
    {
        return await this.callAsync ("sapiPutUserDataStreamIsolated",parameters);
    }

    public async Task<object> sapiDeleteMarginOpenOrders (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteMarginOpenOrders",parameters);
    }

    public async Task<object> sapiDeleteMarginOrder (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteMarginOrder",parameters);
    }

    public async Task<object> sapiDeleteMarginOrderList (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteMarginOrderList",parameters);
    }

    public async Task<object> sapiDeleteMarginIsolatedAccount (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteMarginIsolatedAccount",parameters);
    }

    public async Task<object> sapiDeleteUserDataStream (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteUserDataStream",parameters);
    }

    public async Task<object> sapiDeleteUserDataStreamIsolated (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteUserDataStreamIsolated",parameters);
    }

    public async Task<object> sapiDeleteBrokerSubAccountApi (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteBrokerSubAccountApi",parameters);
    }

    public async Task<object> sapiDeleteBrokerSubAccountApiIpRestrictionIpList (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteBrokerSubAccountApiIpRestrictionIpList",parameters);
    }

    public async Task<object> sapiDeleteAlgoSpotOrder (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteAlgoSpotOrder",parameters);
    }

    public async Task<object> sapiDeleteAlgoFuturesOrder (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteAlgoFuturesOrder",parameters);
    }

    public async Task<object> sapiDeleteSubAccountSubAccountApiIpRestrictionIpList (object parameters = null)
    {
        return await this.callAsync ("sapiDeleteSubAccountSubAccountApiIpRestrictionIpList",parameters);
    }

    public async Task<object> sapiV2GetEthStakingAccount (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetEthStakingAccount",parameters);
    }

    public async Task<object> sapiV2GetSubAccountFuturesAccount (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetSubAccountFuturesAccount",parameters);
    }

    public async Task<object> sapiV2GetSubAccountFuturesAccountSummary (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetSubAccountFuturesAccountSummary",parameters);
    }

    public async Task<object> sapiV2GetSubAccountFuturesPositionRisk (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetSubAccountFuturesPositionRisk",parameters);
    }

    public async Task<object> sapiV2GetLoanFlexibleOngoingOrders (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetLoanFlexibleOngoingOrders",parameters);
    }

    public async Task<object> sapiV2GetLoanFlexibleBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetLoanFlexibleBorrowHistory",parameters);
    }

    public async Task<object> sapiV2GetLoanFlexibleRepayHistory (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetLoanFlexibleRepayHistory",parameters);
    }

    public async Task<object> sapiV2GetLoanFlexibleLtvAdjustmentHistory (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetLoanFlexibleLtvAdjustmentHistory",parameters);
    }

    public async Task<object> sapiV2GetLoanFlexibleLoanableData (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetLoanFlexibleLoanableData",parameters);
    }

    public async Task<object> sapiV2GetLoanFlexibleCollateralData (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetLoanFlexibleCollateralData",parameters);
    }

    public async Task<object> sapiV2GetPortfolioAccount (object parameters = null)
    {
        return await this.callAsync ("sapiV2GetPortfolioAccount",parameters);
    }

    public async Task<object> sapiV2PostEthStakingEthStake (object parameters = null)
    {
        return await this.callAsync ("sapiV2PostEthStakingEthStake",parameters);
    }

    public async Task<object> sapiV2PostSubAccountSubAccountApiIpRestriction (object parameters = null)
    {
        return await this.callAsync ("sapiV2PostSubAccountSubAccountApiIpRestriction",parameters);
    }

    public async Task<object> sapiV2PostLoanFlexibleBorrow (object parameters = null)
    {
        return await this.callAsync ("sapiV2PostLoanFlexibleBorrow",parameters);
    }

    public async Task<object> sapiV2PostLoanFlexibleRepay (object parameters = null)
    {
        return await this.callAsync ("sapiV2PostLoanFlexibleRepay",parameters);
    }

    public async Task<object> sapiV2PostLoanFlexibleAdjustLtv (object parameters = null)
    {
        return await this.callAsync ("sapiV2PostLoanFlexibleAdjustLtv",parameters);
    }

    public async Task<object> sapiV3GetSubAccountAssets (object parameters = null)
    {
        return await this.callAsync ("sapiV3GetSubAccountAssets",parameters);
    }

    public async Task<object> sapiV3PostAssetGetUserAsset (object parameters = null)
    {
        return await this.callAsync ("sapiV3PostAssetGetUserAsset",parameters);
    }

    public async Task<object> sapiV4GetSubAccountAssets (object parameters = null)
    {
        return await this.callAsync ("sapiV4GetSubAccountAssets",parameters);
    }

    public async Task<object> dapiPublicGetPing (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetPing",parameters);
    }

    public async Task<object> dapiPublicGetTime (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetTime",parameters);
    }

    public async Task<object> dapiPublicGetExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetExchangeInfo",parameters);
    }

    public async Task<object> dapiPublicGetDepth (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetDepth",parameters);
    }

    public async Task<object> dapiPublicGetTrades (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetTrades",parameters);
    }

    public async Task<object> dapiPublicGetHistoricalTrades (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetHistoricalTrades",parameters);
    }

    public async Task<object> dapiPublicGetAggTrades (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetAggTrades",parameters);
    }

    public async Task<object> dapiPublicGetPremiumIndex (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetPremiumIndex",parameters);
    }

    public async Task<object> dapiPublicGetFundingRate (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetFundingRate",parameters);
    }

    public async Task<object> dapiPublicGetKlines (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetKlines",parameters);
    }

    public async Task<object> dapiPublicGetContinuousKlines (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetContinuousKlines",parameters);
    }

    public async Task<object> dapiPublicGetIndexPriceKlines (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetIndexPriceKlines",parameters);
    }

    public async Task<object> dapiPublicGetMarkPriceKlines (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetMarkPriceKlines",parameters);
    }

    public async Task<object> dapiPublicGetPremiumIndexKlines (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetPremiumIndexKlines",parameters);
    }

    public async Task<object> dapiPublicGetTicker24hr (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetTicker24hr",parameters);
    }

    public async Task<object> dapiPublicGetTickerPrice (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetTickerPrice",parameters);
    }

    public async Task<object> dapiPublicGetTickerBookTicker (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetTickerBookTicker",parameters);
    }

    public async Task<object> dapiPublicGetConstituents (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetConstituents",parameters);
    }

    public async Task<object> dapiPublicGetOpenInterest (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetOpenInterest",parameters);
    }

    public async Task<object> dapiPublicGetFundingInfo (object parameters = null)
    {
        return await this.callAsync ("dapiPublicGetFundingInfo",parameters);
    }

    public async Task<object> dapiDataGetDeliveryPrice (object parameters = null)
    {
        return await this.callAsync ("dapiDataGetDeliveryPrice",parameters);
    }

    public async Task<object> dapiDataGetOpenInterestHist (object parameters = null)
    {
        return await this.callAsync ("dapiDataGetOpenInterestHist",parameters);
    }

    public async Task<object> dapiDataGetTopLongShortAccountRatio (object parameters = null)
    {
        return await this.callAsync ("dapiDataGetTopLongShortAccountRatio",parameters);
    }

    public async Task<object> dapiDataGetTopLongShortPositionRatio (object parameters = null)
    {
        return await this.callAsync ("dapiDataGetTopLongShortPositionRatio",parameters);
    }

    public async Task<object> dapiDataGetGlobalLongShortAccountRatio (object parameters = null)
    {
        return await this.callAsync ("dapiDataGetGlobalLongShortAccountRatio",parameters);
    }

    public async Task<object> dapiDataGetTakerBuySellVol (object parameters = null)
    {
        return await this.callAsync ("dapiDataGetTakerBuySellVol",parameters);
    }

    public async Task<object> dapiDataGetBasis (object parameters = null)
    {
        return await this.callAsync ("dapiDataGetBasis",parameters);
    }

    public async Task<object> dapiPrivateGetPositionSideDual (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetPositionSideDual",parameters);
    }

    public async Task<object> dapiPrivateGetOrderAmendment (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetOrderAmendment",parameters);
    }

    public async Task<object> dapiPrivateGetOrder (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetOrder",parameters);
    }

    public async Task<object> dapiPrivateGetOpenOrder (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetOpenOrder",parameters);
    }

    public async Task<object> dapiPrivateGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetOpenOrders",parameters);
    }

    public async Task<object> dapiPrivateGetAllOrders (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetAllOrders",parameters);
    }

    public async Task<object> dapiPrivateGetBalance (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetBalance",parameters);
    }

    public async Task<object> dapiPrivateGetAccount (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetAccount",parameters);
    }

    public async Task<object> dapiPrivateGetPositionMarginHistory (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetPositionMarginHistory",parameters);
    }

    public async Task<object> dapiPrivateGetPositionRisk (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetPositionRisk",parameters);
    }

    public async Task<object> dapiPrivateGetUserTrades (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetUserTrades",parameters);
    }

    public async Task<object> dapiPrivateGetIncome (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetIncome",parameters);
    }

    public async Task<object> dapiPrivateGetLeverageBracket (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetLeverageBracket",parameters);
    }

    public async Task<object> dapiPrivateGetForceOrders (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetForceOrders",parameters);
    }

    public async Task<object> dapiPrivateGetAdlQuantile (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetAdlQuantile",parameters);
    }

    public async Task<object> dapiPrivateGetCommissionRate (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetCommissionRate",parameters);
    }

    public async Task<object> dapiPrivateGetIncomeAsyn (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetIncomeAsyn",parameters);
    }

    public async Task<object> dapiPrivateGetIncomeAsynId (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetIncomeAsynId",parameters);
    }

    public async Task<object> dapiPrivateGetTradeAsyn (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetTradeAsyn",parameters);
    }

    public async Task<object> dapiPrivateGetTradeAsynId (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetTradeAsynId",parameters);
    }

    public async Task<object> dapiPrivateGetOrderAsyn (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetOrderAsyn",parameters);
    }

    public async Task<object> dapiPrivateGetOrderAsynId (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetOrderAsynId",parameters);
    }

    public async Task<object> dapiPrivateGetPmExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetPmExchangeInfo",parameters);
    }

    public async Task<object> dapiPrivateGetPmAccountInfo (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateGetPmAccountInfo",parameters);
    }

    public async Task<object> dapiPrivatePostPositionSideDual (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePostPositionSideDual",parameters);
    }

    public async Task<object> dapiPrivatePostOrder (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePostOrder",parameters);
    }

    public async Task<object> dapiPrivatePostBatchOrders (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePostBatchOrders",parameters);
    }

    public async Task<object> dapiPrivatePostCountdownCancelAll (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePostCountdownCancelAll",parameters);
    }

    public async Task<object> dapiPrivatePostLeverage (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePostLeverage",parameters);
    }

    public async Task<object> dapiPrivatePostMarginType (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePostMarginType",parameters);
    }

    public async Task<object> dapiPrivatePostPositionMargin (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePostPositionMargin",parameters);
    }

    public async Task<object> dapiPrivatePostListenKey (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePostListenKey",parameters);
    }

    public async Task<object> dapiPrivatePutListenKey (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePutListenKey",parameters);
    }

    public async Task<object> dapiPrivatePutOrder (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePutOrder",parameters);
    }

    public async Task<object> dapiPrivatePutBatchOrders (object parameters = null)
    {
        return await this.callAsync ("dapiPrivatePutBatchOrders",parameters);
    }

    public async Task<object> dapiPrivateDeleteOrder (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateDeleteOrder",parameters);
    }

    public async Task<object> dapiPrivateDeleteAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateDeleteAllOpenOrders",parameters);
    }

    public async Task<object> dapiPrivateDeleteBatchOrders (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateDeleteBatchOrders",parameters);
    }

    public async Task<object> dapiPrivateDeleteListenKey (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateDeleteListenKey",parameters);
    }

    public async Task<object> dapiPrivateV2GetLeverageBracket (object parameters = null)
    {
        return await this.callAsync ("dapiPrivateV2GetLeverageBracket",parameters);
    }

    public async Task<object> fapiPublicGetPing (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetPing",parameters);
    }

    public async Task<object> fapiPublicGetTime (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetTime",parameters);
    }

    public async Task<object> fapiPublicGetExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetExchangeInfo",parameters);
    }

    public async Task<object> fapiPublicGetDepth (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetDepth",parameters);
    }

    public async Task<object> fapiPublicGetTrades (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetTrades",parameters);
    }

    public async Task<object> fapiPublicGetHistoricalTrades (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetHistoricalTrades",parameters);
    }

    public async Task<object> fapiPublicGetAggTrades (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetAggTrades",parameters);
    }

    public async Task<object> fapiPublicGetKlines (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetKlines",parameters);
    }

    public async Task<object> fapiPublicGetContinuousKlines (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetContinuousKlines",parameters);
    }

    public async Task<object> fapiPublicGetMarkPriceKlines (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetMarkPriceKlines",parameters);
    }

    public async Task<object> fapiPublicGetIndexPriceKlines (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetIndexPriceKlines",parameters);
    }

    public async Task<object> fapiPublicGetPremiumIndexKlines (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetPremiumIndexKlines",parameters);
    }

    public async Task<object> fapiPublicGetFundingRate (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetFundingRate",parameters);
    }

    public async Task<object> fapiPublicGetFundingInfo (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetFundingInfo",parameters);
    }

    public async Task<object> fapiPublicGetPremiumIndex (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetPremiumIndex",parameters);
    }

    public async Task<object> fapiPublicGetTicker24hr (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetTicker24hr",parameters);
    }

    public async Task<object> fapiPublicGetTickerPrice (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetTickerPrice",parameters);
    }

    public async Task<object> fapiPublicGetTickerBookTicker (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetTickerBookTicker",parameters);
    }

    public async Task<object> fapiPublicGetOpenInterest (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetOpenInterest",parameters);
    }

    public async Task<object> fapiPublicGetIndexInfo (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetIndexInfo",parameters);
    }

    public async Task<object> fapiPublicGetAssetIndex (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetAssetIndex",parameters);
    }

    public async Task<object> fapiPublicGetConstituents (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetConstituents",parameters);
    }

    public async Task<object> fapiPublicGetApiTradingStatus (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetApiTradingStatus",parameters);
    }

    public async Task<object> fapiPublicGetLvtKlines (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetLvtKlines",parameters);
    }

    public async Task<object> fapiPublicGetConvertExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("fapiPublicGetConvertExchangeInfo",parameters);
    }

    public async Task<object> fapiDataGetDeliveryPrice (object parameters = null)
    {
        return await this.callAsync ("fapiDataGetDeliveryPrice",parameters);
    }

    public async Task<object> fapiDataGetOpenInterestHist (object parameters = null)
    {
        return await this.callAsync ("fapiDataGetOpenInterestHist",parameters);
    }

    public async Task<object> fapiDataGetTopLongShortAccountRatio (object parameters = null)
    {
        return await this.callAsync ("fapiDataGetTopLongShortAccountRatio",parameters);
    }

    public async Task<object> fapiDataGetTopLongShortPositionRatio (object parameters = null)
    {
        return await this.callAsync ("fapiDataGetTopLongShortPositionRatio",parameters);
    }

    public async Task<object> fapiDataGetGlobalLongShortAccountRatio (object parameters = null)
    {
        return await this.callAsync ("fapiDataGetGlobalLongShortAccountRatio",parameters);
    }

    public async Task<object> fapiDataGetTakerlongshortRatio (object parameters = null)
    {
        return await this.callAsync ("fapiDataGetTakerlongshortRatio",parameters);
    }

    public async Task<object> fapiDataGetBasis (object parameters = null)
    {
        return await this.callAsync ("fapiDataGetBasis",parameters);
    }

    public async Task<object> fapiPrivateGetForceOrders (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetForceOrders",parameters);
    }

    public async Task<object> fapiPrivateGetAllOrders (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetAllOrders",parameters);
    }

    public async Task<object> fapiPrivateGetOpenOrder (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetOpenOrder",parameters);
    }

    public async Task<object> fapiPrivateGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetOpenOrders",parameters);
    }

    public async Task<object> fapiPrivateGetOrder (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetOrder",parameters);
    }

    public async Task<object> fapiPrivateGetAccount (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetAccount",parameters);
    }

    public async Task<object> fapiPrivateGetBalance (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetBalance",parameters);
    }

    public async Task<object> fapiPrivateGetLeverageBracket (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetLeverageBracket",parameters);
    }

    public async Task<object> fapiPrivateGetPositionMarginHistory (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetPositionMarginHistory",parameters);
    }

    public async Task<object> fapiPrivateGetPositionRisk (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetPositionRisk",parameters);
    }

    public async Task<object> fapiPrivateGetPositionSideDual (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetPositionSideDual",parameters);
    }

    public async Task<object> fapiPrivateGetUserTrades (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetUserTrades",parameters);
    }

    public async Task<object> fapiPrivateGetIncome (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetIncome",parameters);
    }

    public async Task<object> fapiPrivateGetCommissionRate (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetCommissionRate",parameters);
    }

    public async Task<object> fapiPrivateGetRateLimitOrder (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetRateLimitOrder",parameters);
    }

    public async Task<object> fapiPrivateGetApiTradingStatus (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetApiTradingStatus",parameters);
    }

    public async Task<object> fapiPrivateGetMultiAssetsMargin (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetMultiAssetsMargin",parameters);
    }

    public async Task<object> fapiPrivateGetApiReferralIfNewUser (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetApiReferralIfNewUser",parameters);
    }

    public async Task<object> fapiPrivateGetApiReferralCustomization (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetApiReferralCustomization",parameters);
    }

    public async Task<object> fapiPrivateGetApiReferralUserCustomization (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetApiReferralUserCustomization",parameters);
    }

    public async Task<object> fapiPrivateGetApiReferralTraderNum (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetApiReferralTraderNum",parameters);
    }

    public async Task<object> fapiPrivateGetApiReferralOverview (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetApiReferralOverview",parameters);
    }

    public async Task<object> fapiPrivateGetApiReferralTradeVol (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetApiReferralTradeVol",parameters);
    }

    public async Task<object> fapiPrivateGetApiReferralRebateVol (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetApiReferralRebateVol",parameters);
    }

    public async Task<object> fapiPrivateGetApiReferralTraderSummary (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetApiReferralTraderSummary",parameters);
    }

    public async Task<object> fapiPrivateGetAdlQuantile (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetAdlQuantile",parameters);
    }

    public async Task<object> fapiPrivateGetPmAccountInfo (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetPmAccountInfo",parameters);
    }

    public async Task<object> fapiPrivateGetOrderAmendment (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetOrderAmendment",parameters);
    }

    public async Task<object> fapiPrivateGetIncomeAsyn (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetIncomeAsyn",parameters);
    }

    public async Task<object> fapiPrivateGetIncomeAsynId (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetIncomeAsynId",parameters);
    }

    public async Task<object> fapiPrivateGetOrderAsyn (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetOrderAsyn",parameters);
    }

    public async Task<object> fapiPrivateGetOrderAsynId (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetOrderAsynId",parameters);
    }

    public async Task<object> fapiPrivateGetTradeAsyn (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetTradeAsyn",parameters);
    }

    public async Task<object> fapiPrivateGetTradeAsynId (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetTradeAsynId",parameters);
    }

    public async Task<object> fapiPrivateGetFeeBurn (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetFeeBurn",parameters);
    }

    public async Task<object> fapiPrivateGetSymbolConfig (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetSymbolConfig",parameters);
    }

    public async Task<object> fapiPrivateGetAccountConfig (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetAccountConfig",parameters);
    }

    public async Task<object> fapiPrivateGetConvertOrderStatus (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateGetConvertOrderStatus",parameters);
    }

    public async Task<object> fapiPrivatePostBatchOrders (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostBatchOrders",parameters);
    }

    public async Task<object> fapiPrivatePostPositionSideDual (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostPositionSideDual",parameters);
    }

    public async Task<object> fapiPrivatePostPositionMargin (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostPositionMargin",parameters);
    }

    public async Task<object> fapiPrivatePostMarginType (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostMarginType",parameters);
    }

    public async Task<object> fapiPrivatePostOrder (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostOrder",parameters);
    }

    public async Task<object> fapiPrivatePostLeverage (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostLeverage",parameters);
    }

    public async Task<object> fapiPrivatePostListenKey (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostListenKey",parameters);
    }

    public async Task<object> fapiPrivatePostCountdownCancelAll (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostCountdownCancelAll",parameters);
    }

    public async Task<object> fapiPrivatePostMultiAssetsMargin (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostMultiAssetsMargin",parameters);
    }

    public async Task<object> fapiPrivatePostApiReferralCustomization (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostApiReferralCustomization",parameters);
    }

    public async Task<object> fapiPrivatePostApiReferralUserCustomization (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostApiReferralUserCustomization",parameters);
    }

    public async Task<object> fapiPrivatePostFeeBurn (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostFeeBurn",parameters);
    }

    public async Task<object> fapiPrivatePostConvertGetQuote (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostConvertGetQuote",parameters);
    }

    public async Task<object> fapiPrivatePostConvertAcceptQuote (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePostConvertAcceptQuote",parameters);
    }

    public async Task<object> fapiPrivatePutListenKey (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePutListenKey",parameters);
    }

    public async Task<object> fapiPrivatePutOrder (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePutOrder",parameters);
    }

    public async Task<object> fapiPrivatePutBatchOrders (object parameters = null)
    {
        return await this.callAsync ("fapiPrivatePutBatchOrders",parameters);
    }

    public async Task<object> fapiPrivateDeleteBatchOrders (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateDeleteBatchOrders",parameters);
    }

    public async Task<object> fapiPrivateDeleteOrder (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateDeleteOrder",parameters);
    }

    public async Task<object> fapiPrivateDeleteAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateDeleteAllOpenOrders",parameters);
    }

    public async Task<object> fapiPrivateDeleteListenKey (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateDeleteListenKey",parameters);
    }

    public async Task<object> fapiPublicV2GetTickerPrice (object parameters = null)
    {
        return await this.callAsync ("fapiPublicV2GetTickerPrice",parameters);
    }

    public async Task<object> fapiPrivateV2GetAccount (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateV2GetAccount",parameters);
    }

    public async Task<object> fapiPrivateV2GetBalance (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateV2GetBalance",parameters);
    }

    public async Task<object> fapiPrivateV2GetPositionRisk (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateV2GetPositionRisk",parameters);
    }

    public async Task<object> fapiPrivateV3GetAccount (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateV3GetAccount",parameters);
    }

    public async Task<object> fapiPrivateV3GetBalance (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateV3GetBalance",parameters);
    }

    public async Task<object> fapiPrivateV3GetPositionRisk (object parameters = null)
    {
        return await this.callAsync ("fapiPrivateV3GetPositionRisk",parameters);
    }

    public async Task<object> eapiPublicGetPing (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetPing",parameters);
    }

    public async Task<object> eapiPublicGetTime (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetTime",parameters);
    }

    public async Task<object> eapiPublicGetExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetExchangeInfo",parameters);
    }

    public async Task<object> eapiPublicGetIndex (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetIndex",parameters);
    }

    public async Task<object> eapiPublicGetTicker (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetTicker",parameters);
    }

    public async Task<object> eapiPublicGetMark (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetMark",parameters);
    }

    public async Task<object> eapiPublicGetDepth (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetDepth",parameters);
    }

    public async Task<object> eapiPublicGetKlines (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetKlines",parameters);
    }

    public async Task<object> eapiPublicGetTrades (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetTrades",parameters);
    }

    public async Task<object> eapiPublicGetHistoricalTrades (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetHistoricalTrades",parameters);
    }

    public async Task<object> eapiPublicGetExerciseHistory (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetExerciseHistory",parameters);
    }

    public async Task<object> eapiPublicGetOpenInterest (object parameters = null)
    {
        return await this.callAsync ("eapiPublicGetOpenInterest",parameters);
    }

    public async Task<object> eapiPrivateGetAccount (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetAccount",parameters);
    }

    public async Task<object> eapiPrivateGetPosition (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetPosition",parameters);
    }

    public async Task<object> eapiPrivateGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetOpenOrders",parameters);
    }

    public async Task<object> eapiPrivateGetHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetHistoryOrders",parameters);
    }

    public async Task<object> eapiPrivateGetUserTrades (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetUserTrades",parameters);
    }

    public async Task<object> eapiPrivateGetExerciseRecord (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetExerciseRecord",parameters);
    }

    public async Task<object> eapiPrivateGetBill (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetBill",parameters);
    }

    public async Task<object> eapiPrivateGetIncomeAsyn (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetIncomeAsyn",parameters);
    }

    public async Task<object> eapiPrivateGetIncomeAsynId (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetIncomeAsynId",parameters);
    }

    public async Task<object> eapiPrivateGetMarginAccount (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetMarginAccount",parameters);
    }

    public async Task<object> eapiPrivateGetMmp (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetMmp",parameters);
    }

    public async Task<object> eapiPrivateGetCountdownCancelAll (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetCountdownCancelAll",parameters);
    }

    public async Task<object> eapiPrivateGetOrder (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetOrder",parameters);
    }

    public async Task<object> eapiPrivateGetBlockOrderOrders (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetBlockOrderOrders",parameters);
    }

    public async Task<object> eapiPrivateGetBlockOrderExecute (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetBlockOrderExecute",parameters);
    }

    public async Task<object> eapiPrivateGetBlockUserTrades (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetBlockUserTrades",parameters);
    }

    public async Task<object> eapiPrivateGetBlockTrades (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateGetBlockTrades",parameters);
    }

    public async Task<object> eapiPrivatePostOrder (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePostOrder",parameters);
    }

    public async Task<object> eapiPrivatePostBatchOrders (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePostBatchOrders",parameters);
    }

    public async Task<object> eapiPrivatePostListenKey (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePostListenKey",parameters);
    }

    public async Task<object> eapiPrivatePostMmpSet (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePostMmpSet",parameters);
    }

    public async Task<object> eapiPrivatePostMmpReset (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePostMmpReset",parameters);
    }

    public async Task<object> eapiPrivatePostCountdownCancelAll (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePostCountdownCancelAll",parameters);
    }

    public async Task<object> eapiPrivatePostCountdownCancelAllHeartBeat (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePostCountdownCancelAllHeartBeat",parameters);
    }

    public async Task<object> eapiPrivatePostBlockOrderCreate (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePostBlockOrderCreate",parameters);
    }

    public async Task<object> eapiPrivatePostBlockOrderExecute (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePostBlockOrderExecute",parameters);
    }

    public async Task<object> eapiPrivatePutListenKey (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePutListenKey",parameters);
    }

    public async Task<object> eapiPrivatePutBlockOrderCreate (object parameters = null)
    {
        return await this.callAsync ("eapiPrivatePutBlockOrderCreate",parameters);
    }

    public async Task<object> eapiPrivateDeleteOrder (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateDeleteOrder",parameters);
    }

    public async Task<object> eapiPrivateDeleteBatchOrders (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateDeleteBatchOrders",parameters);
    }

    public async Task<object> eapiPrivateDeleteAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateDeleteAllOpenOrders",parameters);
    }

    public async Task<object> eapiPrivateDeleteAllOpenOrdersByUnderlying (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateDeleteAllOpenOrdersByUnderlying",parameters);
    }

    public async Task<object> eapiPrivateDeleteListenKey (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateDeleteListenKey",parameters);
    }

    public async Task<object> eapiPrivateDeleteBlockOrderCreate (object parameters = null)
    {
        return await this.callAsync ("eapiPrivateDeleteBlockOrderCreate",parameters);
    }

    public async Task<object> publicGetPing (object parameters = null)
    {
        return await this.callAsync ("publicGetPing",parameters);
    }

    public async Task<object> publicGetTime (object parameters = null)
    {
        return await this.callAsync ("publicGetTime",parameters);
    }

    public async Task<object> publicGetDepth (object parameters = null)
    {
        return await this.callAsync ("publicGetDepth",parameters);
    }

    public async Task<object> publicGetTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetTrades",parameters);
    }

    public async Task<object> publicGetAggTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetAggTrades",parameters);
    }

    public async Task<object> publicGetHistoricalTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetHistoricalTrades",parameters);
    }

    public async Task<object> publicGetKlines (object parameters = null)
    {
        return await this.callAsync ("publicGetKlines",parameters);
    }

    public async Task<object> publicGetUiKlines (object parameters = null)
    {
        return await this.callAsync ("publicGetUiKlines",parameters);
    }

    public async Task<object> publicGetTicker24hr (object parameters = null)
    {
        return await this.callAsync ("publicGetTicker24hr",parameters);
    }

    public async Task<object> publicGetTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetTicker",parameters);
    }

    public async Task<object> publicGetTickerTradingDay (object parameters = null)
    {
        return await this.callAsync ("publicGetTickerTradingDay",parameters);
    }

    public async Task<object> publicGetTickerPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetTickerPrice",parameters);
    }

    public async Task<object> publicGetTickerBookTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetTickerBookTicker",parameters);
    }

    public async Task<object> publicGetExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetExchangeInfo",parameters);
    }

    public async Task<object> publicGetAvgPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetAvgPrice",parameters);
    }

    public async Task<object> publicPutUserDataStream (object parameters = null)
    {
        return await this.callAsync ("publicPutUserDataStream",parameters);
    }

    public async Task<object> publicPostUserDataStream (object parameters = null)
    {
        return await this.callAsync ("publicPostUserDataStream",parameters);
    }

    public async Task<object> publicDeleteUserDataStream (object parameters = null)
    {
        return await this.callAsync ("publicDeleteUserDataStream",parameters);
    }

    public async Task<object> privateGetAllOrderList (object parameters = null)
    {
        return await this.callAsync ("privateGetAllOrderList",parameters);
    }

    public async Task<object> privateGetOpenOrderList (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenOrderList",parameters);
    }

    public async Task<object> privateGetOrderList (object parameters = null)
    {
        return await this.callAsync ("privateGetOrderList",parameters);
    }

    public async Task<object> privateGetOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetOrder",parameters);
    }

    public async Task<object> privateGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenOrders",parameters);
    }

    public async Task<object> privateGetAllOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetAllOrders",parameters);
    }

    public async Task<object> privateGetAccount (object parameters = null)
    {
        return await this.callAsync ("privateGetAccount",parameters);
    }

    public async Task<object> privateGetMyTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetMyTrades",parameters);
    }

    public async Task<object> privateGetRateLimitOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetRateLimitOrder",parameters);
    }

    public async Task<object> privateGetMyPreventedMatches (object parameters = null)
    {
        return await this.callAsync ("privateGetMyPreventedMatches",parameters);
    }

    public async Task<object> privateGetMyAllocations (object parameters = null)
    {
        return await this.callAsync ("privateGetMyAllocations",parameters);
    }

    public async Task<object> privateGetAccountCommission (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountCommission",parameters);
    }

    public async Task<object> privatePostOrderOco (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderOco",parameters);
    }

    public async Task<object> privatePostOrderListOco (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderListOco",parameters);
    }

    public async Task<object> privatePostOrderListOto (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderListOto",parameters);
    }

    public async Task<object> privatePostOrderListOtoco (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderListOtoco",parameters);
    }

    public async Task<object> privatePostSorOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSorOrder",parameters);
    }

    public async Task<object> privatePostSorOrderTest (object parameters = null)
    {
        return await this.callAsync ("privatePostSorOrderTest",parameters);
    }

    public async Task<object> privatePostOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostOrder",parameters);
    }

    public async Task<object> privatePostOrderCancelReplace (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderCancelReplace",parameters);
    }

    public async Task<object> privatePostOrderTest (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderTest",parameters);
    }

    public async Task<object> privateDeleteOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOpenOrders",parameters);
    }

    public async Task<object> privateDeleteOrderList (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrderList",parameters);
    }

    public async Task<object> privateDeleteOrder (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrder",parameters);
    }

    public async Task<object> papiGetPing (object parameters = null)
    {
        return await this.callAsync ("papiGetPing",parameters);
    }

    public async Task<object> papiGetUmOrder (object parameters = null)
    {
        return await this.callAsync ("papiGetUmOrder",parameters);
    }

    public async Task<object> papiGetUmOpenOrder (object parameters = null)
    {
        return await this.callAsync ("papiGetUmOpenOrder",parameters);
    }

    public async Task<object> papiGetUmOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetUmOpenOrders",parameters);
    }

    public async Task<object> papiGetUmAllOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetUmAllOrders",parameters);
    }

    public async Task<object> papiGetCmOrder (object parameters = null)
    {
        return await this.callAsync ("papiGetCmOrder",parameters);
    }

    public async Task<object> papiGetCmOpenOrder (object parameters = null)
    {
        return await this.callAsync ("papiGetCmOpenOrder",parameters);
    }

    public async Task<object> papiGetCmOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetCmOpenOrders",parameters);
    }

    public async Task<object> papiGetCmAllOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetCmAllOrders",parameters);
    }

    public async Task<object> papiGetUmConditionalOpenOrder (object parameters = null)
    {
        return await this.callAsync ("papiGetUmConditionalOpenOrder",parameters);
    }

    public async Task<object> papiGetUmConditionalOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetUmConditionalOpenOrders",parameters);
    }

    public async Task<object> papiGetUmConditionalOrderHistory (object parameters = null)
    {
        return await this.callAsync ("papiGetUmConditionalOrderHistory",parameters);
    }

    public async Task<object> papiGetUmConditionalAllOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetUmConditionalAllOrders",parameters);
    }

    public async Task<object> papiGetCmConditionalOpenOrder (object parameters = null)
    {
        return await this.callAsync ("papiGetCmConditionalOpenOrder",parameters);
    }

    public async Task<object> papiGetCmConditionalOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetCmConditionalOpenOrders",parameters);
    }

    public async Task<object> papiGetCmConditionalOrderHistory (object parameters = null)
    {
        return await this.callAsync ("papiGetCmConditionalOrderHistory",parameters);
    }

    public async Task<object> papiGetCmConditionalAllOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetCmConditionalAllOrders",parameters);
    }

    public async Task<object> papiGetMarginOrder (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginOrder",parameters);
    }

    public async Task<object> papiGetMarginOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginOpenOrders",parameters);
    }

    public async Task<object> papiGetMarginAllOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginAllOrders",parameters);
    }

    public async Task<object> papiGetMarginOrderList (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginOrderList",parameters);
    }

    public async Task<object> papiGetMarginAllOrderList (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginAllOrderList",parameters);
    }

    public async Task<object> papiGetMarginOpenOrderList (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginOpenOrderList",parameters);
    }

    public async Task<object> papiGetMarginMyTrades (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginMyTrades",parameters);
    }

    public async Task<object> papiGetBalance (object parameters = null)
    {
        return await this.callAsync ("papiGetBalance",parameters);
    }

    public async Task<object> papiGetAccount (object parameters = null)
    {
        return await this.callAsync ("papiGetAccount",parameters);
    }

    public async Task<object> papiGetMarginMaxBorrowable (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginMaxBorrowable",parameters);
    }

    public async Task<object> papiGetMarginMaxWithdraw (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginMaxWithdraw",parameters);
    }

    public async Task<object> papiGetUmPositionRisk (object parameters = null)
    {
        return await this.callAsync ("papiGetUmPositionRisk",parameters);
    }

    public async Task<object> papiGetCmPositionRisk (object parameters = null)
    {
        return await this.callAsync ("papiGetCmPositionRisk",parameters);
    }

    public async Task<object> papiGetUmPositionSideDual (object parameters = null)
    {
        return await this.callAsync ("papiGetUmPositionSideDual",parameters);
    }

    public async Task<object> papiGetCmPositionSideDual (object parameters = null)
    {
        return await this.callAsync ("papiGetCmPositionSideDual",parameters);
    }

    public async Task<object> papiGetUmUserTrades (object parameters = null)
    {
        return await this.callAsync ("papiGetUmUserTrades",parameters);
    }

    public async Task<object> papiGetCmUserTrades (object parameters = null)
    {
        return await this.callAsync ("papiGetCmUserTrades",parameters);
    }

    public async Task<object> papiGetUmLeverageBracket (object parameters = null)
    {
        return await this.callAsync ("papiGetUmLeverageBracket",parameters);
    }

    public async Task<object> papiGetCmLeverageBracket (object parameters = null)
    {
        return await this.callAsync ("papiGetCmLeverageBracket",parameters);
    }

    public async Task<object> papiGetMarginForceOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginForceOrders",parameters);
    }

    public async Task<object> papiGetUmForceOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetUmForceOrders",parameters);
    }

    public async Task<object> papiGetCmForceOrders (object parameters = null)
    {
        return await this.callAsync ("papiGetCmForceOrders",parameters);
    }

    public async Task<object> papiGetUmApiTradingStatus (object parameters = null)
    {
        return await this.callAsync ("papiGetUmApiTradingStatus",parameters);
    }

    public async Task<object> papiGetUmCommissionRate (object parameters = null)
    {
        return await this.callAsync ("papiGetUmCommissionRate",parameters);
    }

    public async Task<object> papiGetCmCommissionRate (object parameters = null)
    {
        return await this.callAsync ("papiGetCmCommissionRate",parameters);
    }

    public async Task<object> papiGetMarginMarginLoan (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginMarginLoan",parameters);
    }

    public async Task<object> papiGetMarginRepayLoan (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginRepayLoan",parameters);
    }

    public async Task<object> papiGetMarginMarginInterestHistory (object parameters = null)
    {
        return await this.callAsync ("papiGetMarginMarginInterestHistory",parameters);
    }

    public async Task<object> papiGetPortfolioInterestHistory (object parameters = null)
    {
        return await this.callAsync ("papiGetPortfolioInterestHistory",parameters);
    }

    public async Task<object> papiGetUmIncome (object parameters = null)
    {
        return await this.callAsync ("papiGetUmIncome",parameters);
    }

    public async Task<object> papiGetCmIncome (object parameters = null)
    {
        return await this.callAsync ("papiGetCmIncome",parameters);
    }

    public async Task<object> papiGetUmAccount (object parameters = null)
    {
        return await this.callAsync ("papiGetUmAccount",parameters);
    }

    public async Task<object> papiGetCmAccount (object parameters = null)
    {
        return await this.callAsync ("papiGetCmAccount",parameters);
    }

    public async Task<object> papiGetRepayFuturesSwitch (object parameters = null)
    {
        return await this.callAsync ("papiGetRepayFuturesSwitch",parameters);
    }

    public async Task<object> papiGetUmAdlQuantile (object parameters = null)
    {
        return await this.callAsync ("papiGetUmAdlQuantile",parameters);
    }

    public async Task<object> papiGetCmAdlQuantile (object parameters = null)
    {
        return await this.callAsync ("papiGetCmAdlQuantile",parameters);
    }

    public async Task<object> papiGetUmTradeAsyn (object parameters = null)
    {
        return await this.callAsync ("papiGetUmTradeAsyn",parameters);
    }

    public async Task<object> papiGetUmTradeAsynId (object parameters = null)
    {
        return await this.callAsync ("papiGetUmTradeAsynId",parameters);
    }

    public async Task<object> papiGetUmOrderAsyn (object parameters = null)
    {
        return await this.callAsync ("papiGetUmOrderAsyn",parameters);
    }

    public async Task<object> papiGetUmOrderAsynId (object parameters = null)
    {
        return await this.callAsync ("papiGetUmOrderAsynId",parameters);
    }

    public async Task<object> papiGetUmIncomeAsyn (object parameters = null)
    {
        return await this.callAsync ("papiGetUmIncomeAsyn",parameters);
    }

    public async Task<object> papiGetUmIncomeAsynId (object parameters = null)
    {
        return await this.callAsync ("papiGetUmIncomeAsynId",parameters);
    }

    public async Task<object> papiGetUmOrderAmendment (object parameters = null)
    {
        return await this.callAsync ("papiGetUmOrderAmendment",parameters);
    }

    public async Task<object> papiGetCmOrderAmendment (object parameters = null)
    {
        return await this.callAsync ("papiGetCmOrderAmendment",parameters);
    }

    public async Task<object> papiGetUmFeeBurn (object parameters = null)
    {
        return await this.callAsync ("papiGetUmFeeBurn",parameters);
    }

    public async Task<object> papiGetUmAccountConfig (object parameters = null)
    {
        return await this.callAsync ("papiGetUmAccountConfig",parameters);
    }

    public async Task<object> papiGetUmSymbolConfig (object parameters = null)
    {
        return await this.callAsync ("papiGetUmSymbolConfig",parameters);
    }

    public async Task<object> papiGetCmAccountConfig (object parameters = null)
    {
        return await this.callAsync ("papiGetCmAccountConfig",parameters);
    }

    public async Task<object> papiGetCmSymbolConfig (object parameters = null)
    {
        return await this.callAsync ("papiGetCmSymbolConfig",parameters);
    }

    public async Task<object> papiGetRateLimitOrder (object parameters = null)
    {
        return await this.callAsync ("papiGetRateLimitOrder",parameters);
    }

    public async Task<object> papiPostUmOrder (object parameters = null)
    {
        return await this.callAsync ("papiPostUmOrder",parameters);
    }

    public async Task<object> papiPostUmConditionalOrder (object parameters = null)
    {
        return await this.callAsync ("papiPostUmConditionalOrder",parameters);
    }

    public async Task<object> papiPostCmOrder (object parameters = null)
    {
        return await this.callAsync ("papiPostCmOrder",parameters);
    }

    public async Task<object> papiPostCmConditionalOrder (object parameters = null)
    {
        return await this.callAsync ("papiPostCmConditionalOrder",parameters);
    }

    public async Task<object> papiPostMarginOrder (object parameters = null)
    {
        return await this.callAsync ("papiPostMarginOrder",parameters);
    }

    public async Task<object> papiPostMarginLoan (object parameters = null)
    {
        return await this.callAsync ("papiPostMarginLoan",parameters);
    }

    public async Task<object> papiPostRepayLoan (object parameters = null)
    {
        return await this.callAsync ("papiPostRepayLoan",parameters);
    }

    public async Task<object> papiPostMarginOrderOco (object parameters = null)
    {
        return await this.callAsync ("papiPostMarginOrderOco",parameters);
    }

    public async Task<object> papiPostUmLeverage (object parameters = null)
    {
        return await this.callAsync ("papiPostUmLeverage",parameters);
    }

    public async Task<object> papiPostCmLeverage (object parameters = null)
    {
        return await this.callAsync ("papiPostCmLeverage",parameters);
    }

    public async Task<object> papiPostUmPositionSideDual (object parameters = null)
    {
        return await this.callAsync ("papiPostUmPositionSideDual",parameters);
    }

    public async Task<object> papiPostCmPositionSideDual (object parameters = null)
    {
        return await this.callAsync ("papiPostCmPositionSideDual",parameters);
    }

    public async Task<object> papiPostAutoCollection (object parameters = null)
    {
        return await this.callAsync ("papiPostAutoCollection",parameters);
    }

    public async Task<object> papiPostBnbTransfer (object parameters = null)
    {
        return await this.callAsync ("papiPostBnbTransfer",parameters);
    }

    public async Task<object> papiPostRepayFuturesSwitch (object parameters = null)
    {
        return await this.callAsync ("papiPostRepayFuturesSwitch",parameters);
    }

    public async Task<object> papiPostRepayFuturesNegativeBalance (object parameters = null)
    {
        return await this.callAsync ("papiPostRepayFuturesNegativeBalance",parameters);
    }

    public async Task<object> papiPostListenKey (object parameters = null)
    {
        return await this.callAsync ("papiPostListenKey",parameters);
    }

    public async Task<object> papiPostAssetCollection (object parameters = null)
    {
        return await this.callAsync ("papiPostAssetCollection",parameters);
    }

    public async Task<object> papiPostMarginRepayDebt (object parameters = null)
    {
        return await this.callAsync ("papiPostMarginRepayDebt",parameters);
    }

    public async Task<object> papiPostUmFeeBurn (object parameters = null)
    {
        return await this.callAsync ("papiPostUmFeeBurn",parameters);
    }

    public async Task<object> papiPutListenKey (object parameters = null)
    {
        return await this.callAsync ("papiPutListenKey",parameters);
    }

    public async Task<object> papiPutUmOrder (object parameters = null)
    {
        return await this.callAsync ("papiPutUmOrder",parameters);
    }

    public async Task<object> papiPutCmOrder (object parameters = null)
    {
        return await this.callAsync ("papiPutCmOrder",parameters);
    }

    public async Task<object> papiDeleteUmOrder (object parameters = null)
    {
        return await this.callAsync ("papiDeleteUmOrder",parameters);
    }

    public async Task<object> papiDeleteUmConditionalOrder (object parameters = null)
    {
        return await this.callAsync ("papiDeleteUmConditionalOrder",parameters);
    }

    public async Task<object> papiDeleteUmAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiDeleteUmAllOpenOrders",parameters);
    }

    public async Task<object> papiDeleteUmConditionalAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiDeleteUmConditionalAllOpenOrders",parameters);
    }

    public async Task<object> papiDeleteCmOrder (object parameters = null)
    {
        return await this.callAsync ("papiDeleteCmOrder",parameters);
    }

    public async Task<object> papiDeleteCmConditionalOrder (object parameters = null)
    {
        return await this.callAsync ("papiDeleteCmConditionalOrder",parameters);
    }

    public async Task<object> papiDeleteCmAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiDeleteCmAllOpenOrders",parameters);
    }

    public async Task<object> papiDeleteCmConditionalAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiDeleteCmConditionalAllOpenOrders",parameters);
    }

    public async Task<object> papiDeleteMarginOrder (object parameters = null)
    {
        return await this.callAsync ("papiDeleteMarginOrder",parameters);
    }

    public async Task<object> papiDeleteMarginAllOpenOrders (object parameters = null)
    {
        return await this.callAsync ("papiDeleteMarginAllOpenOrders",parameters);
    }

    public async Task<object> papiDeleteMarginOrderList (object parameters = null)
    {
        return await this.callAsync ("papiDeleteMarginOrderList",parameters);
    }

    public async Task<object> papiDeleteListenKey (object parameters = null)
    {
        return await this.callAsync ("papiDeleteListenKey",parameters);
    }

}