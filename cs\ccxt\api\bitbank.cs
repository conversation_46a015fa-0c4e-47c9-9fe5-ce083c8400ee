// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bitbank : Exchange
{
    public bitbank (object args = null): base(args) {}

    public async Task<object> publicGetPairTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetPairTicker",parameters);
    }

    public async Task<object> publicGetTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetTickers",parameters);
    }

    public async Task<object> publicGetTickersJpy (object parameters = null)
    {
        return await this.callAsync ("publicGetTickersJpy",parameters);
    }

    public async Task<object> publicGetPairDepth (object parameters = null)
    {
        return await this.callAsync ("publicGetPairDepth",parameters);
    }

    public async Task<object> publicGetPairTransactions (object parameters = null)
    {
        return await this.callAsync ("publicGetPairTransactions",parameters);
    }

    public async Task<object> publicGetPairTransactionsYyyymmdd (object parameters = null)
    {
        return await this.callAsync ("publicGetPairTransactionsYyyymmdd",parameters);
    }

    public async Task<object> publicGetPairCandlestickCandletypeYyyymmdd (object parameters = null)
    {
        return await this.callAsync ("publicGetPairCandlestickCandletypeYyyymmdd",parameters);
    }

    public async Task<object> publicGetPairCircuitBreakInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetPairCircuitBreakInfo",parameters);
    }

    public async Task<object> privateGetUserAssets (object parameters = null)
    {
        return await this.callAsync ("privateGetUserAssets",parameters);
    }

    public async Task<object> privateGetUserSpotOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetUserSpotOrder",parameters);
    }

    public async Task<object> privateGetUserSpotActiveOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetUserSpotActiveOrders",parameters);
    }

    public async Task<object> privateGetUserMarginPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetUserMarginPositions",parameters);
    }

    public async Task<object> privateGetUserSpotTradeHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetUserSpotTradeHistory",parameters);
    }

    public async Task<object> privateGetUserDepositHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetUserDepositHistory",parameters);
    }

    public async Task<object> privateGetUserUnconfirmedDeposits (object parameters = null)
    {
        return await this.callAsync ("privateGetUserUnconfirmedDeposits",parameters);
    }

    public async Task<object> privateGetUserDepositOriginators (object parameters = null)
    {
        return await this.callAsync ("privateGetUserDepositOriginators",parameters);
    }

    public async Task<object> privateGetUserWithdrawalAccount (object parameters = null)
    {
        return await this.callAsync ("privateGetUserWithdrawalAccount",parameters);
    }

    public async Task<object> privateGetUserWithdrawalHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetUserWithdrawalHistory",parameters);
    }

    public async Task<object> privateGetSpotStatus (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotStatus",parameters);
    }

    public async Task<object> privateGetSpotPairs (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotPairs",parameters);
    }

    public async Task<object> privatePostUserSpotOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostUserSpotOrder",parameters);
    }

    public async Task<object> privatePostUserSpotCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostUserSpotCancelOrder",parameters);
    }

    public async Task<object> privatePostUserSpotCancelOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostUserSpotCancelOrders",parameters);
    }

    public async Task<object> privatePostUserSpotOrdersInfo (object parameters = null)
    {
        return await this.callAsync ("privatePostUserSpotOrdersInfo",parameters);
    }

    public async Task<object> privatePostUserConfirmDeposits (object parameters = null)
    {
        return await this.callAsync ("privatePostUserConfirmDeposits",parameters);
    }

    public async Task<object> privatePostUserConfirmDepositsAll (object parameters = null)
    {
        return await this.callAsync ("privatePostUserConfirmDepositsAll",parameters);
    }

    public async Task<object> privatePostUserRequestWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostUserRequestWithdrawal",parameters);
    }

    public async Task<object> marketsGetSpotPairs (object parameters = null)
    {
        return await this.callAsync ("marketsGetSpotPairs",parameters);
    }

}