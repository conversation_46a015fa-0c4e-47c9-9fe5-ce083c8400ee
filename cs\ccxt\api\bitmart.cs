// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bitmart : Exchange
{
    public bitmart (object args = null): base(args) {}

    public async Task<object> publicGetSystemTime (object parameters = null)
    {
        return await this.callAsync ("publicGetSystemTime",parameters);
    }

    public async Task<object> publicGetSystemService (object parameters = null)
    {
        return await this.callAsync ("publicGetSystemService",parameters);
    }

    public async Task<object> publicGetSpotV1Currencies (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV1Currencies",parameters);
    }

    public async Task<object> publicGetSpotV1Symbols (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV1Symbols",parameters);
    }

    public async Task<object> publicGetSpotV1SymbolsDetails (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV1SymbolsDetails",parameters);
    }

    public async Task<object> publicGetSpotQuotationV3Tickers (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotQuotationV3Tickers",parameters);
    }

    public async Task<object> publicGetSpotQuotationV3Ticker (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotQuotationV3Ticker",parameters);
    }

    public async Task<object> publicGetSpotQuotationV3LiteKlines (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotQuotationV3LiteKlines",parameters);
    }

    public async Task<object> publicGetSpotQuotationV3Klines (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotQuotationV3Klines",parameters);
    }

    public async Task<object> publicGetSpotQuotationV3Books (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotQuotationV3Books",parameters);
    }

    public async Task<object> publicGetSpotQuotationV3Trades (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotQuotationV3Trades",parameters);
    }

    public async Task<object> publicGetSpotV1Ticker (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV1Ticker",parameters);
    }

    public async Task<object> publicGetSpotV2Ticker (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV2Ticker",parameters);
    }

    public async Task<object> publicGetSpotV1TickerDetail (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV1TickerDetail",parameters);
    }

    public async Task<object> publicGetSpotV1Steps (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV1Steps",parameters);
    }

    public async Task<object> publicGetSpotV1SymbolsKline (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV1SymbolsKline",parameters);
    }

    public async Task<object> publicGetSpotV1SymbolsBook (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV1SymbolsBook",parameters);
    }

    public async Task<object> publicGetSpotV1SymbolsTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV1SymbolsTrades",parameters);
    }

    public async Task<object> publicGetContractV1Tickers (object parameters = null)
    {
        return await this.callAsync ("publicGetContractV1Tickers",parameters);
    }

    public async Task<object> publicGetContractPublicDetails (object parameters = null)
    {
        return await this.callAsync ("publicGetContractPublicDetails",parameters);
    }

    public async Task<object> publicGetContractPublicDepth (object parameters = null)
    {
        return await this.callAsync ("publicGetContractPublicDepth",parameters);
    }

    public async Task<object> publicGetContractPublicOpenInterest (object parameters = null)
    {
        return await this.callAsync ("publicGetContractPublicOpenInterest",parameters);
    }

    public async Task<object> publicGetContractPublicFundingRate (object parameters = null)
    {
        return await this.callAsync ("publicGetContractPublicFundingRate",parameters);
    }

    public async Task<object> publicGetContractPublicFundingRateHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetContractPublicFundingRateHistory",parameters);
    }

    public async Task<object> publicGetContractPublicKline (object parameters = null)
    {
        return await this.callAsync ("publicGetContractPublicKline",parameters);
    }

    public async Task<object> publicGetAccountV1Currencies (object parameters = null)
    {
        return await this.callAsync ("publicGetAccountV1Currencies",parameters);
    }

    public async Task<object> publicGetContractPublicMarkpriceKline (object parameters = null)
    {
        return await this.callAsync ("publicGetContractPublicMarkpriceKline",parameters);
    }

    public async Task<object> privateGetAccountSubAccountV1TransferList (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubAccountV1TransferList",parameters);
    }

    public async Task<object> privateGetAccountSubAccountV1TransferHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubAccountV1TransferHistory",parameters);
    }

    public async Task<object> privateGetAccountSubAccountMainV1Wallet (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubAccountMainV1Wallet",parameters);
    }

    public async Task<object> privateGetAccountSubAccountMainV1SubaccountList (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubAccountMainV1SubaccountList",parameters);
    }

    public async Task<object> privateGetAccountContractSubAccountMainV1Wallet (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountContractSubAccountMainV1Wallet",parameters);
    }

    public async Task<object> privateGetAccountContractSubAccountMainV1TransferList (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountContractSubAccountMainV1TransferList",parameters);
    }

    public async Task<object> privateGetAccountContractSubAccountV1TransferHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountContractSubAccountV1TransferHistory",parameters);
    }

    public async Task<object> privateGetAccountV1Wallet (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountV1Wallet",parameters);
    }

    public async Task<object> privateGetAccountV1Currencies (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountV1Currencies",parameters);
    }

    public async Task<object> privateGetSpotV1Wallet (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1Wallet",parameters);
    }

    public async Task<object> privateGetAccountV1DepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountV1DepositAddress",parameters);
    }

    public async Task<object> privateGetAccountV1WithdrawCharge (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountV1WithdrawCharge",parameters);
    }

    public async Task<object> privateGetAccountV2DepositWithdrawHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountV2DepositWithdrawHistory",parameters);
    }

    public async Task<object> privateGetAccountV1DepositWithdrawDetail (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountV1DepositWithdrawDetail",parameters);
    }

    public async Task<object> privateGetAccountV1WithdrawAddressList (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountV1WithdrawAddressList",parameters);
    }

    public async Task<object> privateGetSpotV1OrderDetail (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1OrderDetail",parameters);
    }

    public async Task<object> privateGetSpotV2Orders (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV2Orders",parameters);
    }

    public async Task<object> privateGetSpotV1Trades (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1Trades",parameters);
    }

    public async Task<object> privateGetSpotV2Trades (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV2Trades",parameters);
    }

    public async Task<object> privateGetSpotV3Orders (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3Orders",parameters);
    }

    public async Task<object> privateGetSpotV2OrderDetail (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV2OrderDetail",parameters);
    }

    public async Task<object> privateGetSpotV1MarginIsolatedBorrowRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1MarginIsolatedBorrowRecord",parameters);
    }

    public async Task<object> privateGetSpotV1MarginIsolatedRepayRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1MarginIsolatedRepayRecord",parameters);
    }

    public async Task<object> privateGetSpotV1MarginIsolatedPairs (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1MarginIsolatedPairs",parameters);
    }

    public async Task<object> privateGetSpotV1MarginIsolatedAccount (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1MarginIsolatedAccount",parameters);
    }

    public async Task<object> privateGetSpotV1TradeFee (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1TradeFee",parameters);
    }

    public async Task<object> privateGetSpotV1UserFee (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1UserFee",parameters);
    }

    public async Task<object> privateGetSpotV1BrokerRebate (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV1BrokerRebate",parameters);
    }

    public async Task<object> privateGetContractPrivateAssetsDetail (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateAssetsDetail",parameters);
    }

    public async Task<object> privateGetContractPrivateOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateOrder",parameters);
    }

    public async Task<object> privateGetContractPrivateOrderHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateOrderHistory",parameters);
    }

    public async Task<object> privateGetContractPrivatePosition (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivatePosition",parameters);
    }

    public async Task<object> privateGetContractPrivatePositionV2 (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivatePositionV2",parameters);
    }

    public async Task<object> privateGetContractPrivateGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateGetOpenOrders",parameters);
    }

    public async Task<object> privateGetContractPrivateCurrentPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateCurrentPlanOrder",parameters);
    }

    public async Task<object> privateGetContractPrivateTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateTrades",parameters);
    }

    public async Task<object> privateGetContractPrivatePositionRisk (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivatePositionRisk",parameters);
    }

    public async Task<object> privateGetContractPrivateAffilateRebateList (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateAffilateRebateList",parameters);
    }

    public async Task<object> privateGetContractPrivateAffilateTradeList (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateAffilateTradeList",parameters);
    }

    public async Task<object> privateGetContractPrivateTransactionHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateTransactionHistory",parameters);
    }

    public async Task<object> privateGetContractPrivateGetPositionMode (object parameters = null)
    {
        return await this.callAsync ("privateGetContractPrivateGetPositionMode",parameters);
    }

    public async Task<object> privatePostAccountSubAccountMainV1SubToMain (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSubAccountMainV1SubToMain",parameters);
    }

    public async Task<object> privatePostAccountSubAccountSubV1SubToMain (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSubAccountSubV1SubToMain",parameters);
    }

    public async Task<object> privatePostAccountSubAccountMainV1MainToSub (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSubAccountMainV1MainToSub",parameters);
    }

    public async Task<object> privatePostAccountSubAccountSubV1SubToSub (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSubAccountSubV1SubToSub",parameters);
    }

    public async Task<object> privatePostAccountSubAccountMainV1SubToSub (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSubAccountMainV1SubToSub",parameters);
    }

    public async Task<object> privatePostAccountContractSubAccountMainV1SubToMain (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountContractSubAccountMainV1SubToMain",parameters);
    }

    public async Task<object> privatePostAccountContractSubAccountMainV1MainToSub (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountContractSubAccountMainV1MainToSub",parameters);
    }

    public async Task<object> privatePostAccountContractSubAccountSubV1SubToMain (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountContractSubAccountSubV1SubToMain",parameters);
    }

    public async Task<object> privatePostAccountV1WithdrawApply (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountV1WithdrawApply",parameters);
    }

    public async Task<object> privatePostSpotV1SubmitOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV1SubmitOrder",parameters);
    }

    public async Task<object> privatePostSpotV1BatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV1BatchOrders",parameters);
    }

    public async Task<object> privatePostSpotV2CancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV2CancelOrder",parameters);
    }

    public async Task<object> privatePostSpotV1CancelOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV1CancelOrders",parameters);
    }

    public async Task<object> privatePostSpotV4QueryOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV4QueryOrder",parameters);
    }

    public async Task<object> privatePostSpotV4QueryClientOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV4QueryClientOrder",parameters);
    }

    public async Task<object> privatePostSpotV4QueryOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV4QueryOpenOrders",parameters);
    }

    public async Task<object> privatePostSpotV4QueryHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV4QueryHistoryOrders",parameters);
    }

    public async Task<object> privatePostSpotV4QueryTrades (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV4QueryTrades",parameters);
    }

    public async Task<object> privatePostSpotV4QueryOrderTrades (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV4QueryOrderTrades",parameters);
    }

    public async Task<object> privatePostSpotV4CancelOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV4CancelOrders",parameters);
    }

    public async Task<object> privatePostSpotV4CancelAll (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV4CancelAll",parameters);
    }

    public async Task<object> privatePostSpotV4BatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV4BatchOrders",parameters);
    }

    public async Task<object> privatePostSpotV3CancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV3CancelOrder",parameters);
    }

    public async Task<object> privatePostSpotV2BatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV2BatchOrders",parameters);
    }

    public async Task<object> privatePostSpotV2SubmitOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV2SubmitOrder",parameters);
    }

    public async Task<object> privatePostSpotV1MarginSubmitOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV1MarginSubmitOrder",parameters);
    }

    public async Task<object> privatePostSpotV1MarginIsolatedBorrow (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV1MarginIsolatedBorrow",parameters);
    }

    public async Task<object> privatePostSpotV1MarginIsolatedRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV1MarginIsolatedRepay",parameters);
    }

    public async Task<object> privatePostSpotV1MarginIsolatedTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV1MarginIsolatedTransfer",parameters);
    }

    public async Task<object> privatePostAccountV1TransferContractList (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountV1TransferContractList",parameters);
    }

    public async Task<object> privatePostAccountV1TransferContract (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountV1TransferContract",parameters);
    }

    public async Task<object> privatePostContractPrivateSubmitOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateSubmitOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateCancelOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateCancelOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateCancelOrders",parameters);
    }

    public async Task<object> privatePostContractPrivateSubmitPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateSubmitPlanOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateCancelPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateCancelPlanOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateSubmitLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateSubmitLeverage",parameters);
    }

    public async Task<object> privatePostContractPrivateSubmitTpSlOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateSubmitTpSlOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateModifyPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateModifyPlanOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateModifyPresetPlanOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateModifyPresetPlanOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateModifyLimitOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateModifyLimitOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateModifyTpSlOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateModifyTpSlOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateSubmitTrailOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateSubmitTrailOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateCancelTrailOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateCancelTrailOrder",parameters);
    }

    public async Task<object> privatePostContractPrivateSetPositionMode (object parameters = null)
    {
        return await this.callAsync ("privatePostContractPrivateSetPositionMode",parameters);
    }

}