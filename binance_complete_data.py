#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
币安完整数据获取器
获取币安交易所的全部公开数据
"""

import requests
import json
import pandas as pd
from datetime import datetime
import time
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

class BinanceCompleteDataCollector:
    def __init__(self, max_workers=10):
        """初始化完整数据收集器"""
        self.base_url = "https://api.binance.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.max_workers = max_workers
        self.lock = threading.Lock()
        
        # 创建输出目录
        self.output_dir = f"binance_complete_data_{self.timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def get_all_symbols(self):
        """获取所有交易对"""
        try:
            print("📊 获取所有交易对信息...")
            response = self.session.get(f"{self.base_url}/api/v3/exchangeInfo", timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                symbols = data.get('symbols', [])
                
                # 分类统计
                all_symbols = []
                usdt_symbols = []
                btc_symbols = []
                eth_symbols = []
                bnb_symbols = []
                
                for symbol_info in symbols:
                    if symbol_info['status'] == 'TRADING':
                        symbol = symbol_info['symbol']
                        all_symbols.append(symbol)
                        
                        if symbol.endswith('USDT'):
                            usdt_symbols.append(symbol)
                        elif symbol.endswith('BTC'):
                            btc_symbols.append(symbol)
                        elif symbol.endswith('ETH'):
                            eth_symbols.append(symbol)
                        elif symbol.endswith('BNB'):
                            bnb_symbols.append(symbol)
                
                print(f"✅ 获取到交易对统计:")
                print(f"  - 总交易对: {len(all_symbols)}")
                print(f"  - USDT交易对: {len(usdt_symbols)}")
                print(f"  - BTC交易对: {len(btc_symbols)}")
                print(f"  - ETH交易对: {len(eth_symbols)}")
                print(f"  - BNB交易对: {len(bnb_symbols)}")
                
                # 保存交易对信息
                symbol_data = []
                for symbol_info in symbols:
                    if symbol_info['status'] == 'TRADING':
                        symbol_data.append({
                            'symbol': symbol_info['symbol'],
                            'base_asset': symbol_info['baseAsset'],
                            'quote_asset': symbol_info['quoteAsset'],
                            'status': symbol_info['status'],
                            'base_precision': symbol_info['baseAssetPrecision'],
                            'quote_precision': symbol_info['quoteAssetPrecision'],
                            'order_types': ','.join(symbol_info['orderTypes']),
                            'iceberg_allowed': symbol_info['icebergAllowed'],
                            'oco_allowed': symbol_info['ocoAllowed'],
                            'is_spot_trading_allowed': symbol_info['isSpotTradingAllowed'],
                            'is_margin_trading_allowed': symbol_info['isMarginTradingAllowed']
                        })
                
                df = pd.DataFrame(symbol_data)
                df.to_csv(f"{self.output_dir}/all_symbols.csv", index=False, encoding='utf-8')
                print(f"💾 交易对信息已保存到 {self.output_dir}/all_symbols.csv")
                
                return {
                    'all': all_symbols,
                    'usdt': usdt_symbols,
                    'btc': btc_symbols,
                    'eth': eth_symbols,
                    'bnb': bnb_symbols
                }
                
            else:
                print(f"❌ 获取失败，状态码: {response.status_code}")
                return {}
                
        except Exception as e:
            print(f"❌ 获取交易对失败: {e}")
            return {}
    
    def get_all_24hr_tickers(self):
        """获取所有24小时行情"""
        try:
            print("\n📈 获取所有24小时行情数据...")
            response = self.session.get(f"{self.base_url}/api/v3/ticker/24hr", timeout=20)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 获取到 {len(data)} 个交易对的24小时行情")
                
                # 转换为DataFrame并保存
                ticker_data = []
                for ticker in data:
                    ticker_data.append({
                        'symbol': ticker['symbol'],
                        'price_change': float(ticker['priceChange']),
                        'price_change_percent': float(ticker['priceChangePercent']),
                        'weighted_avg_price': float(ticker['weightedAvgPrice']),
                        'prev_close_price': float(ticker['prevClosePrice']),
                        'last_price': float(ticker['lastPrice']),
                        'last_qty': float(ticker['lastQty']),
                        'bid_price': float(ticker['bidPrice']),
                        'bid_qty': float(ticker['bidQty']),
                        'ask_price': float(ticker['askPrice']),
                        'ask_qty': float(ticker['askQty']),
                        'open_price': float(ticker['openPrice']),
                        'high_price': float(ticker['highPrice']),
                        'low_price': float(ticker['lowPrice']),
                        'volume': float(ticker['volume']),
                        'quote_volume': float(ticker['quoteVolume']),
                        'open_time': int(ticker['openTime']),
                        'close_time': int(ticker['closeTime']),
                        'first_id': int(ticker['firstId']),
                        'last_id': int(ticker['lastId']),
                        'count': int(ticker['count'])
                    })
                
                df = pd.DataFrame(ticker_data)
                df.to_csv(f"{self.output_dir}/all_24hr_tickers.csv", index=False, encoding='utf-8')
                print(f"💾 24小时行情已保存到 {self.output_dir}/all_24hr_tickers.csv")
                
                return data
                
            else:
                print(f"❌ 获取失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 获取24小时行情失败: {e}")
            return []
    
    def get_orderbook_batch(self, symbols, limit=100):
        """批量获取订单簿"""
        def get_single_orderbook(symbol):
            try:
                params = {'symbol': symbol, 'limit': limit}
                response = self.session.get(f"{self.base_url}/api/v3/depth", params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        'symbol': symbol,
                        'bids': [[float(price), float(qty)] for price, qty in data['bids']],
                        'asks': [[float(price), float(qty)] for price, qty in data['asks']],
                        'timestamp': int(time.time() * 1000)
                    }
                else:
                    return None
                    
            except Exception as e:
                with self.lock:
                    print(f"    ❌ {symbol} 订单簿获取失败: {e}")
                return None
        
        print(f"\n📖 批量获取 {len(symbols)} 个交易对的订单簿...")
        orderbooks = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_symbol = {executor.submit(get_single_orderbook, symbol): symbol for symbol in symbols}
            
            completed = 0
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                result = future.result()
                
                if result:
                    orderbooks.append(result)
                
                completed += 1
                if completed % 50 == 0:
                    print(f"  进度: {completed}/{len(symbols)}")
        
        print(f"✅ 成功获取 {len(orderbooks)} 个订单簿")
        
        # 保存订单簿数据
        if orderbooks:
            orderbook_data = []
            for orderbook in orderbooks:
                symbol = orderbook['symbol']
                timestamp = orderbook['timestamp']
                
                # 买盘
                for i, (price, qty) in enumerate(orderbook['bids'][:20]):  # 保存前20档
                    orderbook_data.append({
                        'symbol': symbol,
                        'side': 'bid',
                        'level': i + 1,
                        'price': price,
                        'quantity': qty,
                        'timestamp': timestamp
                    })
                
                # 卖盘
                for i, (price, qty) in enumerate(orderbook['asks'][:20]):  # 保存前20档
                    orderbook_data.append({
                        'symbol': symbol,
                        'side': 'ask',
                        'level': i + 1,
                        'price': price,
                        'quantity': qty,
                        'timestamp': timestamp
                    })
            
            df = pd.DataFrame(orderbook_data)
            df.to_csv(f"{self.output_dir}/all_orderbooks.csv", index=False, encoding='utf-8')
            print(f"💾 订单簿数据已保存到 {self.output_dir}/all_orderbooks.csv")
        
        return orderbooks
    
    def get_klines_batch(self, symbols, interval='1h', limit=100):
        """批量获取K线数据"""
        def get_single_klines(symbol):
            try:
                params = {'symbol': symbol, 'interval': interval, 'limit': limit}
                response = self.session.get(f"{self.base_url}/api/v3/klines", params=params, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    klines = []
                    for kline in data:
                        klines.append([
                            symbol,
                            int(kline[0]),      # 开盘时间
                            float(kline[1]),    # 开盘价
                            float(kline[2]),    # 最高价
                            float(kline[3]),    # 最低价
                            float(kline[4]),    # 收盘价
                            float(kline[5]),    # 成交量
                            int(kline[6]),      # 收盘时间
                            float(kline[7]),    # 成交额
                            int(kline[8]),      # 成交笔数
                            float(kline[9]),    # 主动买入成交量
                            float(kline[10]),   # 主动买入成交额
                        ])
                    return klines
                else:
                    return []
                    
            except Exception as e:
                with self.lock:
                    print(f"    ❌ {symbol} K线获取失败: {e}")
                return []
        
        print(f"\n📊 批量获取 {len(symbols)} 个交易对的{interval}K线数据...")
        all_klines = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_symbol = {executor.submit(get_single_klines, symbol): symbol for symbol in symbols}
            
            completed = 0
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                klines = future.result()
                
                if klines:
                    all_klines.extend(klines)
                
                completed += 1
                if completed % 50 == 0:
                    print(f"  进度: {completed}/{len(symbols)}")
        
        print(f"✅ 成功获取 {len(all_klines)} 条K线数据")
        
        # 保存K线数据
        if all_klines:
            df = pd.DataFrame(all_klines, columns=[
                'symbol', 'open_time', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades_count', 'taker_buy_volume', 'taker_buy_quote_volume'
            ])
            
            # 添加时间格式
            df['open_datetime'] = pd.to_datetime(df['open_time'], unit='ms')
            df['close_datetime'] = pd.to_datetime(df['close_time'], unit='ms')
            
            df.to_csv(f"{self.output_dir}/all_klines_{interval}.csv", index=False, encoding='utf-8')
            print(f"💾 K线数据已保存到 {self.output_dir}/all_klines_{interval}.csv")
        
        return all_klines
    
    def get_recent_trades_batch(self, symbols, limit=100):
        """批量获取最近交易"""
        def get_single_trades(symbol):
            try:
                params = {'symbol': symbol, 'limit': limit}
                response = self.session.get(f"{self.base_url}/api/v3/trades", params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    trades = []
                    for trade in data:
                        trades.append({
                            'symbol': symbol,
                            'id': trade['id'],
                            'price': float(trade['price']),
                            'qty': float(trade['qty']),
                            'quote_qty': float(trade['quoteQty']),
                            'time': trade['time'],
                            'is_buyer_maker': trade['isBuyerMaker']
                        })
                    return trades
                else:
                    return []
                    
            except Exception as e:
                with self.lock:
                    print(f"    ❌ {symbol} 交易数据获取失败: {e}")
                return []
        
        print(f"\n💱 批量获取 {len(symbols)} 个交易对的最近交易...")
        all_trades = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_symbol = {executor.submit(get_single_trades, symbol): symbol for symbol in symbols}
            
            completed = 0
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                trades = future.result()
                
                if trades:
                    all_trades.extend(trades)
                
                completed += 1
                if completed % 50 == 0:
                    print(f"  进度: {completed}/{len(symbols)}")
        
        print(f"✅ 成功获取 {len(all_trades)} 条交易数据")
        
        # 保存交易数据
        if all_trades:
            df = pd.DataFrame(all_trades)
            df['datetime'] = pd.to_datetime(df['time'], unit='ms')
            df.to_csv(f"{self.output_dir}/all_recent_trades.csv", index=False, encoding='utf-8')
            print(f"💾 交易数据已保存到 {self.output_dir}/all_recent_trades.csv")
        
        return all_trades
    
    def collect_complete_data(self):
        """收集完整数据"""
        print("🚀 开始收集币安完整数据...")
        print(f"📅 时间: {datetime.now()}")
        print(f"📁 输出目录: {self.output_dir}")
        print("=" * 80)
        
        try:
            # 1. 获取所有交易对
            symbols_dict = self.get_all_symbols()
            if not symbols_dict:
                print("❌ 无法获取交易对信息")
                return None
            
            # 2. 获取所有24小时行情
            all_tickers = self.get_all_24hr_tickers()
            
            # 3. 获取USDT交易对的订单簿（限制数量避免过多请求）
            usdt_symbols = symbols_dict.get('usdt', [])[:200]  # 前200个USDT交易对
            if usdt_symbols:
                orderbooks = self.get_orderbook_batch(usdt_symbols, limit=20)
            
            # 4. 获取主要交易对的K线数据
            major_symbols = usdt_symbols[:100]  # 前100个USDT交易对
            if major_symbols:
                klines_1h = self.get_klines_batch(major_symbols, '1h', 24)
                klines_1d = self.get_klines_batch(major_symbols[:50], '1d', 30)  # 前50个获取日K线
            
            # 5. 获取热门交易对的最近交易
            hot_symbols = usdt_symbols[:50]  # 前50个热门交易对
            if hot_symbols:
                recent_trades = self.get_recent_trades_batch(hot_symbols, limit=100)
            
            print("=" * 80)
            print("🎉 完整数据收集完成！")
            print(f"📊 数据统计:")
            print(f"  - 交易对总数: {len(symbols_dict.get('all', []))}")
            print(f"  - 24小时行情: {len(all_tickers)}")
            print(f"  - 订单簿数据: {len(orderbooks) if 'orderbooks' in locals() else 0} 个交易对")
            print(f"  - 1小时K线: {len(klines_1h) if 'klines_1h' in locals() else 0} 条")
            print(f"  - 1日K线: {len(klines_1d) if 'klines_1d' in locals() else 0} 条")
            print(f"  - 最近交易: {len(recent_trades) if 'recent_trades' in locals() else 0} 条")
            print(f"📁 所有数据已保存到: {self.output_dir}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据收集失败: {e}")
            return False

def main():
    """主函数"""
    print("💰 币安完整数据获取器")
    print("获取币安交易所的全部公开数据")
    print("=" * 80)
    
    # 询问用户配置
    print("请选择数据获取范围:")
    print("1. 快速模式 (主要USDT交易对)")
    print("2. 完整模式 (所有可用数据)")
    
    try:
        choice = input("请选择 (1 或 2，默认1): ").strip() or "1"
        
        if choice == "2":
            max_workers = 20
            print("🔥 完整模式：将获取所有可用数据，可能需要较长时间...")
        else:
            max_workers = 10
            print("⚡ 快速模式：获取主要交易对数据...")
        
        collector = BinanceCompleteDataCollector(max_workers=max_workers)
        success = collector.collect_complete_data()
        
        if success:
            print("\n✅ 数据收集成功！")
            print(f"📁 请查看目录 '{collector.output_dir}' 中的所有CSV文件")
            print("💡 这些都是真实的币安完整数据！")
        else:
            print("\n❌ 数据收集失败")
            
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")

if __name__ == "__main__":
    main()
