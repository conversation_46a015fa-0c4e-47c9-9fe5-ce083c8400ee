// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class p2b : Exchange
{
    public p2b (object args = null): base(args) {}

    public async Task<object> publicGetMarkets (object parameters = null)
    {
        return await this.callAsync ("publicGetMarkets",parameters);
    }

    public async Task<object> publicGetMarket (object parameters = null)
    {
        return await this.callAsync ("publicGetMarket",parameters);
    }

    public async Task<object> publicGetTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetTickers",parameters);
    }

    public async Task<object> publicGetTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetTicker",parameters);
    }

    public async Task<object> publicGetBook (object parameters = null)
    {
        return await this.callAsync ("publicGetBook",parameters);
    }

    public async Task<object> publicGetHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetHistory",parameters);
    }

    public async Task<object> publicGetDepthResult (object parameters = null)
    {
        return await this.callAsync ("publicGetDepthResult",parameters);
    }

    public async Task<object> publicGetMarketKline (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketKline",parameters);
    }

    public async Task<object> privatePostAccountBalances (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountBalances",parameters);
    }

    public async Task<object> privatePostAccountBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountBalance",parameters);
    }

    public async Task<object> privatePostOrderNew (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderNew",parameters);
    }

    public async Task<object> privatePostOrderCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderCancel",parameters);
    }

    public async Task<object> privatePostOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrders",parameters);
    }

    public async Task<object> privatePostAccountMarketOrderHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountMarketOrderHistory",parameters);
    }

    public async Task<object> privatePostAccountMarketDealHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountMarketDealHistory",parameters);
    }

    public async Task<object> privatePostAccountOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountOrder",parameters);
    }

    public async Task<object> privatePostAccountOrderHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountOrderHistory",parameters);
    }

    public async Task<object> privatePostAccountExecutedHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountExecutedHistory",parameters);
    }

}