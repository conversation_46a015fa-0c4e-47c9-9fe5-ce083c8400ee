#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
币安简单真实数据获取器
使用同步方式获取币安真实数据
"""

import ccxt
import json
import pandas as pd
from datetime import datetime
import time
import sys
import os

# 添加当前目录到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'python'))

def test_connection():
    """测试连接"""
    try:
        print("🔗 测试币安API连接...")
        
        # 创建交易所实例
        exchange = ccxt.binance({
            'enableRateLimit': True,
            'timeout': 30000,
            'options': {
                'defaultType': 'spot',
            }
        })
        
        # 测试获取服务器时间
        server_time = exchange.fetch_time()
        server_datetime = datetime.fromtimestamp(server_time / 1000)
        print(f"✅ 连接成功！")
        print(f"  服务器时间: {server_datetime}")
        print(f"  本地时间: {datetime.now()}")
        
        return exchange
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("可能的原因:")
        print("  1. 网络连接问题")
        print("  2. 防火墙阻止")
        print("  3. 需要代理设置")
        return None

def get_basic_info(exchange):
    """获取基本信息"""
    try:
        print("\nℹ️ 获取交易所基本信息...")
        
        # 获取交易所状态
        status = exchange.fetch_status()
        print(f"  交易所状态: {status.get('status', 'unknown')}")
        
        # 显示支持的功能
        has = exchange.has
        print(f"  支持的主要功能:")
        features = ['fetchTicker', 'fetchTickers', 'fetchOrderBook', 'fetchTrades', 'fetchOHLCV']
        for feature in features:
            status_icon = "✅" if has.get(feature) else "❌"
            print(f"    {status_icon} {feature}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取基本信息失败: {e}")
        return False

def get_markets(exchange):
    """获取市场信息"""
    try:
        print("\n📊 获取市场信息...")
        
        markets = exchange.load_markets()
        
        # 统计市场类型
        spot_markets = [s for s, m in markets.items() if m.get('spot', False)]
        usdt_markets = [s for s in spot_markets if s.endswith('/USDT')]
        btc_markets = [s for s in spot_markets if s.endswith('/BTC')]
        
        print(f"✅ 市场信息获取成功:")
        print(f"  总交易对: {len(markets)}")
        print(f"  现货交易对: {len(spot_markets)}")
        print(f"  USDT交易对: {len(usdt_markets)}")
        print(f"  BTC交易对: {len(btc_markets)}")
        
        # 显示一些热门交易对
        popular_pairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'XRP/USDT']
        available_pairs = [pair for pair in popular_pairs if pair in markets]
        print(f"  可用的热门交易对: {', '.join(available_pairs)}")
        
        return markets
        
    except Exception as e:
        print(f"❌ 获取市场信息失败: {e}")
        return {}

def get_single_ticker(exchange, symbol):
    """获取单个交易对行情"""
    try:
        print(f"\n📈 获取 {symbol} 行情...")
        
        ticker = exchange.fetch_ticker(symbol)
        
        print(f"✅ {symbol} 行情:")
        print(f"  最新价格: ${ticker['last']:,.4f}")
        print(f"  24h最高: ${ticker['high']:,.4f}")
        print(f"  24h最低: ${ticker['low']:,.4f}")
        print(f"  24h成交量: {ticker['baseVolume']:,.2f}")
        print(f"  24h成交额: ${ticker['quoteVolume']:,.0f}")
        
        if ticker['percentage']:
            print(f"  24h涨跌: {ticker['percentage']:+.2f}%")
        
        return ticker
        
    except Exception as e:
        print(f"❌ 获取 {symbol} 行情失败: {e}")
        return None

def get_multiple_tickers(exchange, symbols):
    """获取多个交易对行情"""
    try:
        print(f"\n📊 获取多个交易对行情...")
        
        tickers = {}
        for i, symbol in enumerate(symbols, 1):
            try:
                print(f"  {i}/{len(symbols)} 获取 {symbol}...")
                ticker = exchange.fetch_ticker(symbol)
                tickers[symbol] = ticker
                
                # 显示简要信息
                price = ticker['last']
                change = ticker['percentage'] or 0
                volume = ticker['quoteVolume'] or 0
                print(f"    ${price:>10.4f} ({change:+6.2f}%) 量:${volume:>12,.0f}")
                
                time.sleep(0.2)  # 避免触发速率限制
                
            except Exception as e:
                print(f"    ❌ 获取 {symbol} 失败: {e}")
                continue
        
        print(f"✅ 成功获取 {len(tickers)} 个交易对行情")
        return tickers
        
    except Exception as e:
        print(f"❌ 获取多个行情失败: {e}")
        return {}

def get_orderbook(exchange, symbol, limit=10):
    """获取订单簿"""
    try:
        print(f"\n📖 获取 {symbol} 订单簿...")
        
        orderbook = exchange.fetch_order_book(symbol, limit)
        
        print(f"✅ {symbol} 订单簿 (前{limit}档):")
        print(f"  买盘 (Bids):")
        for i, (price, amount) in enumerate(orderbook['bids'][:5], 1):
            print(f"    {i}. ${price:>10.4f} × {amount:>10.4f}")
        
        print(f"  卖盘 (Asks):")
        for i, (price, amount) in enumerate(orderbook['asks'][:5], 1):
            print(f"    {i}. ${price:>10.4f} × {amount:>10.4f}")
        
        # 计算价差
        if orderbook['bids'] and orderbook['asks']:
            best_bid = orderbook['bids'][0][0]
            best_ask = orderbook['asks'][0][0]
            spread = best_ask - best_bid
            spread_percent = (spread / best_bid) * 100
            print(f"  价差: ${spread:.4f} ({spread_percent:.3f}%)")
        
        return orderbook
        
    except Exception as e:
        print(f"❌ 获取 {symbol} 订单簿失败: {e}")
        return None

def get_klines(exchange, symbol, timeframe='1h', limit=24):
    """获取K线数据"""
    try:
        print(f"\n📊 获取 {symbol} {timeframe} K线数据...")
        
        klines = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
        
        print(f"✅ {symbol} {timeframe} K线 (最近{limit}条):")
        
        # 显示最近5条K线
        print(f"  最近5条K线:")
        for i, kline in enumerate(klines[-5:], 1):
            timestamp = kline[0]
            dt = datetime.fromtimestamp(timestamp / 1000)
            open_price = kline[1]
            high_price = kline[2]
            low_price = kline[3]
            close_price = kline[4]
            volume = kline[5]
            
            change = ((close_price - open_price) / open_price) * 100
            print(f"    {dt.strftime('%m-%d %H:%M')} O:{open_price:>8.2f} H:{high_price:>8.2f} L:{low_price:>8.2f} C:{close_price:>8.2f} V:{volume:>8.1f} ({change:+5.2f}%)")
        
        return klines
        
    except Exception as e:
        print(f"❌ 获取 {symbol} K线失败: {e}")
        return []

def save_data_to_files(data, timestamp):
    """保存数据到文件"""
    try:
        print(f"\n💾 保存数据到文件...")
        
        # 保存行情数据
        if 'tickers' in data and data['tickers']:
            ticker_data = []
            for symbol, ticker in data['tickers'].items():
                ticker_data.append({
                    'symbol': symbol,
                    'price': ticker['last'],
                    'high_24h': ticker['high'],
                    'low_24h': ticker['low'],
                    'volume_24h_base': ticker['baseVolume'],
                    'volume_24h_quote': ticker['quoteVolume'],
                    'change_24h': ticker['change'],
                    'change_24h_percent': ticker['percentage'],
                    'bid': ticker['bid'],
                    'ask': ticker['ask'],
                    'timestamp': ticker['timestamp'],
                    'datetime': ticker['datetime']
                })
            
            df = pd.DataFrame(ticker_data)
            filename = f'binance_real_tickers_{timestamp}.csv'
            df.to_csv(filename, index=False, encoding='utf-8')
            print(f"  ✅ 行情数据: {filename}")
        
        # 保存K线数据
        if 'klines' in data and data['klines']:
            kline_data = []
            for symbol, klines in data['klines'].items():
                for kline in klines:
                    kline_data.append({
                        'symbol': symbol,
                        'timestamp': kline[0],
                        'datetime': datetime.fromtimestamp(kline[0] / 1000).isoformat(),
                        'open': kline[1],
                        'high': kline[2],
                        'low': kline[3],
                        'close': kline[4],
                        'volume': kline[5]
                    })
            
            df = pd.DataFrame(kline_data)
            filename = f'binance_real_klines_{timestamp}.csv'
            df.to_csv(filename, index=False, encoding='utf-8')
            print(f"  ✅ K线数据: {filename}")
        
        print(f"💾 数据保存完成！")
        
    except Exception as e:
        print(f"❌ 保存数据失败: {e}")

def main():
    """主函数"""
    print("💰 币安简单真实数据获取器")
    print("=" * 60)
    
    # 1. 测试连接
    exchange = test_connection()
    if not exchange:
        print("\n❌ 无法连接到币安API，程序退出")
        return
    
    try:
        # 2. 获取基本信息
        get_basic_info(exchange)
        
        # 3. 获取市场信息
        markets = get_markets(exchange)
        if not markets:
            print("❌ 无法获取市场信息")
            return
        
        # 4. 获取单个热门交易对行情
        btc_ticker = get_single_ticker(exchange, 'BTC/USDT')
        
        # 5. 获取多个交易对行情
        popular_symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'XRP/USDT']
        available_symbols = [s for s in popular_symbols if s in markets]
        tickers = get_multiple_tickers(exchange, available_symbols[:3])  # 只获取前3个
        
        # 6. 获取BTC/USDT订单簿
        btc_orderbook = get_orderbook(exchange, 'BTC/USDT')
        
        # 7. 获取BTC/USDT K线数据
        btc_klines = get_klines(exchange, 'BTC/USDT', '1h', 24)
        
        # 8. 保存数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        data = {
            'tickers': tickers,
            'klines': {'BTC/USDT': btc_klines} if btc_klines else {}
        }
        save_data_to_files(data, timestamp)
        
        print("\n" + "=" * 60)
        print("🎉 真实数据获取完成！")
        print(f"  - 获取了 {len(tickers)} 个交易对的行情数据")
        print(f"  - 获取了 BTC/USDT 的订单簿数据")
        print(f"  - 获取了 BTC/USDT 的K线数据")
        print(f"  - 数据已保存到 CSV 文件")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    
    finally:
        print("\n🔚 程序结束")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")
        print("请检查网络连接")
