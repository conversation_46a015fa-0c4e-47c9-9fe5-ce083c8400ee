namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code


public partial class Exchange
{
    public async Task<Dictionary<string, object>> FetchCurrenciesWs(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchCurrenciesWs(parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<List<MarketInterface>> FetchMarkets(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchMarkets(parameters);
        return ((IList<object>)res).Select(item => new MarketInterface(item)).ToList<MarketInterface>();
    }
    public async Task<List<MarketInterface>> FetchMarketsWs(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchMarketsWs(parameters);
        return ((IList<object>)res).Select(item => new MarketInterface(item)).ToList<MarketInterface>();
    }
    public Dictionary<string, object> CreateSafeDictionary()
    {
        var res = this.createSafeDictionary();
        return ((Dictionary<string, object>)res);
    }
    public async Task<List<Account>> FetchAccounts(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchAccounts(parameters);
        return ((IList<object>)res).Select(item => new Account(item)).ToList<Account>();
    }
    public async Task<List<Trade>> FetchTrades(string symbol, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchTrades(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Trade(item)).ToList<Trade>();
    }
    public async Task<List<Trade>> FetchTradesWs(string symbol, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchTradesWs(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Trade(item)).ToList<Trade>();
    }
    public async Task<List<Liquidation>> WatchLiquidations(string symbol, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchLiquidations(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Liquidation(item)).ToList<Liquidation>();
    }
    public async Task<List<Liquidation>> WatchLiquidationsForSymbols(List<string> symbols, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchLiquidationsForSymbols(symbols, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Liquidation(item)).ToList<Liquidation>();
    }
    public async Task<List<Liquidation>> WatchMyLiquidations(string symbol, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchMyLiquidations(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Liquidation(item)).ToList<Liquidation>();
    }
    public async Task<List<Liquidation>> WatchMyLiquidationsForSymbols(List<string> symbols, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchMyLiquidationsForSymbols(symbols, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Liquidation(item)).ToList<Liquidation>();
    }
    public async Task<List<Trade>> WatchTrades(string symbol, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchTrades(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Trade(item)).ToList<Trade>();
    }
    public async Task<List<Trade>> WatchTradesForSymbols(List<string> symbols, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchTradesForSymbols(symbols, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Trade(item)).ToList<Trade>();
    }
    public async Task<List<Trade>> WatchMyTradesForSymbols(List<string> symbols, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchMyTradesForSymbols(symbols, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Trade(item)).ToList<Trade>();
    }
    public async Task<List<Order>> WatchOrdersForSymbols(List<string> symbols, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchOrdersForSymbols(symbols, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<Dictionary<string, Dictionary<string, List<OHLCV>>>> WatchOHLCVForSymbols(List<List<string>> symbolsAndTimeframes, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchOHLCVForSymbols(symbolsAndTimeframes, since, limit, parameters);
        return Helper.ConvertToDictionaryOHLCVList(res);
    }
    public async Task<ccxt.pro.IOrderBook> WatchOrderBookForSymbols(List<string> symbols, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchOrderBookForSymbols(symbols, limit, parameters);
        return ((ccxt.pro.IOrderBook) res).Copy();
    }
    public async Task<List<DepositAddress>> FetchDepositAddresses(List<String> codes = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchDepositAddresses(codes, parameters);
        return ((IList<object>)res).Select(item => new DepositAddress(item)).ToList<DepositAddress>();
    }
    public async Task<OrderBook> FetchOrderBook(string symbol, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOrderBook(symbol, limit, parameters);
        return new OrderBook(res);
    }
    public async Task<OrderBook> FetchOrderBookWs(string symbol, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOrderBookWs(symbol, limit, parameters);
        return new OrderBook(res);
    }
    public async Task<MarginMode> FetchMarginMode(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchMarginMode(symbol, parameters);
        return new MarginMode(res);
    }
    public async Task<MarginModes> FetchMarginModes(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchMarginModes(symbols, parameters);
        return new MarginModes(res);
    }
    public async Task<OrderBook> FetchRestOrderBookSafe(object symbol, object limit = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchRestOrderBookSafe(symbol, limit, parameters);
        return new OrderBook(res);
    }
    public async Task<ccxt.pro.IOrderBook> WatchOrderBook(string symbol, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchOrderBook(symbol, limit, parameters);
        return ((ccxt.pro.IOrderBook) res).Copy();
    }
    public async Task<Int64> FetchTime(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTime(parameters);
        return (Int64)res;
    }
    public async Task<Dictionary<string, object>> FetchTradingLimits(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTradingLimits(symbols, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<CrossBorrowRates> FetchCrossBorrowRates(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchCrossBorrowRates(parameters);
        return new CrossBorrowRates(res);
    }
    public async Task<IsolatedBorrowRates> FetchIsolatedBorrowRates(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchIsolatedBorrowRates(parameters);
        return new IsolatedBorrowRates(res);
    }
    public async Task<LeverageTiers> FetchLeverageTiers(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchLeverageTiers(symbols, parameters);
        return new LeverageTiers(res);
    }
    public async Task<FundingRates> FetchFundingRates(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchFundingRates(symbols, parameters);
        return new FundingRates(res);
    }
    public async Task<FundingRates> FetchFundingIntervals(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchFundingIntervals(symbols, parameters);
        return new FundingRates(res);
    }
    public async Task<FundingRate> WatchFundingRate(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.watchFundingRate(symbol, parameters);
        return new FundingRate(res);
    }
    public async Task<FundingRates> WatchFundingRates(List<string> symbols, Dictionary<string, object> parameters = null)
    {
        var res = await this.watchFundingRates(symbols, parameters);
        return new FundingRates(res);
    }
    public async Task<Dictionary<string, object>> WatchFundingRatesForSymbols(List<string> symbols, Dictionary<string, object> parameters = null)
    {
        var res = await this.watchFundingRatesForSymbols(symbols, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<TransferEntry> Transfer(string code, double amount, string fromAccount, string toAccount, Dictionary<string, object> parameters = null)
    {
        var res = await this.transfer(code, amount, fromAccount, toAccount, parameters);
        return new TransferEntry(res);
    }
    public async Task<Transaction> Withdraw(string code, double amount, string address, object tag = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.withdraw(code, amount, address, tag, parameters);
        return new Transaction(res);
    }
    public async Task<DepositAddress> CreateDepositAddress(string code, Dictionary<string, object> parameters = null)
    {
        var res = await this.createDepositAddress(code, parameters);
        return new DepositAddress(res);
    }
    public async Task<Dictionary<string, object>> SetLeverage(Int64 leverage, string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.setLeverage(leverage, symbol, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Leverage> FetchLeverage(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchLeverage(symbol, parameters);
        return new Leverage(res);
    }
    public async Task<Leverages> FetchLeverages(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchLeverages(symbols, parameters);
        return new Leverages(res);
    }
    public async Task<Dictionary<string, object>> SetPositionMode(bool hedged, string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.setPositionMode(hedged, symbol, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> SetMargin(string symbol, double amount, Dictionary<string, object> parameters = null)
    {
        var res = await this.setMargin(symbol, amount, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<LongShortRatio> FetchLongShortRatio(string symbol, string timeframe = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchLongShortRatio(symbol, timeframe, parameters);
        return new LongShortRatio(res);
    }
    public async Task<List<LongShortRatio>> FetchLongShortRatioHistory(string symbol = null, string timeframe = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchLongShortRatioHistory(symbol, timeframe, since, limit, parameters);
        return ((IList<object>)res).Select(item => new LongShortRatio(item)).ToList<LongShortRatio>();
    }
    public async Task<List<MarginModification>> FetchMarginAdjustmentHistory(string symbol = null, string type = null, double? since2 = 0, double? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchMarginAdjustmentHistory(symbol, type, since, limit, parameters);
        return ((IList<object>)res).Select(item => new MarginModification(item)).ToList<MarginModification>();
    }
    public async Task<Dictionary<string, object>> SetMarginMode(string marginMode, string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.setMarginMode(marginMode, symbol, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<List<DepositAddress>> FetchDepositAddressesByNetwork(string code, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchDepositAddressesByNetwork(code, parameters);
        return ((IList<object>)res).Select(item => new DepositAddress(item)).ToList<DepositAddress>();
    }
    public async Task<List<OpenInterest>> FetchOpenInterestHistory(string symbol, string timeframe = "1h", Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOpenInterestHistory(symbol, timeframe, since, limit, parameters);
        return ((IList<object>)res).Select(item => new OpenInterest(item)).ToList<OpenInterest>();
    }
    public async Task<OpenInterest> FetchOpenInterest(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchOpenInterest(symbol, parameters);
        return new OpenInterest(res);
    }
    public async Task<OpenInterests> FetchOpenInterests(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchOpenInterests(symbols, parameters);
        return new OpenInterests(res);
    }
    public async Task<Dictionary<string, object>> FetchPaymentMethods(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPaymentMethods(parameters);
        return ((Dictionary<string, object>)res);
    }
    public Dictionary<string, object> SetMarkets(object markets, object currencies = null)
    {
        var res = this.setMarkets(markets, currencies);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> FetchBorrowRate(string code, double amount, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchBorrowRate(code, amount, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<List<OHLCV>> FetchOHLCV(string symbol, string timeframe = "1m", Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOHLCV(symbol, timeframe, since, limit, parameters);
        return ((IList<object>)res).Select(item => new OHLCV(item)).ToList<OHLCV>();
    }
    public async Task<List<OHLCV>> FetchOHLCVWs(string symbol, string timeframe = "1m", Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOHLCVWs(symbol, timeframe, since, limit, parameters);
        return ((IList<object>)res).Select(item => new OHLCV(item)).ToList<OHLCV>();
    }
    public async Task<List<OHLCV>> WatchOHLCV(string symbol, string timeframe = "1m", Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchOHLCV(symbol, timeframe, since, limit, parameters);
        return ((IList<object>)res).Select(item => new OHLCV(item)).ToList<OHLCV>();
    }
    public async Task<Dictionary<string, object>> FetchWebEndpoint(object method, object endpointMethod, object returnAsJson, object startRegex = null, object endRegex = null)
    {
        var res = await this.fetchWebEndpoint(method, endpointMethod, returnAsJson, startRegex, endRegex);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> FetchL2OrderBook(string symbol, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchL2OrderBook(symbol, limit, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> Fetch2(object path, object api = null, string method = "GET", Dictionary<string, object> parameters = null, object headers = null, object body = null, Dictionary<string, object> config = null)
    {
        var res = await this.fetch2(path, api, method, parameters, headers, body, config);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Order> EditLimitBuyOrder(string id, string symbol, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.editLimitBuyOrder(id, symbol, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> EditLimitSellOrder(string id, string symbol, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.editLimitSellOrder(id, symbol, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> EditLimitOrder(string id, string symbol, string side, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.editLimitOrder(id, symbol, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> EditOrder(string id, string symbol, string type, string side, double? amount2 = 0, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var amount = amount2 == 0 ? null : (object)amount2;
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.editOrder(id, symbol, type, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> EditOrderWs(string id, string symbol, string type, string side, double? amount2 = 0, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var amount = amount2 == 0 ? null : (object)amount2;
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.editOrderWs(id, symbol, type, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Position> FetchPosition(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPosition(symbol, parameters);
        return new Position(res);
    }
    public async Task<List<Position>> FetchPositionWs(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPositionWs(symbol, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<Position> WatchPosition(string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.watchPosition(symbol, parameters);
        return new Position(res);
    }
    public async Task<List<Position>> WatchPositions(List<String> symbols = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchPositions(symbols, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<List<Position>> WatchPositionForSymbols(List<String> symbols = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchPositionForSymbols(symbols, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<List<Position>> FetchPositionsForSymbol(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPositionsForSymbol(symbol, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<List<Position>> FetchPositionsForSymbolWs(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPositionsForSymbolWs(symbol, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<List<Position>> FetchPositions(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPositions(symbols, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<List<Position>> FetchPositionsWs(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPositionsWs(symbols, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<List<Position>> FetchPositionsRisk(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPositionsRisk(symbols, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<Tickers> FetchBidsAsks(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchBidsAsks(symbols, parameters);
        return new Tickers(res);
    }
    public async Task<List<BorrowInterest>> FetchBorrowInterest(string code = null, string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchBorrowInterest(code, symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new BorrowInterest(item)).ToList<BorrowInterest>();
    }
    public async Task<List<LedgerEntry>> FetchLedger(string code = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchLedger(code, since, limit, parameters);
        return ((IList<object>)res).Select(item => new LedgerEntry(item)).ToList<LedgerEntry>();
    }
    public async Task<LedgerEntry> FetchLedgerEntry(string id, string code = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchLedgerEntry(id, code, parameters);
        return new LedgerEntry(res);
    }
    public async Task<Balances> FetchBalance(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchBalance(parameters);
        return new Balances(res);
    }
    public async Task<Balances> FetchBalanceWs(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchBalanceWs(parameters);
        return new Balances(res);
    }
    public async Task<Balances> WatchBalance(Dictionary<string, object> parameters = null)
    {
        var res = await this.watchBalance(parameters);
        return new Balances(res);
    }
    public async Task<Balance> FetchPartialBalance(object part, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPartialBalance(part, parameters);
        return new Balance(res);
    }
    public async Task<Balance> FetchFreeBalance(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchFreeBalance(parameters);
        return new Balance(res);
    }
    public async Task<Balance> FetchUsedBalance(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchUsedBalance(parameters);
        return new Balance(res);
    }
    public async Task<Balance> FetchTotalBalance(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTotalBalance(parameters);
        return new Balance(res);
    }
    public async Task<Dictionary<string, object>> FetchStatus(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchStatus(parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> FetchTransactionFee(string code, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTransactionFee(code, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> FetchTransactionFees(List<String> codes = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTransactionFees(codes, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, DepositWithdrawFeeNetwork>> FetchDepositWithdrawFees(List<String> codes = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchDepositWithdrawFees(codes, parameters);
        return ((Dictionary<string, DepositWithdrawFeeNetwork>)res);
    }
    public async Task<DepositWithdrawFeeNetwork> FetchDepositWithdrawFee(string code, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchDepositWithdrawFee(code, parameters);
        return new DepositWithdrawFeeNetwork(res);
    }
    public async Task<CrossBorrowRate> FetchCrossBorrowRate(string code, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchCrossBorrowRate(code, parameters);
        return new CrossBorrowRate(res);
    }
    public async Task<IsolatedBorrowRate> FetchIsolatedBorrowRate(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchIsolatedBorrowRate(symbol, parameters);
        return new IsolatedBorrowRate(res);
    }
    public async Task<Ticker> FetchTicker(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTicker(symbol, parameters);
        return new Ticker(res);
    }
    public async Task<Ticker> FetchMarkPrice(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchMarkPrice(symbol, parameters);
        return new Ticker(res);
    }
    public async Task<Ticker> FetchTickerWs(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTickerWs(symbol, parameters);
        return new Ticker(res);
    }
    public async Task<Ticker> WatchTicker(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.watchTicker(symbol, parameters);
        return new Ticker(res);
    }
    public async Task<Tickers> FetchTickers(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTickers(symbols, parameters);
        return new Tickers(res);
    }
    public async Task<Tickers> FetchMarkPrices(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchMarkPrices(symbols, parameters);
        return new Tickers(res);
    }
    public async Task<Tickers> FetchTickersWs(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTickersWs(symbols, parameters);
        return new Tickers(res);
    }
    public async Task<OrderBooks> FetchOrderBooks(List<String> symbols = null, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOrderBooks(symbols, limit, parameters);
        return new OrderBooks(res);
    }
    public async Task<Tickers> WatchBidsAsks(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.watchBidsAsks(symbols, parameters);
        return new Tickers(res);
    }
    public async Task<Tickers> WatchTickers(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.watchTickers(symbols, parameters);
        return new Tickers(res);
    }
    public async Task<Order> FetchOrder(string id, string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchOrder(id, symbol, parameters);
        return new Order(res);
    }
    public async Task<Order> FetchOrderWs(string id, string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchOrderWs(id, symbol, parameters);
        return new Order(res);
    }
    public async Task<string> FetchOrderStatus(string id, string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchOrderStatus(id, symbol, parameters);
        return ((string)res);
    }
    public async Task<Order> FetchUnifiedOrder(object order, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchUnifiedOrder(order, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateOrder(string symbol, string type, string side, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createOrder(symbol, type, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Conversion> CreateConvertTrade(string id, string fromCode, string toCode, double? amount2 = 0, Dictionary<string, object> parameters = null)
    {
        var amount = amount2 == 0 ? null : (object)amount2;
        var res = await this.createConvertTrade(id, fromCode, toCode, amount, parameters);
        return new Conversion(res);
    }
    public async Task<Conversion> FetchConvertTrade(string id, string code = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchConvertTrade(id, code, parameters);
        return new Conversion(res);
    }
    public async Task<List<Conversion>> FetchConvertTradeHistory(string code = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchConvertTradeHistory(code, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Conversion(item)).ToList<Conversion>();
    }
    public async Task<Dictionary<string, object>> FetchPositionMode(string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchPositionMode(symbol, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Order> CreateTrailingAmountOrder(string symbol, string type, string side, double amount, double? price2 = 0, object trailingAmount = null, object trailingTriggerPrice = null, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createTrailingAmountOrder(symbol, type, side, amount, price, trailingAmount, trailingTriggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateTrailingAmountOrderWs(string symbol, string type, string side, double amount, double? price2 = 0, object trailingAmount = null, object trailingTriggerPrice = null, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createTrailingAmountOrderWs(symbol, type, side, amount, price, trailingAmount, trailingTriggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateTrailingPercentOrder(string symbol, string type, string side, double amount, double? price2 = 0, object trailingPercent = null, object trailingTriggerPrice = null, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createTrailingPercentOrder(symbol, type, side, amount, price, trailingPercent, trailingTriggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateTrailingPercentOrderWs(string symbol, string type, string side, double amount, double? price2 = 0, object trailingPercent = null, object trailingTriggerPrice = null, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createTrailingPercentOrderWs(symbol, type, side, amount, price, trailingPercent, trailingTriggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketOrderWithCost(string symbol, string side, double cost, Dictionary<string, object> parameters = null)
    {
        var res = await this.createMarketOrderWithCost(symbol, side, cost, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketBuyOrderWithCost(string symbol, double cost, Dictionary<string, object> parameters = null)
    {
        var res = await this.createMarketBuyOrderWithCost(symbol, cost, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketSellOrderWithCost(string symbol, double cost, Dictionary<string, object> parameters = null)
    {
        var res = await this.createMarketSellOrderWithCost(symbol, cost, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketOrderWithCostWs(string symbol, string side, double cost, Dictionary<string, object> parameters = null)
    {
        var res = await this.createMarketOrderWithCostWs(symbol, side, cost, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateTriggerOrder(string symbol, string type, string side, double amount, double? price2 = 0, double? triggerPrice2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var triggerPrice = triggerPrice2 == 0 ? null : (object)triggerPrice2;
        var res = await this.createTriggerOrder(symbol, type, side, amount, price, triggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateTriggerOrderWs(string symbol, string type, string side, double amount, double? price2 = 0, double? triggerPrice2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var triggerPrice = triggerPrice2 == 0 ? null : (object)triggerPrice2;
        var res = await this.createTriggerOrderWs(symbol, type, side, amount, price, triggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateStopLossOrder(string symbol, string type, string side, double amount, double? price2 = 0, double? stopLossPrice2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var stopLossPrice = stopLossPrice2 == 0 ? null : (object)stopLossPrice2;
        var res = await this.createStopLossOrder(symbol, type, side, amount, price, stopLossPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateStopLossOrderWs(string symbol, string type, string side, double amount, double? price2 = 0, double? stopLossPrice2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var stopLossPrice = stopLossPrice2 == 0 ? null : (object)stopLossPrice2;
        var res = await this.createStopLossOrderWs(symbol, type, side, amount, price, stopLossPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateTakeProfitOrder(string symbol, string type, string side, double amount, double? price2 = 0, double? takeProfitPrice2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var takeProfitPrice = takeProfitPrice2 == 0 ? null : (object)takeProfitPrice2;
        var res = await this.createTakeProfitOrder(symbol, type, side, amount, price, takeProfitPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateTakeProfitOrderWs(string symbol, string type, string side, double amount, double? price2 = 0, double? takeProfitPrice2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var takeProfitPrice = takeProfitPrice2 == 0 ? null : (object)takeProfitPrice2;
        var res = await this.createTakeProfitOrderWs(symbol, type, side, amount, price, takeProfitPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateOrderWithTakeProfitAndStopLoss(string symbol, string type, string side, double amount, double? price2 = 0, double? takeProfit2 = 0, double? stopLoss2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var takeProfit = takeProfit2 == 0 ? null : (object)takeProfit2;
        var stopLoss = stopLoss2 == 0 ? null : (object)stopLoss2;
        var res = await this.createOrderWithTakeProfitAndStopLoss(symbol, type, side, amount, price, takeProfit, stopLoss, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateOrderWithTakeProfitAndStopLossWs(string symbol, string type, string side, double amount, double? price2 = 0, double? takeProfit2 = 0, double? stopLoss2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var takeProfit = takeProfit2 == 0 ? null : (object)takeProfit2;
        var stopLoss = stopLoss2 == 0 ? null : (object)stopLoss2;
        var res = await this.createOrderWithTakeProfitAndStopLossWs(symbol, type, side, amount, price, takeProfit, stopLoss, parameters);
        return new Order(res);
    }
    public async Task<List<Order>> CreateOrders(List<OrderRequest> orders, Dictionary<string, object> parameters = null)
    {
        var res = await this.createOrders(orders, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<List<Order>> EditOrders(List<OrderRequest> orders, Dictionary<string, object> parameters = null)
    {
        var res = await this.editOrders(orders, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<Order> CreateOrderWs(string symbol, string type, string side, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createOrderWs(symbol, type, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Dictionary<string, object>> CancelOrder(string id, string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.cancelOrder(id, symbol, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> CancelOrderWs(string id, string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.cancelOrderWs(id, symbol, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> CancelOrdersWs(List<string> ids, string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.cancelOrdersWs(ids, symbol, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> CancelAllOrders(string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.cancelAllOrders(symbol, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> CancelAllOrdersAfter(Int64 timeout, Dictionary<string, object> parameters = null)
    {
        var res = await this.cancelAllOrdersAfter(timeout, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> CancelOrdersForSymbols(List<CancellationRequest> orders, Dictionary<string, object> parameters = null)
    {
        var res = await this.cancelOrdersForSymbols(orders, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> CancelAllOrdersWs(string symbol = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.cancelAllOrdersWs(symbol, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> CancelUnifiedOrder(object order, Dictionary<string, object> parameters = null)
    {
        var res = await this.cancelUnifiedOrder(order, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<List<Order>> FetchOrders(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOrders(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<List<Order>> FetchOrdersWs(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOrdersWs(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<List<Trade>> FetchOrderTrades(string id, string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOrderTrades(id, symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Trade(item)).ToList<Trade>();
    }
    public async Task<List<Order>> WatchOrders(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchOrders(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<List<Order>> FetchOpenOrders(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOpenOrders(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<List<Order>> FetchOpenOrdersWs(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchOpenOrdersWs(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<List<Order>> FetchClosedOrders(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchClosedOrders(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<List<Order>> FetchCanceledAndClosedOrders(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchCanceledAndClosedOrders(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<List<Order>> FetchClosedOrdersWs(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchClosedOrdersWs(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Order(item)).ToList<Order>();
    }
    public async Task<List<Trade>> FetchMyTrades(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchMyTrades(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Trade(item)).ToList<Trade>();
    }
    public async Task<List<Liquidation>> FetchMyLiquidations(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchMyLiquidations(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Liquidation(item)).ToList<Liquidation>();
    }
    public async Task<List<Liquidation>> FetchLiquidations(string symbol, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchLiquidations(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Liquidation(item)).ToList<Liquidation>();
    }
    public async Task<List<Trade>> FetchMyTradesWs(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchMyTradesWs(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Trade(item)).ToList<Trade>();
    }
    public async Task<List<Trade>> WatchMyTrades(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.watchMyTrades(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Trade(item)).ToList<Trade>();
    }
    public async Task<Greeks> FetchGreeks(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchGreeks(symbol, parameters);
        return new Greeks(res);
    }
    public async Task<List<Greeks>> FetchAllGreeks(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchAllGreeks(symbols, parameters);
        return ((IList<object>)res).Select(item => new Greeks(item)).ToList<Greeks>();
    }
    public async Task<OptionChain> FetchOptionChain(string code, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchOptionChain(code, parameters);
        return new OptionChain(res);
    }
    public async Task<Option> FetchOption(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchOption(symbol, parameters);
        return new Option(res);
    }
    public async Task<Conversion> FetchConvertQuote(string fromCode, string toCode, double? amount2 = 0, Dictionary<string, object> parameters = null)
    {
        var amount = amount2 == 0 ? null : (object)amount2;
        var res = await this.fetchConvertQuote(fromCode, toCode, amount, parameters);
        return new Conversion(res);
    }
    public async Task<List<Transaction>> FetchDepositsWithdrawals(string code = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchDepositsWithdrawals(code, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Transaction(item)).ToList<Transaction>();
    }
    public async Task<List<Transaction>> FetchDeposits(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchDeposits(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Transaction(item)).ToList<Transaction>();
    }
    public async Task<List<Transaction>> FetchWithdrawals(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchWithdrawals(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Transaction(item)).ToList<Transaction>();
    }
    public async Task<Dictionary<string, object>> FetchDepositsWs(string code = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchDepositsWs(code, since, limit, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> FetchWithdrawalsWs(string code = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchWithdrawalsWs(code, since, limit, parameters);
        return ((Dictionary<string, object>)res);
    }
    public async Task<List<FundingRateHistory>> FetchFundingRateHistory(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchFundingRateHistory(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new FundingRateHistory(item)).ToList<FundingRateHistory>();
    }
    public async Task<List<FundingHistory>> FetchFundingHistory(string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchFundingHistory(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new FundingHistory(item)).ToList<FundingHistory>();
    }
    public async Task<OrderBook> FetchL3OrderBook(string symbol, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchL3OrderBook(symbol, limit, parameters);
        return new OrderBook(res);
    }
    public async Task<DepositAddress> FetchDepositAddress(string code, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchDepositAddress(code, parameters);
        return new DepositAddress(res);
    }
    public MarketInterface CreateExpiredOptionMarket(string symbol)
    {
        var res = this.createExpiredOptionMarket(symbol);
        return new MarketInterface(res);
    }
    public async Task<Order> CreateLimitOrder(string symbol, string side, double amount, double price, Dictionary<string, object> parameters = null)
    {
        var res = await this.createLimitOrder(symbol, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateLimitOrderWs(string symbol, string side, double amount, double price, Dictionary<string, object> parameters = null)
    {
        var res = await this.createLimitOrderWs(symbol, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketOrder(string symbol, string side, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createMarketOrder(symbol, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketOrderWs(string symbol, string side, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createMarketOrderWs(symbol, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateLimitBuyOrder(string symbol, double amount, double price, Dictionary<string, object> parameters = null)
    {
        var res = await this.createLimitBuyOrder(symbol, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateLimitBuyOrderWs(string symbol, double amount, double price, Dictionary<string, object> parameters = null)
    {
        var res = await this.createLimitBuyOrderWs(symbol, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateLimitSellOrder(string symbol, double amount, double price, Dictionary<string, object> parameters = null)
    {
        var res = await this.createLimitSellOrder(symbol, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateLimitSellOrderWs(string symbol, double amount, double price, Dictionary<string, object> parameters = null)
    {
        var res = await this.createLimitSellOrderWs(symbol, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketBuyOrder(string symbol, double amount, Dictionary<string, object> parameters = null)
    {
        var res = await this.createMarketBuyOrder(symbol, amount, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketBuyOrderWs(string symbol, double amount, Dictionary<string, object> parameters = null)
    {
        var res = await this.createMarketBuyOrderWs(symbol, amount, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketSellOrder(string symbol, double amount, Dictionary<string, object> parameters = null)
    {
        var res = await this.createMarketSellOrder(symbol, amount, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateMarketSellOrderWs(string symbol, double amount, Dictionary<string, object> parameters = null)
    {
        var res = await this.createMarketSellOrderWs(symbol, amount, parameters);
        return new Order(res);
    }
    public async Task<List<LeverageTier>> FetchMarketLeverageTiers(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchMarketLeverageTiers(symbol, parameters);
        return ((IList<object>)res).Select(item => new LeverageTier(item)).ToList<LeverageTier>();
    }
    public async Task<Order> CreatePostOnlyOrder(string symbol, string type, string side, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createPostOnlyOrder(symbol, type, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreatePostOnlyOrderWs(string symbol, string type, string side, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createPostOnlyOrderWs(symbol, type, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateReduceOnlyOrder(string symbol, string type, string side, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createReduceOnlyOrder(symbol, type, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateReduceOnlyOrderWs(string symbol, string type, string side, double amount, double? price2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var res = await this.createReduceOnlyOrderWs(symbol, type, side, amount, price, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateStopOrder(string symbol, string type, string side, double amount, double? price2 = 0, double? triggerPrice2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var triggerPrice = triggerPrice2 == 0 ? null : (object)triggerPrice2;
        var res = await this.createStopOrder(symbol, type, side, amount, price, triggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateStopOrderWs(string symbol, string type, string side, double amount, double? price2 = 0, double? triggerPrice2 = 0, Dictionary<string, object> parameters = null)
    {
        var price = price2 == 0 ? null : (object)price2;
        var triggerPrice = triggerPrice2 == 0 ? null : (object)triggerPrice2;
        var res = await this.createStopOrderWs(symbol, type, side, amount, price, triggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateStopLimitOrder(string symbol, string side, double amount, double price, double triggerPrice, Dictionary<string, object> parameters = null)
    {
        var res = await this.createStopLimitOrder(symbol, side, amount, price, triggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateStopLimitOrderWs(string symbol, string side, double amount, double price, double triggerPrice, Dictionary<string, object> parameters = null)
    {
        var res = await this.createStopLimitOrderWs(symbol, side, amount, price, triggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateStopMarketOrder(string symbol, string side, double amount, double triggerPrice, Dictionary<string, object> parameters = null)
    {
        var res = await this.createStopMarketOrder(symbol, side, amount, triggerPrice, parameters);
        return new Order(res);
    }
    public async Task<Order> CreateStopMarketOrderWs(string symbol, string side, double amount, double triggerPrice, Dictionary<string, object> parameters = null)
    {
        var res = await this.createStopMarketOrderWs(symbol, side, amount, triggerPrice, parameters);
        return new Order(res);
    }
    public async Task<LastPrices> FetchLastPrices(List<String> symbols = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchLastPrices(symbols, parameters);
        return new LastPrices(res);
    }
    public async Task<TradingFees> FetchTradingFees(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTradingFees(parameters);
        return new TradingFees(res);
    }
    public async Task<TradingFees> FetchTradingFeesWs(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTradingFeesWs(parameters);
        return new TradingFees(res);
    }
    public async Task<TradingFeeInterface> FetchTradingFee(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTradingFee(symbol, parameters);
        return new TradingFeeInterface(res);
    }
    public async Task<Currencies> FetchConvertCurrencies(Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchConvertCurrencies(parameters);
        return new Currencies(res);
    }
    public async Task<FundingRate> FetchFundingRate(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchFundingRate(symbol, parameters);
        return new FundingRate(res);
    }
    public async Task<FundingRate> FetchFundingInterval(string symbol, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchFundingInterval(symbol, parameters);
        return new FundingRate(res);
    }
    public async Task<List<OHLCV>> FetchMarkOHLCV(object symbol, string timeframe = "1m", Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchMarkOHLCV(symbol, timeframe, since, limit, parameters);
        return ((IList<object>)res).Select(item => new OHLCV(item)).ToList<OHLCV>();
    }
    public async Task<List<OHLCV>> FetchIndexOHLCV(string symbol, string timeframe = "1m", Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchIndexOHLCV(symbol, timeframe, since, limit, parameters);
        return ((IList<object>)res).Select(item => new OHLCV(item)).ToList<OHLCV>();
    }
    public async Task<List<OHLCV>> FetchPremiumIndexOHLCV(string symbol, string timeframe = "1m", Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchPremiumIndexOHLCV(symbol, timeframe, since, limit, parameters);
        return ((IList<object>)res).Select(item => new OHLCV(item)).ToList<OHLCV>();
    }
    public async Task<List<Transaction>> FetchTransactions(string code = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchTransactions(code, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Transaction(item)).ToList<Transaction>();
    }
    public Dictionary<string, Dictionary<string, OHLCV[]>> CreateOHLCVObject(string symbol, string timeframe, object data)
    {
        var res = this.createOHLCVObject(symbol, timeframe, data);
        return ((Dictionary<string, Dictionary<string, OHLCV[]>>)res);
    }
    public async Task<Dictionary<string, object>> FetchPaginatedCallDynamic(string method, string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null, Int64? maxEntriesPerRequest2 = 0, bool removeRepeated = true)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var maxEntriesPerRequest = maxEntriesPerRequest2 == 0 ? null : (object)maxEntriesPerRequest2;
        var res = await this.fetchPaginatedCallDynamic(method, symbol, since, limit, parameters, maxEntriesPerRequest, removeRepeated);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> FetchPaginatedCallDeterministic(string method, string symbol = null, Int64? since2 = 0, Int64? limit2 = 0, string timeframe = null, Dictionary<string, object> parameters = null, object maxEntriesPerRequest = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchPaginatedCallDeterministic(method, symbol, since, limit, timeframe, parameters, maxEntriesPerRequest);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> FetchPaginatedCallCursor(string method, string symbol = null, object since = null, object limit = null, Dictionary<string, object> parameters = null, object cursorReceived = null, object cursorSent = null, object cursorIncrement = null, object maxEntriesPerRequest = null)
    {
        var res = await this.fetchPaginatedCallCursor(method, symbol, since, limit, parameters, cursorReceived, cursorSent, cursorIncrement, maxEntriesPerRequest);
        return ((Dictionary<string, object>)res);
    }
    public async Task<Dictionary<string, object>> FetchPaginatedCallIncremental(string method, string symbol = null, object since = null, object limit = null, Dictionary<string, object> parameters = null, object pageKey = null, object maxEntriesPerRequest = null)
    {
        var res = await this.fetchPaginatedCallIncremental(method, symbol, since, limit, parameters, pageKey, maxEntriesPerRequest);
        return ((Dictionary<string, object>)res);
    }
    public async Task<List<Position>> FetchPositionHistory(string symbol, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchPositionHistory(symbol, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<List<Position>> FetchPositionsHistory(List<String> symbols = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchPositionsHistory(symbols, since, limit, parameters);
        return ((IList<object>)res).Select(item => new Position(item)).ToList<Position>();
    }
    public async Task<TransferEntry> FetchTransfer(string id, string code = null, Dictionary<string, object> parameters = null)
    {
        var res = await this.fetchTransfer(id, code, parameters);
        return new TransferEntry(res);
    }
    public async Task<List<TransferEntry>> FetchTransfers(string code = null, Int64? since2 = 0, Int64? limit2 = 0, Dictionary<string, object> parameters = null)
    {
        var since = since2 == 0 ? null : (object)since2;
        var limit = limit2 == 0 ? null : (object)limit2;
        var res = await this.fetchTransfers(code, since, limit, parameters);
        return ((IList<object>)res).Select(item => new TransferEntry(item)).ToList<TransferEntry>();
    }
}
// class wrappers
public class  Alpaca: alpaca { public Alpaca(object args = null) : base(args) { } }
public class  Apex: apex { public Apex(object args = null) : base(args) { } }
public class  Ascendex: ascendex { public Ascendex(object args = null) : base(args) { } }
public class  Bequant: bequant { public Bequant(object args = null) : base(args) { } }
public class  Bigone: bigone { public Bigone(object args = null) : base(args) { } }
public class  Binance: binance { public Binance(object args = null) : base(args) { } }
public class  Binancecoinm: binancecoinm { public Binancecoinm(object args = null) : base(args) { } }
public class  Binanceus: binanceus { public Binanceus(object args = null) : base(args) { } }
public class  Binanceusdm: binanceusdm { public Binanceusdm(object args = null) : base(args) { } }
public class  Bingx: bingx { public Bingx(object args = null) : base(args) { } }
public class  Bit2c: bit2c { public Bit2c(object args = null) : base(args) { } }
public class  Bitbank: bitbank { public Bitbank(object args = null) : base(args) { } }
public class  Bitbns: bitbns { public Bitbns(object args = null) : base(args) { } }
public class  Bitfinex: bitfinex { public Bitfinex(object args = null) : base(args) { } }
public class  Bitflyer: bitflyer { public Bitflyer(object args = null) : base(args) { } }
public class  Bitget: bitget { public Bitget(object args = null) : base(args) { } }
public class  Bithumb: bithumb { public Bithumb(object args = null) : base(args) { } }
public class  Bitmart: bitmart { public Bitmart(object args = null) : base(args) { } }
public class  Bitmex: bitmex { public Bitmex(object args = null) : base(args) { } }
public class  Bitopro: bitopro { public Bitopro(object args = null) : base(args) { } }
public class  Bitrue: bitrue { public Bitrue(object args = null) : base(args) { } }
public class  Bitso: bitso { public Bitso(object args = null) : base(args) { } }
public class  Bitstamp: bitstamp { public Bitstamp(object args = null) : base(args) { } }
public class  Bitteam: bitteam { public Bitteam(object args = null) : base(args) { } }
public class  Bittrade: bittrade { public Bittrade(object args = null) : base(args) { } }
public class  Bitvavo: bitvavo { public Bitvavo(object args = null) : base(args) { } }
public class  Blockchaincom: blockchaincom { public Blockchaincom(object args = null) : base(args) { } }
public class  Blofin: blofin { public Blofin(object args = null) : base(args) { } }
public class  Btcalpha: btcalpha { public Btcalpha(object args = null) : base(args) { } }
public class  Btcbox: btcbox { public Btcbox(object args = null) : base(args) { } }
public class  Btcmarkets: btcmarkets { public Btcmarkets(object args = null) : base(args) { } }
public class  Btcturk: btcturk { public Btcturk(object args = null) : base(args) { } }
public class  Bybit: bybit { public Bybit(object args = null) : base(args) { } }
public class  Cex: cex { public Cex(object args = null) : base(args) { } }
public class  Coinbase: coinbase { public Coinbase(object args = null) : base(args) { } }
public class  Coinbaseadvanced: coinbaseadvanced { public Coinbaseadvanced(object args = null) : base(args) { } }
public class  Coinbaseexchange: coinbaseexchange { public Coinbaseexchange(object args = null) : base(args) { } }
public class  Coinbaseinternational: coinbaseinternational { public Coinbaseinternational(object args = null) : base(args) { } }
public class  Coincatch: coincatch { public Coincatch(object args = null) : base(args) { } }
public class  Coincheck: coincheck { public Coincheck(object args = null) : base(args) { } }
public class  Coinex: coinex { public Coinex(object args = null) : base(args) { } }
public class  Coinmate: coinmate { public Coinmate(object args = null) : base(args) { } }
public class  Coinmetro: coinmetro { public Coinmetro(object args = null) : base(args) { } }
public class  Coinone: coinone { public Coinone(object args = null) : base(args) { } }
public class  Coinsph: coinsph { public Coinsph(object args = null) : base(args) { } }
public class  Coinspot: coinspot { public Coinspot(object args = null) : base(args) { } }
public class  Cryptocom: cryptocom { public Cryptocom(object args = null) : base(args) { } }
public class  Cryptomus: cryptomus { public Cryptomus(object args = null) : base(args) { } }
public class  Defx: defx { public Defx(object args = null) : base(args) { } }
public class  Delta: delta { public Delta(object args = null) : base(args) { } }
public class  Deribit: deribit { public Deribit(object args = null) : base(args) { } }
public class  Derive: derive { public Derive(object args = null) : base(args) { } }
public class  Digifinex: digifinex { public Digifinex(object args = null) : base(args) { } }
public class  Ellipx: ellipx { public Ellipx(object args = null) : base(args) { } }
public class  Exmo: exmo { public Exmo(object args = null) : base(args) { } }
public class  Fmfwio: fmfwio { public Fmfwio(object args = null) : base(args) { } }
public class  Gate: gate { public Gate(object args = null) : base(args) { } }
public class  Gateio: gateio { public Gateio(object args = null) : base(args) { } }
public class  Gemini: gemini { public Gemini(object args = null) : base(args) { } }
public class  Hashkey: hashkey { public Hashkey(object args = null) : base(args) { } }
public class  Hitbtc: hitbtc { public Hitbtc(object args = null) : base(args) { } }
public class  Hollaex: hollaex { public Hollaex(object args = null) : base(args) { } }
public class  Htx: htx { public Htx(object args = null) : base(args) { } }
public class  Huobi: huobi { public Huobi(object args = null) : base(args) { } }
public class  Hyperliquid: hyperliquid { public Hyperliquid(object args = null) : base(args) { } }
public class  Independentreserve: independentreserve { public Independentreserve(object args = null) : base(args) { } }
public class  Indodax: indodax { public Indodax(object args = null) : base(args) { } }
public class  Kraken: kraken { public Kraken(object args = null) : base(args) { } }
public class  Krakenfutures: krakenfutures { public Krakenfutures(object args = null) : base(args) { } }
public class  Kucoin: kucoin { public Kucoin(object args = null) : base(args) { } }
public class  Kucoinfutures: kucoinfutures { public Kucoinfutures(object args = null) : base(args) { } }
public class  Latoken: latoken { public Latoken(object args = null) : base(args) { } }
public class  Lbank: lbank { public Lbank(object args = null) : base(args) { } }
public class  Luno: luno { public Luno(object args = null) : base(args) { } }
public class  Mercado: mercado { public Mercado(object args = null) : base(args) { } }
public class  Mexc: mexc { public Mexc(object args = null) : base(args) { } }
public class  Modetrade: modetrade { public Modetrade(object args = null) : base(args) { } }
public class  Myokx: myokx { public Myokx(object args = null) : base(args) { } }
public class  Ndax: ndax { public Ndax(object args = null) : base(args) { } }
public class  Novadax: novadax { public Novadax(object args = null) : base(args) { } }
public class  Oceanex: oceanex { public Oceanex(object args = null) : base(args) { } }
public class  Okcoin: okcoin { public Okcoin(object args = null) : base(args) { } }
public class  Okx: okx { public Okx(object args = null) : base(args) { } }
public class  Okxus: okxus { public Okxus(object args = null) : base(args) { } }
public class  Onetrading: onetrading { public Onetrading(object args = null) : base(args) { } }
public class  Oxfun: oxfun { public Oxfun(object args = null) : base(args) { } }
public class  P2b: p2b { public P2b(object args = null) : base(args) { } }
public class  Paradex: paradex { public Paradex(object args = null) : base(args) { } }
public class  Paymium: paymium { public Paymium(object args = null) : base(args) { } }
public class  Phemex: phemex { public Phemex(object args = null) : base(args) { } }
public class  Poloniex: poloniex { public Poloniex(object args = null) : base(args) { } }
public class  Probit: probit { public Probit(object args = null) : base(args) { } }
public class  Timex: timex { public Timex(object args = null) : base(args) { } }
public class  Tokocrypto: tokocrypto { public Tokocrypto(object args = null) : base(args) { } }
public class  Tradeogre: tradeogre { public Tradeogre(object args = null) : base(args) { } }
public class  Upbit: upbit { public Upbit(object args = null) : base(args) { } }
public class  Vertex: vertex { public Vertex(object args = null) : base(args) { } }
public class  Wavesexchange: wavesexchange { public Wavesexchange(object args = null) : base(args) { } }
public class  Whitebit: whitebit { public Whitebit(object args = null) : base(args) { } }
public class  Woo: woo { public Woo(object args = null) : base(args) { } }
public class  Woofipro: woofipro { public Woofipro(object args = null) : base(args) { } }
public class  Xt: xt { public Xt(object args = null) : base(args) { } }
public class  Yobit: yobit { public Yobit(object args = null) : base(args) { } }
public class  Zaif: zaif { public Zaif(object args = null) : base(args) { } }
public class  Zonda: zonda { public Zonda(object args = null) : base(args) { } }