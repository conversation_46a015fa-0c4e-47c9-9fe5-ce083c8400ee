// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class btcbox : Exchange
{
    public btcbox (object args = null): base(args) {}

    public async Task<object> publicGetDepth (object parameters = null)
    {
        return await this.callAsync ("publicGetDepth",parameters);
    }

    public async Task<object> publicGetOrders (object parameters = null)
    {
        return await this.callAsync ("publicGetOrders",parameters);
    }

    public async Task<object> publicGetTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetTicker",parameters);
    }

    public async Task<object> publicGetTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetTickers",parameters);
    }

    public async Task<object> privatePostBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostBalance",parameters);
    }

    public async Task<object> privatePostTradeAdd (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeAdd",parameters);
    }

    public async Task<object> privatePostTradeCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancel",parameters);
    }

    public async Task<object> privatePostTradeList (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeList",parameters);
    }

    public async Task<object> privatePostTradeView (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeView",parameters);
    }

    public async Task<object> privatePostWallet (object parameters = null)
    {
        return await this.callAsync ("privatePostWallet",parameters);
    }

    public async Task<object> webApiGetAjaxCoinCoinInfo (object parameters = null)
    {
        return await this.callAsync ("webApiGetAjaxCoinCoinInfo",parameters);
    }

}