#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
币安数据获取演示
使用模拟数据演示数据获取和处理功能
"""

import json
import pandas as pd
from datetime import datetime, timedelta
import random
import os

class BinanceDataDemo:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def generate_mock_markets(self):
        """生成模拟市场数据"""
        base_currencies = ['BTC', 'ETH', 'BNB', 'ADA', 'XRP', 'SOL', 'DOT', 'DOGE', 'AVAX', 'MATIC', 
                          'LINK', 'UNI', 'LTC', 'BCH', 'FIL', 'TRX', 'ETC', 'XLM', 'VET', 'ICP']
        quote_currencies = ['USDT', 'BTC', 'ETH', 'BNB']
        
        markets = {}
        for base in base_currencies:
            for quote in quote_currencies:
                if base != quote:
                    symbol = f"{base}/{quote}"
                    markets[symbol] = {
                        'id': f"{base.lower()}{quote.lower()}",
                        'symbol': symbol,
                        'base': base,
                        'quote': quote,
                        'active': True,
                        'type': 'spot',
                        'spot': True,
                        'margin': random.choice([True, False]),
                        'future': False,
                        'option': False,
                        'contract': False,
                        'precision': {
                            'amount': random.randint(2, 8),
                            'price': random.randint(2, 8)
                        },
                        'limits': {
                            'amount': {'min': 0.001, 'max': 1000000},
                            'price': {'min': 0.0001, 'max': 1000000},
                            'cost': {'min': 10, 'max': 1000000}
                        }
                    }
        
        return markets
    
    def generate_mock_tickers(self, markets):
        """生成模拟行情数据"""
        tickers = {}
        base_prices = {
            'BTC': 45000, 'ETH': 3000, 'BNB': 300, 'ADA': 0.5, 'XRP': 0.6,
            'SOL': 100, 'DOT': 25, 'DOGE': 0.08, 'AVAX': 35, 'MATIC': 1.2,
            'LINK': 15, 'UNI': 8, 'LTC': 150, 'BCH': 250, 'FIL': 6,
            'TRX': 0.1, 'ETC': 30, 'XLM': 0.12, 'VET': 0.03, 'ICP': 12
        }
        
        for symbol, market in markets.items():
            base = market['base']
            quote = market['quote']
            
            # 计算价格
            if quote == 'USDT':
                base_price = base_prices.get(base, random.uniform(0.1, 100))
            elif quote == 'BTC':
                base_price = base_prices.get(base, random.uniform(0.1, 100)) / base_prices['BTC']
            elif quote == 'ETH':
                base_price = base_prices.get(base, random.uniform(0.1, 100)) / base_prices['ETH']
            else:
                base_price = random.uniform(0.001, 10)
            
            # 生成价格波动
            change_percent = random.uniform(-10, 10)
            current_price = base_price * (1 + change_percent / 100)
            high_price = current_price * random.uniform(1.01, 1.05)
            low_price = current_price * random.uniform(0.95, 0.99)
            
            volume = random.uniform(1000, 1000000)
            quote_volume = volume * current_price
            
            tickers[symbol] = {
                'symbol': symbol,
                'timestamp': int(datetime.now().timestamp() * 1000),
                'datetime': datetime.now().isoformat(),
                'high': high_price,
                'low': low_price,
                'bid': current_price * 0.999,
                'bidVolume': random.uniform(10, 1000),
                'ask': current_price * 1.001,
                'askVolume': random.uniform(10, 1000),
                'vwap': current_price,
                'open': base_price,
                'close': current_price,
                'last': current_price,
                'previousClose': base_price,
                'change': current_price - base_price,
                'percentage': change_percent,
                'average': (current_price + base_price) / 2,
                'baseVolume': volume,
                'quoteVolume': quote_volume
            }
        
        return tickers
    
    def generate_mock_orderbook(self, symbol, price):
        """生成模拟订单簿"""
        bids = []
        asks = []
        
        # 生成买单
        for i in range(10):
            bid_price = price * (1 - (i + 1) * 0.001)
            bid_amount = random.uniform(0.1, 10)
            bids.append([bid_price, bid_amount])
        
        # 生成卖单
        for i in range(10):
            ask_price = price * (1 + (i + 1) * 0.001)
            ask_amount = random.uniform(0.1, 10)
            asks.append([ask_price, ask_amount])
        
        return {
            'symbol': symbol,
            'bids': bids,
            'asks': asks,
            'timestamp': int(datetime.now().timestamp() * 1000),
            'datetime': datetime.now().isoformat()
        }
    
    def generate_mock_klines(self, symbol, timeframe='1h', periods=24):
        """生成模拟K线数据"""
        klines = []
        base_price = 45000 if 'BTC' in symbol else 3000
        
        for i in range(periods):
            if timeframe == '1h':
                timestamp = int((datetime.now() - timedelta(hours=periods-i)).timestamp() * 1000)
            else:  # 1d
                timestamp = int((datetime.now() - timedelta(days=periods-i)).timestamp() * 1000)
            
            # 生成OHLCV数据
            open_price = base_price * random.uniform(0.98, 1.02)
            close_price = open_price * random.uniform(0.95, 1.05)
            high_price = max(open_price, close_price) * random.uniform(1.001, 1.02)
            low_price = min(open_price, close_price) * random.uniform(0.98, 0.999)
            volume = random.uniform(100, 10000)
            
            klines.append([timestamp, open_price, high_price, low_price, close_price, volume])
        
        return klines
    
    def save_data_to_files(self, data):
        """保存数据到文件"""
        print("💾 保存数据到文件...")
        
        # 保存市场数据
        with open(f'demo_markets_{self.timestamp}.json', 'w', encoding='utf-8') as f:
            json.dump(data['markets'], f, indent=2, ensure_ascii=False, default=str)
        
        # 保存行情数据到CSV
        ticker_data = []
        for symbol, ticker in data['tickers'].items():
            if symbol.endswith('/USDT'):  # 只保存USDT交易对
                ticker_data.append({
                    'symbol': symbol,
                    'price': ticker['last'],
                    'high_24h': ticker['high'],
                    'low_24h': ticker['low'],
                    'volume_24h': ticker['baseVolume'],
                    'volume_24h_usdt': ticker['quoteVolume'],
                    'change_24h': ticker['change'],
                    'change_24h_percent': ticker['percentage'],
                    'timestamp': ticker['timestamp'],
                    'datetime': ticker['datetime']
                })
        
        df_tickers = pd.DataFrame(ticker_data)
        df_tickers.to_csv(f'demo_tickers_{self.timestamp}.csv', index=False, encoding='utf-8')
        
        # 保存订单簿数据
        orderbook_data = []
        for symbol, orderbook in data['orderbooks'].items():
            # 买单
            for i, (price, amount) in enumerate(orderbook['bids'][:5]):
                orderbook_data.append({
                    'symbol': symbol,
                    'side': 'bid',
                    'level': i + 1,
                    'price': price,
                    'amount': amount,
                    'timestamp': orderbook['timestamp']
                })
            
            # 卖单
            for i, (price, amount) in enumerate(orderbook['asks'][:5]):
                orderbook_data.append({
                    'symbol': symbol,
                    'side': 'ask',
                    'level': i + 1,
                    'price': price,
                    'amount': amount,
                    'timestamp': orderbook['timestamp']
                })
        
        df_orderbooks = pd.DataFrame(orderbook_data)
        df_orderbooks.to_csv(f'demo_orderbooks_{self.timestamp}.csv', index=False, encoding='utf-8')
        
        # 保存K线数据
        kline_data = []
        for timeframe, klines in data['klines'].items():
            for ohlcv in klines:
                kline_data.append({
                    'timestamp': ohlcv[0],
                    'datetime': datetime.fromtimestamp(ohlcv[0] / 1000).isoformat(),
                    'open': ohlcv[1],
                    'high': ohlcv[2],
                    'low': ohlcv[3],
                    'close': ohlcv[4],
                    'volume': ohlcv[5],
                    'timeframe': timeframe
                })
        
        df_klines = pd.DataFrame(kline_data)
        df_klines.to_csv(f'demo_btc_klines_{self.timestamp}.csv', index=False, encoding='utf-8')
        
        print(f"✅ 数据已保存:")
        print(f"  - demo_markets_{self.timestamp}.json")
        print(f"  - demo_tickers_{self.timestamp}.csv")
        print(f"  - demo_orderbooks_{self.timestamp}.csv")
        print(f"  - demo_btc_klines_{self.timestamp}.csv")
    
    def run_demo(self):
        """运行演示"""
        print("🎭 币安数据获取演示 (使用模拟数据)")
        print("=" * 60)
        
        # 1. 生成市场数据
        print("📊 生成市场数据...")
        markets = self.generate_mock_markets()
        print(f"✅ 生成了 {len(markets)} 个交易对")
        
        # 2. 生成行情数据
        print("📈 生成行情数据...")
        tickers = self.generate_mock_tickers(markets)
        print(f"✅ 生成了 {len(tickers)} 个币种行情")
        
        # 3. 筛选USDT交易对并排序
        usdt_tickers = {k: v for k, v in tickers.items() if k.endswith('/USDT')}
        top_usdt = sorted(usdt_tickers.items(), key=lambda x: x[1]['quoteVolume'], reverse=True)[:10]
        
        print(f"\n📊 交易量最大的10个USDT交易对:")
        for i, (symbol, ticker) in enumerate(top_usdt, 1):
            volume = ticker['quoteVolume']
            price = ticker['last']
            change = ticker['percentage']
            print(f"  {i:2d}. {symbol:12s} 价格: ${price:>10.4f} 24h量: ${volume:>15,.0f} 涨跌: {change:>6.2f}%")
        
        # 4. 生成订单簿数据
        print(f"\n📖 生成主要币种订单簿...")
        orderbooks = {}
        for symbol, ticker in top_usdt[:5]:
            orderbook = self.generate_mock_orderbook(symbol, ticker['last'])
            orderbooks[symbol] = orderbook
            print(f"  ✅ {symbol} 订单簿生成完成")
        
        # 5. 生成K线数据
        print(f"\n📊 生成BTC/USDT K线数据...")
        klines = {
            '1h': self.generate_mock_klines('BTC/USDT', '1h', 24),
            '1d': self.generate_mock_klines('BTC/USDT', '1d', 7)
        }
        print(f"  ✅ 生成了 {len(klines['1h'])} 条1小时K线和 {len(klines['1d'])} 条日K线")
        
        # 6. 保存数据
        data = {
            'markets': markets,
            'tickers': tickers,
            'orderbooks': orderbooks,
            'klines': klines
        }
        self.save_data_to_files(data)
        
        # 7. 显示统计信息
        print("\n" + "="*60)
        print("📊 演示完成！统计信息:")
        print(f"  - 总交易对数量: {len(markets)}")
        print(f"  - 行情数据数量: {len(tickers)}")
        print(f"  - USDT交易对数量: {len(usdt_tickers)}")
        print(f"  - 订单簿数量: {len(orderbooks)}")
        print(f"  - K线数据: {len(klines['1h']) + len(klines['1d'])} 条")
        
        print(f"\n🏆 模拟的交易量最大的5个USDT交易对:")
        for i, (symbol, ticker) in enumerate(top_usdt[:5], 1):
            print(f"  {i}. {symbol}: ${ticker['last']:.4f} (24h量: ${ticker['quoteVolume']:,.0f})")
        
        print(f"\n💡 这是一个演示版本，展示了如何获取和处理币安的各种数据类型")
        print(f"   实际使用时，将模拟数据替换为真实的API调用即可")

def main():
    """主函数"""
    demo = BinanceDataDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
