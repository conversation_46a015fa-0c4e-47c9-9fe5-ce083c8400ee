// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bitmex : Exchange
{
    public bitmex (object args = null): base(args) {}

    public async Task<object> publicGetAnnouncement (object parameters = null)
    {
        return await this.callAsync ("publicGetAnnouncement",parameters);
    }

    public async Task<object> publicGetAnnouncementUrgent (object parameters = null)
    {
        return await this.callAsync ("publicGetAnnouncementUrgent",parameters);
    }

    public async Task<object> publicGetChat (object parameters = null)
    {
        return await this.callAsync ("publicGetChat",parameters);
    }

    public async Task<object> publicGetChatChannels (object parameters = null)
    {
        return await this.callAsync ("publicGetChatChannels",parameters);
    }

    public async Task<object> publicGetChatConnected (object parameters = null)
    {
        return await this.callAsync ("publicGetChatConnected",parameters);
    }

    public async Task<object> publicGetChatPinned (object parameters = null)
    {
        return await this.callAsync ("publicGetChatPinned",parameters);
    }

    public async Task<object> publicGetFunding (object parameters = null)
    {
        return await this.callAsync ("publicGetFunding",parameters);
    }

    public async Task<object> publicGetGuild (object parameters = null)
    {
        return await this.callAsync ("publicGetGuild",parameters);
    }

    public async Task<object> publicGetInstrument (object parameters = null)
    {
        return await this.callAsync ("publicGetInstrument",parameters);
    }

    public async Task<object> publicGetInstrumentActive (object parameters = null)
    {
        return await this.callAsync ("publicGetInstrumentActive",parameters);
    }

    public async Task<object> publicGetInstrumentActiveAndIndices (object parameters = null)
    {
        return await this.callAsync ("publicGetInstrumentActiveAndIndices",parameters);
    }

    public async Task<object> publicGetInstrumentActiveIntervals (object parameters = null)
    {
        return await this.callAsync ("publicGetInstrumentActiveIntervals",parameters);
    }

    public async Task<object> publicGetInstrumentCompositeIndex (object parameters = null)
    {
        return await this.callAsync ("publicGetInstrumentCompositeIndex",parameters);
    }

    public async Task<object> publicGetInstrumentIndices (object parameters = null)
    {
        return await this.callAsync ("publicGetInstrumentIndices",parameters);
    }

    public async Task<object> publicGetInstrumentUsdVolume (object parameters = null)
    {
        return await this.callAsync ("publicGetInstrumentUsdVolume",parameters);
    }

    public async Task<object> publicGetInsurance (object parameters = null)
    {
        return await this.callAsync ("publicGetInsurance",parameters);
    }

    public async Task<object> publicGetLeaderboard (object parameters = null)
    {
        return await this.callAsync ("publicGetLeaderboard",parameters);
    }

    public async Task<object> publicGetLiquidation (object parameters = null)
    {
        return await this.callAsync ("publicGetLiquidation",parameters);
    }

    public async Task<object> publicGetOrderBookL2 (object parameters = null)
    {
        return await this.callAsync ("publicGetOrderBookL2",parameters);
    }

    public async Task<object> publicGetPorlNonce (object parameters = null)
    {
        return await this.callAsync ("publicGetPorlNonce",parameters);
    }

    public async Task<object> publicGetQuote (object parameters = null)
    {
        return await this.callAsync ("publicGetQuote",parameters);
    }

    public async Task<object> publicGetQuoteBucketed (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteBucketed",parameters);
    }

    public async Task<object> publicGetSchema (object parameters = null)
    {
        return await this.callAsync ("publicGetSchema",parameters);
    }

    public async Task<object> publicGetSchemaWebsocketHelp (object parameters = null)
    {
        return await this.callAsync ("publicGetSchemaWebsocketHelp",parameters);
    }

    public async Task<object> publicGetSettlement (object parameters = null)
    {
        return await this.callAsync ("publicGetSettlement",parameters);
    }

    public async Task<object> publicGetStats (object parameters = null)
    {
        return await this.callAsync ("publicGetStats",parameters);
    }

    public async Task<object> publicGetStatsHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetStatsHistory",parameters);
    }

    public async Task<object> publicGetStatsHistoryUSD (object parameters = null)
    {
        return await this.callAsync ("publicGetStatsHistoryUSD",parameters);
    }

    public async Task<object> publicGetTrade (object parameters = null)
    {
        return await this.callAsync ("publicGetTrade",parameters);
    }

    public async Task<object> publicGetTradeBucketed (object parameters = null)
    {
        return await this.callAsync ("publicGetTradeBucketed",parameters);
    }

    public async Task<object> publicGetWalletAssets (object parameters = null)
    {
        return await this.callAsync ("publicGetWalletAssets",parameters);
    }

    public async Task<object> publicGetWalletNetworks (object parameters = null)
    {
        return await this.callAsync ("publicGetWalletNetworks",parameters);
    }

    public async Task<object> privateGetAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetAddress",parameters);
    }

    public async Task<object> privateGetApiKey (object parameters = null)
    {
        return await this.callAsync ("privateGetApiKey",parameters);
    }

    public async Task<object> privateGetExecution (object parameters = null)
    {
        return await this.callAsync ("privateGetExecution",parameters);
    }

    public async Task<object> privateGetExecutionTradeHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetExecutionTradeHistory",parameters);
    }

    public async Task<object> privateGetGlobalNotification (object parameters = null)
    {
        return await this.callAsync ("privateGetGlobalNotification",parameters);
    }

    public async Task<object> privateGetLeaderboardName (object parameters = null)
    {
        return await this.callAsync ("privateGetLeaderboardName",parameters);
    }

    public async Task<object> privateGetOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetOrder",parameters);
    }

    public async Task<object> privateGetPorlSnapshots (object parameters = null)
    {
        return await this.callAsync ("privateGetPorlSnapshots",parameters);
    }

    public async Task<object> privateGetPosition (object parameters = null)
    {
        return await this.callAsync ("privateGetPosition",parameters);
    }

    public async Task<object> privateGetUser (object parameters = null)
    {
        return await this.callAsync ("privateGetUser",parameters);
    }

    public async Task<object> privateGetUserAffiliateStatus (object parameters = null)
    {
        return await this.callAsync ("privateGetUserAffiliateStatus",parameters);
    }

    public async Task<object> privateGetUserCheckReferralCode (object parameters = null)
    {
        return await this.callAsync ("privateGetUserCheckReferralCode",parameters);
    }

    public async Task<object> privateGetUserCommission (object parameters = null)
    {
        return await this.callAsync ("privateGetUserCommission",parameters);
    }

    public async Task<object> privateGetUserCsa (object parameters = null)
    {
        return await this.callAsync ("privateGetUserCsa",parameters);
    }

    public async Task<object> privateGetUserDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetUserDepositAddress",parameters);
    }

    public async Task<object> privateGetUserExecutionHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetUserExecutionHistory",parameters);
    }

    public async Task<object> privateGetUserGetWalletTransferAccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetUserGetWalletTransferAccounts",parameters);
    }

    public async Task<object> privateGetUserMargin (object parameters = null)
    {
        return await this.callAsync ("privateGetUserMargin",parameters);
    }

    public async Task<object> privateGetUserQuoteFillRatio (object parameters = null)
    {
        return await this.callAsync ("privateGetUserQuoteFillRatio",parameters);
    }

    public async Task<object> privateGetUserQuoteValueRatio (object parameters = null)
    {
        return await this.callAsync ("privateGetUserQuoteValueRatio",parameters);
    }

    public async Task<object> privateGetUserStaking (object parameters = null)
    {
        return await this.callAsync ("privateGetUserStaking",parameters);
    }

    public async Task<object> privateGetUserStakingInstruments (object parameters = null)
    {
        return await this.callAsync ("privateGetUserStakingInstruments",parameters);
    }

    public async Task<object> privateGetUserStakingTiers (object parameters = null)
    {
        return await this.callAsync ("privateGetUserStakingTiers",parameters);
    }

    public async Task<object> privateGetUserTradingVolume (object parameters = null)
    {
        return await this.callAsync ("privateGetUserTradingVolume",parameters);
    }

    public async Task<object> privateGetUserUnstakingRequests (object parameters = null)
    {
        return await this.callAsync ("privateGetUserUnstakingRequests",parameters);
    }

    public async Task<object> privateGetUserWallet (object parameters = null)
    {
        return await this.callAsync ("privateGetUserWallet",parameters);
    }

    public async Task<object> privateGetUserWalletHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetUserWalletHistory",parameters);
    }

    public async Task<object> privateGetUserWalletSummary (object parameters = null)
    {
        return await this.callAsync ("privateGetUserWalletSummary",parameters);
    }

    public async Task<object> privateGetUserAffiliates (object parameters = null)
    {
        return await this.callAsync ("privateGetUserAffiliates",parameters);
    }

    public async Task<object> privateGetUserEvent (object parameters = null)
    {
        return await this.callAsync ("privateGetUserEvent",parameters);
    }

    public async Task<object> privatePostAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostAddress",parameters);
    }

    public async Task<object> privatePostChat (object parameters = null)
    {
        return await this.callAsync ("privatePostChat",parameters);
    }

    public async Task<object> privatePostGuild (object parameters = null)
    {
        return await this.callAsync ("privatePostGuild",parameters);
    }

    public async Task<object> privatePostGuildArchive (object parameters = null)
    {
        return await this.callAsync ("privatePostGuildArchive",parameters);
    }

    public async Task<object> privatePostGuildJoin (object parameters = null)
    {
        return await this.callAsync ("privatePostGuildJoin",parameters);
    }

    public async Task<object> privatePostGuildKick (object parameters = null)
    {
        return await this.callAsync ("privatePostGuildKick",parameters);
    }

    public async Task<object> privatePostGuildLeave (object parameters = null)
    {
        return await this.callAsync ("privatePostGuildLeave",parameters);
    }

    public async Task<object> privatePostGuildSharesTrades (object parameters = null)
    {
        return await this.callAsync ("privatePostGuildSharesTrades",parameters);
    }

    public async Task<object> privatePostOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostOrder",parameters);
    }

    public async Task<object> privatePostOrderCancelAllAfter (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderCancelAllAfter",parameters);
    }

    public async Task<object> privatePostOrderClosePosition (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderClosePosition",parameters);
    }

    public async Task<object> privatePostPositionIsolate (object parameters = null)
    {
        return await this.callAsync ("privatePostPositionIsolate",parameters);
    }

    public async Task<object> privatePostPositionLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostPositionLeverage",parameters);
    }

    public async Task<object> privatePostPositionRiskLimit (object parameters = null)
    {
        return await this.callAsync ("privatePostPositionRiskLimit",parameters);
    }

    public async Task<object> privatePostPositionTransferMargin (object parameters = null)
    {
        return await this.callAsync ("privatePostPositionTransferMargin",parameters);
    }

    public async Task<object> privatePostUserAddSubaccount (object parameters = null)
    {
        return await this.callAsync ("privatePostUserAddSubaccount",parameters);
    }

    public async Task<object> privatePostUserCancelWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostUserCancelWithdrawal",parameters);
    }

    public async Task<object> privatePostUserCommunicationToken (object parameters = null)
    {
        return await this.callAsync ("privatePostUserCommunicationToken",parameters);
    }

    public async Task<object> privatePostUserConfirmEmail (object parameters = null)
    {
        return await this.callAsync ("privatePostUserConfirmEmail",parameters);
    }

    public async Task<object> privatePostUserConfirmWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostUserConfirmWithdrawal",parameters);
    }

    public async Task<object> privatePostUserLogout (object parameters = null)
    {
        return await this.callAsync ("privatePostUserLogout",parameters);
    }

    public async Task<object> privatePostUserPreferences (object parameters = null)
    {
        return await this.callAsync ("privatePostUserPreferences",parameters);
    }

    public async Task<object> privatePostUserRequestWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostUserRequestWithdrawal",parameters);
    }

    public async Task<object> privatePostUserUnstakingRequests (object parameters = null)
    {
        return await this.callAsync ("privatePostUserUnstakingRequests",parameters);
    }

    public async Task<object> privatePostUserUpdateSubaccount (object parameters = null)
    {
        return await this.callAsync ("privatePostUserUpdateSubaccount",parameters);
    }

    public async Task<object> privatePostUserWalletTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostUserWalletTransfer",parameters);
    }

    public async Task<object> privatePutGuild (object parameters = null)
    {
        return await this.callAsync ("privatePutGuild",parameters);
    }

    public async Task<object> privatePutOrder (object parameters = null)
    {
        return await this.callAsync ("privatePutOrder",parameters);
    }

    public async Task<object> privateDeleteOrder (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrder",parameters);
    }

    public async Task<object> privateDeleteOrderAll (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrderAll",parameters);
    }

    public async Task<object> privateDeleteUserUnstakingRequests (object parameters = null)
    {
        return await this.callAsync ("privateDeleteUserUnstakingRequests",parameters);
    }

}