{"BTC/USDT": {"id": "btcusdt", "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "BTC/ETH": {"id": "btceth", "symbol": "BTC/ETH", "base": "BTC", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 6}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "BTC/BNB": {"id": "btcbnb", "symbol": "BTC/BNB", "base": "BTC", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ETH/USDT": {"id": "<PERSON><PERSON><PERSON>", "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ETH/BTC": {"id": "ethbtc", "symbol": "ETH/BTC", "base": "ETH", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ETH/BNB": {"id": "ethbnb", "symbol": "ETH/BNB", "base": "ETH", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "BNB/USDT": {"id": "bnbusdt", "symbol": "BNB/USDT", "base": "BNB", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "BNB/BTC": {"id": "bnbbtc", "symbol": "BNB/BTC", "base": "BNB", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "BNB/ETH": {"id": "bnbeth", "symbol": "BNB/ETH", "base": "BNB", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 8}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ADA/USDT": {"id": "<PERSON><PERSON><PERSON>", "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ADA/BTC": {"id": "adabtc", "symbol": "ADA/BTC", "base": "ADA", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 6, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ADA/ETH": {"id": "ad<PERSON><PERSON>", "symbol": "ADA/ETH", "base": "ADA", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ADA/BNB": {"id": "adabnb", "symbol": "ADA/BNB", "base": "ADA", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 6}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "XRP/USDT": {"id": "xrpusdt", "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "XRP/BTC": {"id": "xrpbtc", "symbol": "XRP/BTC", "base": "XRP", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "XRP/ETH": {"id": "xrpeth", "symbol": "XRP/ETH", "base": "XRP", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 8}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "XRP/BNB": {"id": "xrpbnb", "symbol": "XRP/BNB", "base": "XRP", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "SOL/USDT": {"id": "solus<PERSON>", "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "SOL/BTC": {"id": "solbtc", "symbol": "SOL/BTC", "base": "SOL", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 6}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "SOL/ETH": {"id": "soleth", "symbol": "SOL/ETH", "base": "SOL", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 6, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "SOL/BNB": {"id": "solbnb", "symbol": "SOL/BNB", "base": "SOL", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "DOT/USDT": {"id": "dotusdt", "symbol": "DOT/USDT", "base": "DOT", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "DOT/BTC": {"id": "dotbtc", "symbol": "DOT/BTC", "base": "DOT", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "DOT/ETH": {"id": "doteth", "symbol": "DOT/ETH", "base": "DOT", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "DOT/BNB": {"id": "dotbnb", "symbol": "DOT/BNB", "base": "DOT", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 8, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "DOGE/USDT": {"id": "<PERSON><PERSON><PERSON>", "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "DOGE/BTC": {"id": "dogebtc", "symbol": "DOGE/BTC", "base": "DOGE", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 8, "price": 8}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "DOGE/ETH": {"id": "dogeeth", "symbol": "DOGE/ETH", "base": "DOGE", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 8, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "DOGE/BNB": {"id": "dogebnb", "symbol": "DOGE/BNB", "base": "DOGE", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "AVAX/USDT": {"id": "avaxusdt", "symbol": "AVAX/USDT", "base": "AVAX", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "AVAX/BTC": {"id": "avaxbtc", "symbol": "AVAX/BTC", "base": "AVAX", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "AVAX/ETH": {"id": "avaxeth", "symbol": "AVAX/ETH", "base": "AVAX", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 6, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "AVAX/BNB": {"id": "avaxbnb", "symbol": "AVAX/BNB", "base": "AVAX", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "MATIC/USDT": {"id": "mat<PERSON><PERSON>", "symbol": "MATIC/USDT", "base": "MATIC", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "MATIC/BTC": {"id": "maticbtc", "symbol": "MATIC/BTC", "base": "MATIC", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "MATIC/ETH": {"id": "maticeth", "symbol": "MATIC/ETH", "base": "MATIC", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "MATIC/BNB": {"id": "maticbnb", "symbol": "MATIC/BNB", "base": "MATIC", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 8}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "LINK/USDT": {"id": "linkusdt", "symbol": "LINK/USDT", "base": "LINK", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "LINK/BTC": {"id": "linkbtc", "symbol": "LINK/BTC", "base": "LINK", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 6, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "LINK/ETH": {"id": "linketh", "symbol": "LINK/ETH", "base": "LINK", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "LINK/BNB": {"id": "linkbnb", "symbol": "LINK/BNB", "base": "LINK", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "UNI/USDT": {"id": "uniusdt", "symbol": "UNI/USDT", "base": "UNI", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 8, "price": 8}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "UNI/BTC": {"id": "unibtc", "symbol": "UNI/BTC", "base": "UNI", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "UNI/ETH": {"id": "unieth", "symbol": "UNI/ETH", "base": "UNI", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "UNI/BNB": {"id": "unibnb", "symbol": "UNI/BNB", "base": "UNI", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 5, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "LTC/USDT": {"id": "ltcusdt", "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 6, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "LTC/BTC": {"id": "ltcbtc", "symbol": "LTC/BTC", "base": "LTC", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "LTC/ETH": {"id": "ltceth", "symbol": "LTC/ETH", "base": "LTC", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "LTC/BNB": {"id": "ltcbnb", "symbol": "LTC/BNB", "base": "LTC", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 8}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "BCH/USDT": {"id": "b<PERSON><PERSON>", "symbol": "BCH/USDT", "base": "BCH", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "BCH/BTC": {"id": "bchbtc", "symbol": "BCH/BTC", "base": "BCH", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "BCH/ETH": {"id": "bcheth", "symbol": "BCH/ETH", "base": "BCH", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 6, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "BCH/BNB": {"id": "bchbnb", "symbol": "BCH/BNB", "base": "BCH", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "FIL/USDT": {"id": "filusdt", "symbol": "FIL/USDT", "base": "FIL", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "FIL/BTC": {"id": "filbtc", "symbol": "FIL/BTC", "base": "FIL", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 8, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "FIL/ETH": {"id": "fileth", "symbol": "FIL/ETH", "base": "FIL", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "FIL/BNB": {"id": "filbnb", "symbol": "FIL/BNB", "base": "FIL", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "TRX/USDT": {"id": "trxusdt", "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 8, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "TRX/BTC": {"id": "trxbtc", "symbol": "TRX/BTC", "base": "TRX", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 8}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "TRX/ETH": {"id": "trxeth", "symbol": "TRX/ETH", "base": "TRX", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 8, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "TRX/BNB": {"id": "trxbnb", "symbol": "TRX/BNB", "base": "TRX", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ETC/USDT": {"id": "etcusdt", "symbol": "ETC/USDT", "base": "ETC", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 8, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ETC/BTC": {"id": "etcbtc", "symbol": "ETC/BTC", "base": "ETC", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 6}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ETC/ETH": {"id": "etceth", "symbol": "ETC/ETH", "base": "ETC", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ETC/BNB": {"id": "etcbnb", "symbol": "ETC/BNB", "base": "ETC", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 6}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "XLM/USDT": {"id": "xlmusdt", "symbol": "XLM/USDT", "base": "XLM", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "XLM/BTC": {"id": "xlmbtc", "symbol": "XLM/BTC", "base": "XLM", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 7, "price": 3}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "XLM/ETH": {"id": "xlmeth", "symbol": "XLM/ETH", "base": "XLM", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "XLM/BNB": {"id": "xlmbnb", "symbol": "XLM/BNB", "base": "XLM", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "VET/USDT": {"id": "vetusdt", "symbol": "VET/USDT", "base": "VET", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 5}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "VET/BTC": {"id": "vetbtc", "symbol": "VET/BTC", "base": "VET", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 8}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "VET/ETH": {"id": "veteth", "symbol": "VET/ETH", "base": "VET", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 7}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "VET/BNB": {"id": "vetbnb", "symbol": "VET/BNB", "base": "VET", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 2, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ICP/USDT": {"id": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "ICP/USDT", "base": "ICP", "quote": "USDT", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 8}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ICP/BTC": {"id": "icpbtc", "symbol": "ICP/BTC", "base": "ICP", "quote": "BTC", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 6, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ICP/ETH": {"id": "i<PERSON><PERSON><PERSON>", "symbol": "ICP/ETH", "base": "ICP", "quote": "ETH", "active": true, "type": "spot", "spot": true, "margin": false, "future": false, "option": false, "contract": false, "precision": {"amount": 4, "price": 2}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}, "ICP/BNB": {"id": "icpbnb", "symbol": "ICP/BNB", "base": "ICP", "quote": "BNB", "active": true, "type": "spot", "spot": true, "margin": true, "future": false, "option": false, "contract": false, "precision": {"amount": 3, "price": 4}, "limits": {"amount": {"min": 0.001, "max": 1000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": 10, "max": 1000000}}}}