{
  "env": {
    "es2021": true,
    "node": true
  },
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "module"
  },
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "extends": ["eslint:recommended", "airbnb-base", "plugin:import/typescript"],
  "ignorePatterns": ["base/functions/*", "static_dependencies/*", "pro/bridge/*", "abstract/*"],
  "rules": {
    "import/no-unresolved":"off", // tmp until typescript decides to properly resolve names
    "jsdoc/require-param-description": "off",
    "import/named": "off",
    "strict": "off",
    "semi": "error",
    "indent": ["error", 4],
    "init-declarations": "off",
    "no-undef-init": "off",
    "comma-dangle": ["error", {
        "arrays": "always-multiline",
        "objects": "always-multiline",
        "imports": "always-multiline",
        "exports": "always-multiline",
        "functions": "never"
    }],
    "no-use-before-define":"off",
    "import/extensions": "off",
    "brace-style": ["error", "1tbs"],
    "multiline-comment-style": ["error", "separate-lines"],
    "dot-notation": "off",
    "quote-props": ["error", "always"],
    "no-multi-spaces": ["error", { "ignoreEOLComments": true }],
    "no-whitespace-before-property": "error",
    "space-before-blocks": ["error", "always"],
    "space-before-function-paren": ["error", "always"],
    "no-spaced-func": "off",
    "func-call-spacing": ["error", "always"],
    "block-spacing": ["error", "always"],
    "keyword-spacing": ["error", { "before": true, "after": true }],
    "object-curly-spacing": ["error", "always", { "objectsInObjects": false }],
    "object-curly-newline": ["error", { "consistent": true }],
    "space-infix-ops": "error",
    "space-unary-ops": "error",
    "space-in-parens": "error",
    "no-nested-ternary": "error",
    "eqeqeq": "error",
    "quotes": ["error", "single", { "avoidEscape": true }],
    "no-unused-vars": ["error", { "argsIgnorePattern": "^(exchange|headers|body|account|info|symbol|price|tag|side|since|limit|params|market|timeframe|api|path|code|currency|statusCode|statusText|url|method|response|requestHeaders|requestBody|bidsKey|asksKey|context|config|type|priceKey|amountKey|networkCode|marginMode|subscription|message|client|nonce|orderbook|bookside|deltas|delta|countOrIdKey|amount|fromCurrency|toCurrency|rawCurrency)" }],
    "new-parens": "error",
    "new-cap": ["error"],
    "no-var": "error",
    "prefer-const": ["error", {
        "destructuring": "any",
        "ignoreReadBeforeAssign": false
    }],
    "no-warning-comments": ["warn", { "terms": ["fixme"] }],
    "padded-blocks": ["error", "never"],
    "lines-between-class-members": "error",
    "no-multiple-empty-lines": ["error", { "max": 1 }],
    "padding-line-between-statements": ["error",
      // { "blankLine": "never",  "prev":"*", "next": "*" },
      { "blankLine": "always", "prev":"import", "next": "*" },
      { "blankLine": "never",  "prev":"import", "next": "import" },
      { "blankLine": "always", "prev":"directive", "next": "*" },
      { "blankLine": "always", "prev":"*", "next": "cjs-export" },
      { "blankLine": "always", "prev":"*", "next": "export" },
      { "blankLine": "any",    "prev":"export", "next": "export" },
      { "blankLine": "always", "prev":"*", "next": "function" }
    ],
    "prefer-template": "off",
    "curly": "error",
    "no-plusplus": "off",
    "no-restricted-properties": "off",
    "prefer-destructuring": "off",
    "class-methods-use-this": "off",
    "no-param-reassign": "off",
    "max-len": "off",
    "no-return-await": "off",
    "array-bracket-spacing": ["error", "always"],
    "radix": "off",
    "camelcase": "off",
    "no-lonely-if": "off",
    "no-mixed-operators": "off",
    "no-shadow": ["error", {
      "builtinGlobals": true, // prevent shadowing global vars (i.e. Object, Array, Number, etc...)
      "hoist": "all", // just for cleanest practices
      "allow": [], // no need to allow any
      "ignoreOnInitialization": false // don't ignore lambdas and etc.
    }],
    "no-useless-concat": "off",
    "no-continue": "off",
    "no-else-return": "off",
    "no-unneeded-ternary": "off",
    "operator-assignment": "off",
    "no-underscore-dangle": "off",
    "consistent-return": "off",
    "no-await-in-loop": "off",
    "prefer-exponentiation-operator": "off",
  },
  "settings": {
    "import/resolver": {
      "node": {
        "extensions": [".js", ".ts"]
      }
    },
    "import/parsers": {
      "@typescript-eslint/parser": [".ts", ".js"]
    }
  }
}
