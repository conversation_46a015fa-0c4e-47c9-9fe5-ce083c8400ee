# Awesome CCXT

Suggestions and contributions are always welcome! Make sure to read the [contribution guidelines](#contributing)

- [Awesome CCXT](#awesome-ccxt)
  - [Trading bots](#trading-bots)
  - [Trading toolkits](#trading-toolkits)
  - [Trading terminals](#trading-terminals)
  - [Analytics platforms](#analytics-platforms)
  - [API](#api)
  - [AI](#ai)
  - [Utils](#utils)
  - [Languages](#languages)
  - [Javascript examples](#javascript-examples)
  - [Python examples](#python-examples)
  - [PHP examples](#php-examples)
  - [Articles](#articles)
  - [Videos](#videos)
- [Contributing](#contributing)

## Trading bots

- [Supertrend crypto bot](https://github.com/hackingthemarkets/supertrend-crypto-bot) - Python, pandas. Has a good video tutorial.
- [CryptoMon Bot](https://github.com/jchristov/cryptomon-bot) - Helps tracking of your cryptocurrency investments and making smart, informed buy/sell decisions.
- [ZenBot](https://github.com/carlos8f/zenbot) - A command-line cryptocurrency trading bot using Node.js and MongoDB.
- [Titan](https://github.com/Denton24646/Titan) - Based on python, flask, postgres.
- [Python-crypto-Bot](https://github.com/Seigneur774/Python-crypto-Bot)
- [Nash](https://github.com/joelsfoster/Nash) - Arbitrage bot on meteorjs
- [cryptodaemon](https://github.com/gmark4212/cryptodaemon) - Bot watches which currencies are now in a significant decline but have the potential for growth.
- [pine-bot-client](https://github.com/kzh-dev/pine-bot-client)
- [cryptobot](https://github.com/Zane-/cryptobot) - lowhighbot, poolbot, releasebot
- [ccxt-trader](https://github.com/Schnides123/ccxt-trader) - cryptocurrency arbitrage calculator on #python
- [KryptoBot](https://github.com/eristoddle/KryptoBot)
- [blockbid-ccxt-tutorials](https://github.com/thomasdavis/blockbid-ccxt-tutorials)
- [trader0](https://github.com/kebnekaise-io/trader0)
- [bakuchi](https://github.com/tsu-nera/bakuchi) - arbitrage bot
- [Superalgos](https://github.com/superalgos/superalgos) - Trading bot platform, node.js, tensorflow

## Signals

- [Peregrine](https://github.com/wardbradt/peregrine) - Arbitrage on python.
- [ccxt-arbitrage](https://github.com/ArthurAnanda/ccxt-arbitrage-v1)
- [crypto-arbitrage-framework](https://github.com/hzjken/crypto-arbitrage-framework)
- [Nash](https://github.com/joelsfoster/Nash) - Arbitrage bot on meteorjs
- [ccxt-trade-mgr](https://github.com/notlesh/ccxt-trade-mgr) - take profit vs. stop loss
- [EasyTrage](https://github.com/Vikrammel/EasyTrage) - crypto arbitrage bot in MERN
- [Automatic-Arbitrage-Crypto-exchange](https://github.com/ZgodaRafal/Automatic-Arbitrage-Crypto-exchange)
- [Cryptocurrency-Multi-Exchange-Arbitrage-Strategy](https://github.com/chicago-joe/Cryptocurrency-Multi-Exchange-Arbitrage-Strategy)

## Trading toolkits

- [The Coinbase Pro trading toolkit (CPTT)](https://github.com/coinbase/coinbase-pro-trading-toolkit) – a trading toolkit developed by the coinbase team that integrates with ccxt, bitfinex, bitmex, bittrex, coinbasePro, gemini, poloniex.
- [bt-ccxt-store](https://github.com/Dave-Vallance/bt-ccxt-store) – Store with backtesting. #python
- [ZTOM](https://github.com/ztomsy/ztom) - #python, #backtesting, #rest, orders manager, loggin, reporting, throttling control.
- [TradingSystemDemo](https://github.com/FWangTrading/TradingSystemDemo) - #Catalyst, #CryptoCompare, #backtesting, #alerts, #trading, #python
- [Visuccxt](https://github.com/wnklb/Visuccxt) - #python
- [DataSynchronizer](https://github.com/stockmlbot/DataSynchronizer)
- [Trading-indicator](https://github.com/thanhnguyennguyen/trading-indicator) - fetch data with ccxt and generate indicators

## Trading terminals

- [Kupi-terminal](https://github.com/kupi-network/kupi-terminal) - Customized, extendable trading platform based on js, vue, react, express (rest api), mongo. Can: plot OHLCV, buy/sell, show balances, history balances, orders, trades, my orders, my trades.
- [ccxt-trading-cp](https://github.com/michnovka/ccxt-trading-cp) - Trading terminal in console: plot OHLCV, find arbitrage signals, buy/sell, show balances.
- [Auto-Trade-Crypto-Bot](https://github.com/dev4Fun/Auto-Trade-Crypto-Bot) - Crypto trading #bot in #python with #telegram.
- [ccxt-exchange-template](https://github.com/AdrenalineAI/ccxt-exchange-template) - python

## Analytics platforms

- [CoinTop](https://github.com/fatihacet/CoinTop) - portfolio with secure access to keys
- [DACP](https://github.com/Pyeskyhigh/DACP) - portfolio
- [hodlwatch](https://github.com/belaczek/hodlwatch) - portfolio
- [Live-Crypto-Dashboard-and-DB](https://github.com/srozov/Live-Crypto-Dashboard-and-DB) - python, sentiments

## API

- [sanic-ccxt](https://github.com/zloyuser/sanic-ccxt) - Unified REST API on #python
- [ccxt-rest](https://github.com/franz-see/ccxt-rest) - Unified REST API on #javascript
- [ccxt-microservice](https://github.com/xeno14/ccxt-microservice) - Unified REST API on #python
- [mpakus/ccxt-server](https://github.com/mpakus/ccxt-server) - #javascript
- [imloama/ccxt-server](https://github.com/imloama/ccxt-server) - #typescript
- [ccxt-private-ws](https://github.com/invao/ccxt-private-ws) - private websocket connections with CCXT compatible data layout

## AI

- [Taurus](https://github.com/OptimalPandemic/taurus) - A cryptocurrency trading platform using deep reinforcement learning.

## Utils

- [bitcoin-chart-cli](https://github.com/madnight/bitcoin-chart-cli) by [madnight](https://github.com/madnight) – a command-line console util that draws Bitcoin, Ether, Litecoin and many altcoin charts right in the terminal!
- [node-red-contrib-ccxt](https://github.com/masalinas/node-red-contrib-ccxt) - Node-red integration.
- [freqcache](https://github.com/creslinux/freqcache) - A environment to control egress and ingress data from ccxt fronted trading bots for security and scalability
- [candlestick-convert](https://github.com/valamidev/candlestick-convert) - [Object,Object] => [[],[]]
- [crypto-exporter](https://github.com/ix-ai/crypto-exporter) - An exporter capable of connecting to multiple exchanges and getting account balances and exchange rates
- [ccxtreplay](https://github.com/xCuri0/ccxtreplay) - collect data from ccxt exchanges and replay it for backtesting

## Languages

- [CCXT](https://github.com/ccxt/ccxt) - Javascript, python, php, C#.
- [kizzx2/ccxt-rs](https://github.com/kizzx2/ccxt-rs) - Rust.
- [irbis-labs/ccxt-rs](https://github.com/irbis-labs/ccxt-rs) - Rust. Early version. See branch 'preview'.
- [CCXT.NET](https://github.com/lisa3907/ccxt.net) - C#.
- [ccxtex](https://github.com/metachaos-systems/ccxtex) - Elixir/Erlang.
- [ccxt.dart](https://github.com/Sach97/ccxt.dart) - Dart.
- [ccxt-go](https://github.com/prompt-cash/ccxt-go) - Go.

## Javascript examples

- [Examples from ccxt](https://github.com/ccxt/ccxt/tree/master/examples/js)
- [Using ccxt and technicalindicators to calculate MACD for BTC/USDT with Node.js](https://runkit.com/dhilipb/macd-for-btc-usdt) – A runkit sample showing how to fetch data and run statistical calculations.

## Python examples

- [Examples from ccxt](https://github.com/ccxt/ccxt/tree/master/examples/py)
- [ccxt_notes](https://github.com/yinruiqing/ccxt_notes) - jupiter notebooks
- [51bitqunt](https://github.com/ramoslin02/51bitqunt) - video playlist

## PHP examples

- [Examples from ccxt](https://github.com/ccxt/ccxt/tree/master/examples/php)

## Articles

- [Enigma Catalyst](https://blog.enigma.co/enigma-announces-catalyst-0-4-our-biggest-release-yet-fa31a5ffa4b1) – The major effort towards decentralized exchanges integrates ccxt!
- [Playing with CCXT in Google Colab](https://medium.com/@ccxt/playing-with-ccxt-in-google-colab-23522ac8a6cb) – An article on how useful Colab can be for quick prototyping and testing your trading ideas with CCXT.
- [CC Power Analytics Part 1: How to get the data of the exchanges](https://www.linkedin.com/pulse/part-1-cc-power-analytics-how-get-data-exchanges-steve-rein/) – The first part of a series of articles on cryptocurrency analytics.
- [Looking for arbitrage opportunies with ccxt](https://steemit.com/steemdev/@codewithcheese/looking-for-arbitrage-opportunies-with-javascript-library-cctx-supporting-70-exchanges) – An article @steemit on getting arbitrage started with ccxt for crypto-arbitrage.
- [A n00bs Guide To Deep Cryptocurrency Trading](https://medium.com/@LeonFedden/deep-cryptocurrency-trading-1e64af6d280a) – An article @ Medium on deep neural trading using ccxt for backend tasks.
- [Let’s write a cryptocurrency bot](https://medium.com/@joeldg/an-advanced-tutorial-a-new-crypto-currency-trading-bot-boilerplate-framework-e777733607ae) – A multi-part advanced tutorial on a new cryptocurrency trading framework also integrating ccxt.
- [Download Cryptocurrency Data with CCXT](https://backtest-rookies.com/2018/03/08/download-cryptocurrency-data-with-ccxt/) – A sample script to download historical data from exchanges with a video overview: [Easy Python script to download crypto currency market data with CCXT package](https://www.youtube.com/watch?v=PTGkJsrF7kQ).
- [Experiments : Creating a Crypto Twitter Bot to Announce Newly Listed CryptoCoins](https://medium.com/@kennychuaio/experiments-creating-a-crypto-twitter-bot-to-announce-newly-listed-cryptocoins-9cd23930f5cb) – An article on how to integrate with CCXT and Twitter to get new currencies from exchanges automatically.
- [How to make your own trading bot](https://codeburst.io/how-to-make-your-own-trading-bot-83b5c6e35036) – A tutorial on algortrading in Python.
- [Writing crypto trading bot in Python with Telegram and ccxt](https://medium.com/@maxAvdyushkin/writing-crypto-trading-bot-in-python-with-telegram-and-ccxt-80632a00c637) – A tutorial on programming a Telegram bot in Python with CCXT
- [Data science is eating the world — Here’s how you can start exploring it](https://medium.com/covee-network/data-science-is-eating-the-world-heres-how-you-can-start-exploring-it-37501414af15) – An article on collecting and analyzing financial big data.
- [Chasing fake volume: a crypto-plague](https://medium.com/@sylvainartplayribes/chasing-fake-volume-a-crypto-plague-ea1a3c1e0b5e) – An article @ Medium on trading volumes analysis.
- [CCXTが便利な理由！CCXTライブラリをインストールしよう](https://ryota-trade.com/?p=476) – An article in Japanese on getting CCXT up and runining.
- [Bitflyerや各取引所の個別APIをCCXTライブラリ経由で直接利用する方法](https://ryota-trade.com/?p=629) – How to use Bitflyer and individual API of each exchange directly via the CCXT library.
- [CCXTライブラリでBitflyerに注文を出す方法をマスターする](https://ryota-trade.com/?p=662) – Master placing orders on Bitflyer with the CCXT library.
- [CCXTでBitflyerに出した注文を管理・キャンセルする方法](https://ryota-trade.com/?p=759) – How to manage and cancel orders placed on Bitflyer with CCXT
- [Bitflyerの未約定の全注文をCCXTで一括でキャンセルする方法](https://ryota-trade.com/?p=833) – How to cancel all uncommitted orders of Bitflyer in bulk with CCXT
- [Python3とCCXTライブラリを用いたBitMEX自動売買bot作成Tips](https://note.mu/akagami/n/n0af0a96c261f) – An article in Japanese on getting started with CCXT and a few bot tips.
- [BitMEX 自動売買BOT開発 (API編) ① 開発環境の構築、APIライブラリの詳細、全APIマップ](https://note.mu/mazmex7/n/n1a3a0293ce82) – An article on developing an automatic BitMEX trading bot.
- [仮想通貨トレード Bot 制作に便利な CCXT ライブラリに関する知見まとめ (Python 随時更新)](http://www.stray-scrapbook.work/entry/2018/04/03/205700) – A summary on Python version of CCXT API in Japanese.
- [Python3とCCXTを使用して仮想通貨の自動売買プログラムを作る](http://www.hacky.xyz/entry/2018/03/18/200822) – Automatic cryptocurrency trading using Python 3 and CCXT
- [ccxtを使って裁定取引botを作ってみたらなぜか虚しくなった件](https://qiita.com/reon777/items/21ed87f19cdd50f08bd9) – An article in Japanese explaining the basics of programming an arbitrage bot with CCXT.
- [Python 3 / BitMEX の BOT を作ろう CCXT + BOT サンプルコード 〈基礎編〉](https://note.mu/mman/n/n5a9083864335) – A sample of basic BitMEX bot with CCXT in Python 3.
- [ccxtがbtcfxbot界隈でちょっと話題になっていたので使ってみた](http://cryptojapan.ml/entry/2018/03/01/151752) – Trying CCXT for a basic bot.
- [python異步加協程獲取比特幣市場信息](https://hk.saowen.com/a/18a648f24d6e7f54981e9db4411b56730a35dd2b3b27519083543bcd6198cd27) – An article in Chinese on using CCXT with Python.
- [Лучшая криптотрейдинг библиотека?](http://medium.com/@vladthelittleone/лучшая-криптотрейдинг-библиотека-67e308f96c1f) – An article in Russian on setting up CCXT to connect and trade with crypto exchanges.

## Videos

- [The Evolution of CCXT](https://www.youtube.com/watch?v=O5HrvSLeo90) – An awesome Gource visualization video on YouTube! )
- [A Video Crash-Course On Using CCXT](https://www.youtube.com/playlist?list=PLIFBTFgFpoJ-xGRz3v_2nF7f9HKZrfSpj) – A series of awesome video tutorials on getting started with CCXT!
- [Download Cryptocurrency Data with CCXT](https://backtest-rookies.com/2018/03/08/download-cryptocurrency-data-with-ccxt/) – A sample script to download historical data from exchanges with a video overview: [Easy Python script to download crypto currency market data with CCXT package](https://www.youtube.com/watch?v=PTGkJsrF7kQ).
- [Python数字货币量化交易开发入门视频-利用CCXT获取bitmex交易所的行情数据](https://www.bilibili.com/video/av44339579/) – An video in Chinese on how to get started with CCXT in Python and a tutorial on OHLCV + Pandas export to CSV.
- [数字货币量化交易1 【群友 Shadow 自制】ccxt 的python版本安装及使用入门](https://www.bilibili.com/video/av21795165) – Trading Digital Currencies 1: Installing and using the Python version of ccxt (video in Chinese)
- [数字货币量化交易2 【群友 林军 自制】ccxt Unified API命令详解及node版本使用演示](https://www.bilibili.com/video/av21842290) – Trading Digital Currencies 2: Using unified CCXT API (video in Chinese)
- [数字货币量化交易3 ccxt Custom API命令详解及node版本使用演示](https://www.bilibili.com/video/av21842988)
– Trading Digital Currencies 3: Details on using custom exchange-specific [implicit methods](https://github.com/ccxt/ccxt/wiki/Manual#implicit-api-methods) in ccxt (video in Chinese)
- [Algorithmic Trading - Aula 4](https://www.youtube.com/watch?v=BkKebXrhMGY) – A video introduction to algorithmic trading with CCXT from Curso Algo Trading in Portuguese language.
- [Présentation de projet - Créer un Bot Telegram de A à Z (+CCXT)](https://www.youtube.com/watch?v=yayJKRtg5M4) – La création d'un bot Telegram en Node.js, qui va rechercher des informations sur les différentes marketpalce de crypto-monnaies, et cela via la librairie CCXT.
- [Which Bitcoin crypto currency bot project? Gekko vs ccxt vs Tribeca vs Blackbird](https://www.youtube.com/watch?v=Bn2p-nkbVdE) – A video comparison of opensource cryptocurrency trading platforms.

## Other

- [Projects based on ccxt](https://github.com/ccxt/ccxt/network/dependents) – A list of hundreds of ccxt-based projects by developers from all over the world!
- [LOC-Extension](https://github.com/walkjivefly/LOC-Extension) – a LibreOffice extension which embeds ccxt to provide cryptocurrency price lookup in your spreadsheets.
- [exchange-connector](https://github.com/donvadicastro/exchange-connector) - Exchange connector over ccxt and kafka
- [ccew](https://github.com/saytoken/ccew) - has the same scheme as ccxt, but provide data by websocket

## Contributing

Please ensure your pull request adheres to the following:

- Search previous suggestions before making a new one to ensure yours is not a duplicate.
- Make an individual pull request for each suggestion.
- Use the following format: [NAME](LINK) - DESCRIPTION.
- Keep descriptions simple.
- New categories, or improvements to the existing ones are welcome.
- End all descriptions with a period.
- Try to ensure your pull requests only have one commit if possible. You can squash your commits into one before submitting.
- Make your pull request titles and descriptions as detailed as possible. Explain why an asset or resources deserves to be added to the list.
- Make sure your additions are in alphabetical order. This includes the headers and items themselves.
- Ensure that assets you submit are of high quality, documented well, and are recently maintained. It also helps if they're popular so I can research them before adding them to the list.
- This is not meant to be used as a list to advertise your own assets. Free or open-source assets should be prioritized over paid assets if they are of similar quality and feature sets.

## Special thanks
This was forked from /sindresorhus/awesone. Special thanks to [![Awesome](https://cdn.rawgit.com/sindresorhus/awesome/d7305f38d29fed78fa85652e3a63e154dd8e8829/media/badge.svg)](https://github.com/sindresorhus/awesome) for starting this list and those that contributed to it [contributors](https://github.com/suenot/awesome-ccxt/graphs/contributors), this wouldn't be possible without you!
