// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class coinbaseadvanced : coinbase
{
    public coinbaseadvanced (object args = null): base(args) {}

    public async Task<object> v2PublicGetCurrencies (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetCurrencies",parameters);
    }

    public async Task<object> v2PublicGetCurrenciesCrypto (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetCurrenciesCrypto",parameters);
    }

    public async Task<object> v2PublicGetTime (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetTime",parameters);
    }

    public async Task<object> v2PublicGetExchangeRates (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetExchangeRates",parameters);
    }

    public async Task<object> v2PublicGetUsersUserId (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetUsersUserId",parameters);
    }

    public async Task<object> v2PublicGetPricesSymbolBuy (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetPricesSymbolBuy",parameters);
    }

    public async Task<object> v2PublicGetPricesSymbolSell (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetPricesSymbolSell",parameters);
    }

    public async Task<object> v2PublicGetPricesSymbolSpot (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetPricesSymbolSpot",parameters);
    }

    public async Task<object> v2PrivateGetAccounts (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccounts",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountId",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdAddresses (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdAddresses",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdAddressesAddressId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdAddressesAddressId",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdAddressesAddressIdTransactions (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdAddressesAddressIdTransactions",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdTransactions (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdTransactions",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdTransactionsTransactionId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdTransactionsTransactionId",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdBuys (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdBuys",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdBuysBuyId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdBuysBuyId",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdSells (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdSells",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdSellsSellId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdSellsSellId",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdDeposits (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdDeposits",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdDepositsDepositId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdDepositsDepositId",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdWithdrawals (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdWithdrawals",parameters);
    }

    public async Task<object> v2PrivateGetAccountsAccountIdWithdrawalsWithdrawalId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetAccountsAccountIdWithdrawalsWithdrawalId",parameters);
    }

    public async Task<object> v2PrivateGetPaymentMethods (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetPaymentMethods",parameters);
    }

    public async Task<object> v2PrivateGetPaymentMethodsPaymentMethodId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetPaymentMethodsPaymentMethodId",parameters);
    }

    public async Task<object> v2PrivateGetUser (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetUser",parameters);
    }

    public async Task<object> v2PrivateGetUserAuth (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetUserAuth",parameters);
    }

    public async Task<object> v2PrivatePostAccounts (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccounts",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdPrimary (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdPrimary",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdAddresses (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdAddresses",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdTransactions (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdTransactions",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdTransactionsTransactionIdComplete (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdTransactionsTransactionIdComplete",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdTransactionsTransactionIdResend (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdTransactionsTransactionIdResend",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdBuys (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdBuys",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdBuysBuyIdCommit (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdBuysBuyIdCommit",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdSells (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdSells",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdSellsSellIdCommit (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdSellsSellIdCommit",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdDeposits (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdDeposits",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdDepositsDepositIdCommit (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdDepositsDepositIdCommit",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdWithdrawals (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdWithdrawals",parameters);
    }

    public async Task<object> v2PrivatePostAccountsAccountIdWithdrawalsWithdrawalIdCommit (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountsAccountIdWithdrawalsWithdrawalIdCommit",parameters);
    }

    public async Task<object> v2PrivatePutAccountsAccountId (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePutAccountsAccountId",parameters);
    }

    public async Task<object> v2PrivatePutUser (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePutUser",parameters);
    }

    public async Task<object> v2PrivateDeleteAccountsId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateDeleteAccountsId",parameters);
    }

    public async Task<object> v2PrivateDeleteAccountsAccountIdTransactionsTransactionId (object parameters = null)
    {
        return await this.callAsync ("v2PrivateDeleteAccountsAccountIdTransactionsTransactionId",parameters);
    }

    public async Task<object> v3PublicGetBrokerageTime (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetBrokerageTime",parameters);
    }

    public async Task<object> v3PublicGetBrokerageMarketProductBook (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetBrokerageMarketProductBook",parameters);
    }

    public async Task<object> v3PublicGetBrokerageMarketProducts (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetBrokerageMarketProducts",parameters);
    }

    public async Task<object> v3PublicGetBrokerageMarketProductsProductId (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetBrokerageMarketProductsProductId",parameters);
    }

    public async Task<object> v3PublicGetBrokerageMarketProductsProductIdCandles (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetBrokerageMarketProductsProductIdCandles",parameters);
    }

    public async Task<object> v3PublicGetBrokerageMarketProductsProductIdTicker (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetBrokerageMarketProductsProductIdTicker",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageAccounts (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageAccounts",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageAccountsAccountUuid (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageAccountsAccountUuid",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageOrdersHistoricalBatch (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageOrdersHistoricalBatch",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageOrdersHistoricalFills (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageOrdersHistoricalFills",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageOrdersHistoricalOrderId (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageOrdersHistoricalOrderId",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageProducts (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageProducts",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageProductsProductId (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageProductsProductId",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageProductsProductIdCandles (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageProductsProductIdCandles",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageProductsProductIdTicker (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageProductsProductIdTicker",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageBestBidAsk (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageBestBidAsk",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageProductBook (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageProductBook",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageTransactionSummary (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageTransactionSummary",parameters);
    }

    public async Task<object> v3PrivateGetBrokeragePortfolios (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokeragePortfolios",parameters);
    }

    public async Task<object> v3PrivateGetBrokeragePortfoliosPortfolioUuid (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokeragePortfoliosPortfolioUuid",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageConvertTradeTradeId (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageConvertTradeTradeId",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageCfmBalanceSummary (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageCfmBalanceSummary",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageCfmPositions (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageCfmPositions",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageCfmPositionsProductId (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageCfmPositionsProductId",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageCfmSweeps (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageCfmSweeps",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageIntxPortfolioPortfolioUuid (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageIntxPortfolioPortfolioUuid",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageIntxPositionsPortfolioUuid (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageIntxPositionsPortfolioUuid",parameters);
    }

    public async Task<object> v3PrivateGetBrokerageIntxPositionsPortfolioUuidSymbol (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokerageIntxPositionsPortfolioUuidSymbol",parameters);
    }

    public async Task<object> v3PrivateGetBrokeragePaymentMethods (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokeragePaymentMethods",parameters);
    }

    public async Task<object> v3PrivateGetBrokeragePaymentMethodsPaymentMethodId (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBrokeragePaymentMethodsPaymentMethodId",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageOrders (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageOrders",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageOrdersBatchCancel (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageOrdersBatchCancel",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageOrdersEdit (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageOrdersEdit",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageOrdersEditPreview (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageOrdersEditPreview",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageOrdersPreview (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageOrdersPreview",parameters);
    }

    public async Task<object> v3PrivatePostBrokeragePortfolios (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokeragePortfolios",parameters);
    }

    public async Task<object> v3PrivatePostBrokeragePortfoliosMoveFunds (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokeragePortfoliosMoveFunds",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageConvertQuote (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageConvertQuote",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageConvertTradeTradeId (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageConvertTradeTradeId",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageCfmSweepsSchedule (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageCfmSweepsSchedule",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageIntxAllocate (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageIntxAllocate",parameters);
    }

    public async Task<object> v3PrivatePostBrokerageOrdersClosePosition (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostBrokerageOrdersClosePosition",parameters);
    }

    public async Task<object> v3PrivatePutBrokeragePortfoliosPortfolioUuid (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePutBrokeragePortfoliosPortfolioUuid",parameters);
    }

    public async Task<object> v3PrivateDeleteBrokeragePortfoliosPortfolioUuid (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteBrokeragePortfoliosPortfolioUuid",parameters);
    }

    public async Task<object> v3PrivateDeleteBrokerageCfmSweeps (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteBrokerageCfmSweeps",parameters);
    }

}