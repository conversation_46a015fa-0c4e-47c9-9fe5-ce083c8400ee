// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class poloniex : Exchange
{
    public poloniex (object args = null): base(args) {}

    public async Task<object> publicGetMarkets (object parameters = null)
    {
        return await this.callAsync ("publicGetMarkets",parameters);
    }

    public async Task<object> publicGetMarketsSymbol (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsSymbol",parameters);
    }

    public async Task<object> publicGetCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicGetCurrencies",parameters);
    }

    public async Task<object> publicGetCurrenciesCurrency (object parameters = null)
    {
        return await this.callAsync ("publicGetCurrenciesCurrency",parameters);
    }

    public async Task<object> publicGetV2Currencies (object parameters = null)
    {
        return await this.callAsync ("publicGetV2Currencies",parameters);
    }

    public async Task<object> publicGetV2CurrenciesCurrency (object parameters = null)
    {
        return await this.callAsync ("publicGetV2CurrenciesCurrency",parameters);
    }

    public async Task<object> publicGetTimestamp (object parameters = null)
    {
        return await this.callAsync ("publicGetTimestamp",parameters);
    }

    public async Task<object> publicGetMarketsPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsPrice",parameters);
    }

    public async Task<object> publicGetMarketsSymbolPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsSymbolPrice",parameters);
    }

    public async Task<object> publicGetMarketsMarkPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsMarkPrice",parameters);
    }

    public async Task<object> publicGetMarketsSymbolMarkPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsSymbolMarkPrice",parameters);
    }

    public async Task<object> publicGetMarketsSymbolMarkPriceComponents (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsSymbolMarkPriceComponents",parameters);
    }

    public async Task<object> publicGetMarketsSymbolOrderBook (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsSymbolOrderBook",parameters);
    }

    public async Task<object> publicGetMarketsSymbolCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsSymbolCandles",parameters);
    }

    public async Task<object> publicGetMarketsSymbolTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsSymbolTrades",parameters);
    }

    public async Task<object> publicGetMarketsTicker24h (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsTicker24h",parameters);
    }

    public async Task<object> publicGetMarketsSymbolTicker24h (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsSymbolTicker24h",parameters);
    }

    public async Task<object> publicGetMarketsCollateralInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsCollateralInfo",parameters);
    }

    public async Task<object> publicGetMarketsCurrencyCollateralInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsCurrencyCollateralInfo",parameters);
    }

    public async Task<object> publicGetMarketsBorrowRatesInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketsBorrowRatesInfo",parameters);
    }

    public async Task<object> privateGetAccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetAccounts",parameters);
    }

    public async Task<object> privateGetAccountsBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsBalances",parameters);
    }

    public async Task<object> privateGetAccountsIdBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsIdBalances",parameters);
    }

    public async Task<object> privateGetAccountsActivity (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsActivity",parameters);
    }

    public async Task<object> privateGetAccountsTransfer (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsTransfer",parameters);
    }

    public async Task<object> privateGetAccountsTransferId (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsTransferId",parameters);
    }

    public async Task<object> privateGetFeeinfo (object parameters = null)
    {
        return await this.callAsync ("privateGetFeeinfo",parameters);
    }

    public async Task<object> privateGetAccountsInterestHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsInterestHistory",parameters);
    }

    public async Task<object> privateGetSubaccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetSubaccounts",parameters);
    }

    public async Task<object> privateGetSubaccountsBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetSubaccountsBalances",parameters);
    }

    public async Task<object> privateGetSubaccountsIdBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetSubaccountsIdBalances",parameters);
    }

    public async Task<object> privateGetSubaccountsTransfer (object parameters = null)
    {
        return await this.callAsync ("privateGetSubaccountsTransfer",parameters);
    }

    public async Task<object> privateGetSubaccountsTransferId (object parameters = null)
    {
        return await this.callAsync ("privateGetSubaccountsTransferId",parameters);
    }

    public async Task<object> privateGetWalletsAddresses (object parameters = null)
    {
        return await this.callAsync ("privateGetWalletsAddresses",parameters);
    }

    public async Task<object> privateGetWalletsAddressesCurrency (object parameters = null)
    {
        return await this.callAsync ("privateGetWalletsAddressesCurrency",parameters);
    }

    public async Task<object> privateGetWalletsActivity (object parameters = null)
    {
        return await this.callAsync ("privateGetWalletsActivity",parameters);
    }

    public async Task<object> privateGetMarginAccountMargin (object parameters = null)
    {
        return await this.callAsync ("privateGetMarginAccountMargin",parameters);
    }

    public async Task<object> privateGetMarginBorrowStatus (object parameters = null)
    {
        return await this.callAsync ("privateGetMarginBorrowStatus",parameters);
    }

    public async Task<object> privateGetMarginMaxSize (object parameters = null)
    {
        return await this.callAsync ("privateGetMarginMaxSize",parameters);
    }

    public async Task<object> privateGetOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetOrders",parameters);
    }

    public async Task<object> privateGetOrdersId (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersId",parameters);
    }

    public async Task<object> privateGetOrdersKillSwitchStatus (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersKillSwitchStatus",parameters);
    }

    public async Task<object> privateGetSmartorders (object parameters = null)
    {
        return await this.callAsync ("privateGetSmartorders",parameters);
    }

    public async Task<object> privateGetSmartordersId (object parameters = null)
    {
        return await this.callAsync ("privateGetSmartordersId",parameters);
    }

    public async Task<object> privateGetOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersHistory",parameters);
    }

    public async Task<object> privateGetSmartordersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetSmartordersHistory",parameters);
    }

    public async Task<object> privateGetTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetTrades",parameters);
    }

    public async Task<object> privateGetOrdersIdTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersIdTrades",parameters);
    }

    public async Task<object> privatePostAccountsTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountsTransfer",parameters);
    }

    public async Task<object> privatePostSubaccountsTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostSubaccountsTransfer",parameters);
    }

    public async Task<object> privatePostWalletsAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostWalletsAddress",parameters);
    }

    public async Task<object> privatePostWalletsWithdraw (object parameters = null)
    {
        return await this.callAsync ("privatePostWalletsWithdraw",parameters);
    }

    public async Task<object> privatePostV2WalletsWithdraw (object parameters = null)
    {
        return await this.callAsync ("privatePostV2WalletsWithdraw",parameters);
    }

    public async Task<object> privatePostOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrders",parameters);
    }

    public async Task<object> privatePostOrdersBatch (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersBatch",parameters);
    }

    public async Task<object> privatePostOrdersKillSwitch (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersKillSwitch",parameters);
    }

    public async Task<object> privatePostSmartorders (object parameters = null)
    {
        return await this.callAsync ("privatePostSmartorders",parameters);
    }

    public async Task<object> privateDeleteOrdersId (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrdersId",parameters);
    }

    public async Task<object> privateDeleteOrdersCancelByIds (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrdersCancelByIds",parameters);
    }

    public async Task<object> privateDeleteOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrders",parameters);
    }

    public async Task<object> privateDeleteSmartordersId (object parameters = null)
    {
        return await this.callAsync ("privateDeleteSmartordersId",parameters);
    }

    public async Task<object> privateDeleteSmartordersCancelByIds (object parameters = null)
    {
        return await this.callAsync ("privateDeleteSmartordersCancelByIds",parameters);
    }

    public async Task<object> privateDeleteSmartorders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteSmartorders",parameters);
    }

    public async Task<object> privatePutOrdersId (object parameters = null)
    {
        return await this.callAsync ("privatePutOrdersId",parameters);
    }

    public async Task<object> privatePutSmartordersId (object parameters = null)
    {
        return await this.callAsync ("privatePutSmartordersId",parameters);
    }

    public async Task<object> swapPublicGetV3MarketAllInstruments (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketAllInstruments",parameters);
    }

    public async Task<object> swapPublicGetV3MarketInstruments (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketInstruments",parameters);
    }

    public async Task<object> swapPublicGetV3MarketOrderBook (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketOrderBook",parameters);
    }

    public async Task<object> swapPublicGetV3MarketCandles (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketCandles",parameters);
    }

    public async Task<object> swapPublicGetV3MarketIndexPriceCandlesticks (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketIndexPriceCandlesticks",parameters);
    }

    public async Task<object> swapPublicGetV3MarketPremiumIndexCandlesticks (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketPremiumIndexCandlesticks",parameters);
    }

    public async Task<object> swapPublicGetV3MarketMarkPriceCandlesticks (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketMarkPriceCandlesticks",parameters);
    }

    public async Task<object> swapPublicGetV3MarketTrades (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketTrades",parameters);
    }

    public async Task<object> swapPublicGetV3MarketLiquidationOrder (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketLiquidationOrder",parameters);
    }

    public async Task<object> swapPublicGetV3MarketTickers (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketTickers",parameters);
    }

    public async Task<object> swapPublicGetV3MarketMarkPrice (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketMarkPrice",parameters);
    }

    public async Task<object> swapPublicGetV3MarketIndexPrice (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketIndexPrice",parameters);
    }

    public async Task<object> swapPublicGetV3MarketIndexPriceComponents (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketIndexPriceComponents",parameters);
    }

    public async Task<object> swapPublicGetV3MarketFundingRate (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketFundingRate",parameters);
    }

    public async Task<object> swapPublicGetV3MarketOpenInterest (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketOpenInterest",parameters);
    }

    public async Task<object> swapPublicGetV3MarketInsurance (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketInsurance",parameters);
    }

    public async Task<object> swapPublicGetV3MarketRiskLimit (object parameters = null)
    {
        return await this.callAsync ("swapPublicGetV3MarketRiskLimit",parameters);
    }

    public async Task<object> swapPrivateGetV3AccountBalance (object parameters = null)
    {
        return await this.callAsync ("swapPrivateGetV3AccountBalance",parameters);
    }

    public async Task<object> swapPrivateGetV3AccountBills (object parameters = null)
    {
        return await this.callAsync ("swapPrivateGetV3AccountBills",parameters);
    }

    public async Task<object> swapPrivateGetV3TradeOrderOpens (object parameters = null)
    {
        return await this.callAsync ("swapPrivateGetV3TradeOrderOpens",parameters);
    }

    public async Task<object> swapPrivateGetV3TradeOrderTrades (object parameters = null)
    {
        return await this.callAsync ("swapPrivateGetV3TradeOrderTrades",parameters);
    }

    public async Task<object> swapPrivateGetV3TradeOrderHistory (object parameters = null)
    {
        return await this.callAsync ("swapPrivateGetV3TradeOrderHistory",parameters);
    }

    public async Task<object> swapPrivateGetV3TradePositionOpens (object parameters = null)
    {
        return await this.callAsync ("swapPrivateGetV3TradePositionOpens",parameters);
    }

    public async Task<object> swapPrivateGetV3TradePositionHistory (object parameters = null)
    {
        return await this.callAsync ("swapPrivateGetV3TradePositionHistory",parameters);
    }

    public async Task<object> swapPrivateGetV3PositionLeverages (object parameters = null)
    {
        return await this.callAsync ("swapPrivateGetV3PositionLeverages",parameters);
    }

    public async Task<object> swapPrivateGetV3PositionMode (object parameters = null)
    {
        return await this.callAsync ("swapPrivateGetV3PositionMode",parameters);
    }

    public async Task<object> swapPrivatePostV3TradeOrder (object parameters = null)
    {
        return await this.callAsync ("swapPrivatePostV3TradeOrder",parameters);
    }

    public async Task<object> swapPrivatePostV3TradeOrders (object parameters = null)
    {
        return await this.callAsync ("swapPrivatePostV3TradeOrders",parameters);
    }

    public async Task<object> swapPrivatePostV3TradePosition (object parameters = null)
    {
        return await this.callAsync ("swapPrivatePostV3TradePosition",parameters);
    }

    public async Task<object> swapPrivatePostV3TradePositionAll (object parameters = null)
    {
        return await this.callAsync ("swapPrivatePostV3TradePositionAll",parameters);
    }

    public async Task<object> swapPrivatePostV3PositionLeverage (object parameters = null)
    {
        return await this.callAsync ("swapPrivatePostV3PositionLeverage",parameters);
    }

    public async Task<object> swapPrivatePostV3PositionMode (object parameters = null)
    {
        return await this.callAsync ("swapPrivatePostV3PositionMode",parameters);
    }

    public async Task<object> swapPrivatePostV3TradePositionMargin (object parameters = null)
    {
        return await this.callAsync ("swapPrivatePostV3TradePositionMargin",parameters);
    }

    public async Task<object> swapPrivateDeleteV3TradeOrder (object parameters = null)
    {
        return await this.callAsync ("swapPrivateDeleteV3TradeOrder",parameters);
    }

    public async Task<object> swapPrivateDeleteV3TradeBatchOrders (object parameters = null)
    {
        return await this.callAsync ("swapPrivateDeleteV3TradeBatchOrders",parameters);
    }

    public async Task<object> swapPrivateDeleteV3TradeAllOrders (object parameters = null)
    {
        return await this.callAsync ("swapPrivateDeleteV3TradeAllOrders",parameters);
    }

}