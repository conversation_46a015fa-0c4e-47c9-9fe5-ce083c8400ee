// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class coinspot : Exchange
{
    public coinspot (object args = null): base(args) {}

    public async Task<object> publicGetLatest (object parameters = null)
    {
        return await this.callAsync ("publicGetLatest",parameters);
    }

    public async Task<object> privatePostOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrders",parameters);
    }

    public async Task<object> privatePostOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersHistory",parameters);
    }

    public async Task<object> privatePostMyCoinDeposit (object parameters = null)
    {
        return await this.callAsync ("privatePostMyCoinDeposit",parameters);
    }

    public async Task<object> privatePostMyCoinSend (object parameters = null)
    {
        return await this.callAsync ("privatePostMyCoinSend",parameters);
    }

    public async Task<object> privatePostQuoteBuy (object parameters = null)
    {
        return await this.callAsync ("privatePostQuoteBuy",parameters);
    }

    public async Task<object> privatePostQuoteSell (object parameters = null)
    {
        return await this.callAsync ("privatePostQuoteSell",parameters);
    }

    public async Task<object> privatePostMyBalances (object parameters = null)
    {
        return await this.callAsync ("privatePostMyBalances",parameters);
    }

    public async Task<object> privatePostMyOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostMyOrders",parameters);
    }

    public async Task<object> privatePostMyBuy (object parameters = null)
    {
        return await this.callAsync ("privatePostMyBuy",parameters);
    }

    public async Task<object> privatePostMySell (object parameters = null)
    {
        return await this.callAsync ("privatePostMySell",parameters);
    }

    public async Task<object> privatePostMyBuyCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostMyBuyCancel",parameters);
    }

    public async Task<object> privatePostMySellCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostMySellCancel",parameters);
    }

    public async Task<object> privatePostRoMyBalances (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyBalances",parameters);
    }

    public async Task<object> privatePostRoMyBalancesCointype (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyBalancesCointype",parameters);
    }

    public async Task<object> privatePostRoMyDeposits (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyDeposits",parameters);
    }

    public async Task<object> privatePostRoMyWithdrawals (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyWithdrawals",parameters);
    }

    public async Task<object> privatePostRoMyTransactions (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyTransactions",parameters);
    }

    public async Task<object> privatePostRoMyTransactionsCointype (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyTransactionsCointype",parameters);
    }

    public async Task<object> privatePostRoMyTransactionsOpen (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyTransactionsOpen",parameters);
    }

    public async Task<object> privatePostRoMyTransactionsCointypeOpen (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyTransactionsCointypeOpen",parameters);
    }

    public async Task<object> privatePostRoMySendreceive (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMySendreceive",parameters);
    }

    public async Task<object> privatePostRoMyAffiliatepayments (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyAffiliatepayments",parameters);
    }

    public async Task<object> privatePostRoMyReferralpayments (object parameters = null)
    {
        return await this.callAsync ("privatePostRoMyReferralpayments",parameters);
    }

}