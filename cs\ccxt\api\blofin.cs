// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class blofin : Exchange
{
    public blofin (object args = null): base(args) {}

    public async Task<object> publicGetMarketInstruments (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketInstruments",parameters);
    }

    public async Task<object> publicGetMarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTickers",parameters);
    }

    public async Task<object> publicGetMarketBooks (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketBooks",parameters);
    }

    public async Task<object> publicGetMarketTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTrades",parameters);
    }

    public async Task<object> publicGetMarketCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketCandles",parameters);
    }

    public async Task<object> publicGetMarketMarkPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketMarkPrice",parameters);
    }

    public async Task<object> publicGetMarketFundingRate (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketFundingRate",parameters);
    }

    public async Task<object> publicGetMarketFundingRateHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketFundingRateHistory",parameters);
    }

    public async Task<object> privateGetAssetBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetBalances",parameters);
    }

    public async Task<object> privateGetTradeOrdersPending (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersPending",parameters);
    }

    public async Task<object> privateGetTradeFillsHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeFillsHistory",parameters);
    }

    public async Task<object> privateGetAssetDepositHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetDepositHistory",parameters);
    }

    public async Task<object> privateGetAssetWithdrawalHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetWithdrawalHistory",parameters);
    }

    public async Task<object> privateGetAssetBills (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetBills",parameters);
    }

    public async Task<object> privateGetAccountBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBalance",parameters);
    }

    public async Task<object> privateGetAccountPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountPositions",parameters);
    }

    public async Task<object> privateGetAccountLeverageInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountLeverageInfo",parameters);
    }

    public async Task<object> privateGetAccountMarginMode (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMarginMode",parameters);
    }

    public async Task<object> privateGetAccountPositionMode (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountPositionMode",parameters);
    }

    public async Task<object> privateGetAccountBatchLeverageInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBatchLeverageInfo",parameters);
    }

    public async Task<object> privateGetTradeOrdersTpslPending (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersTpslPending",parameters);
    }

    public async Task<object> privateGetTradeOrdersAlgoPending (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersAlgoPending",parameters);
    }

    public async Task<object> privateGetTradeOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersHistory",parameters);
    }

    public async Task<object> privateGetTradeOrdersTpslHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersTpslHistory",parameters);
    }

    public async Task<object> privateGetTradeOrdersAlgoHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersAlgoHistory",parameters);
    }

    public async Task<object> privateGetTradeOrderPriceRange (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrderPriceRange",parameters);
    }

    public async Task<object> privateGetUserQueryApikey (object parameters = null)
    {
        return await this.callAsync ("privateGetUserQueryApikey",parameters);
    }

    public async Task<object> privateGetAffiliateBasic (object parameters = null)
    {
        return await this.callAsync ("privateGetAffiliateBasic",parameters);
    }

    public async Task<object> privateGetCopytradingInstruments (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingInstruments",parameters);
    }

    public async Task<object> privateGetCopytradingAccountBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingAccountBalance",parameters);
    }

    public async Task<object> privateGetCopytradingAccountPositionsByOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingAccountPositionsByOrder",parameters);
    }

    public async Task<object> privateGetCopytradingAccountPositionsDetailsByOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingAccountPositionsDetailsByOrder",parameters);
    }

    public async Task<object> privateGetCopytradingAccountPositionsByContract (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingAccountPositionsByContract",parameters);
    }

    public async Task<object> privateGetCopytradingAccountPositionMode (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingAccountPositionMode",parameters);
    }

    public async Task<object> privateGetCopytradingAccountLeverageInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingAccountLeverageInfo",parameters);
    }

    public async Task<object> privateGetCopytradingTradeOrdersPending (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingTradeOrdersPending",parameters);
    }

    public async Task<object> privateGetCopytradingTradePendingTpslByContract (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingTradePendingTpslByContract",parameters);
    }

    public async Task<object> privateGetCopytradingTradePositionHistoryByOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingTradePositionHistoryByOrder",parameters);
    }

    public async Task<object> privateGetCopytradingTradeOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingTradeOrdersHistory",parameters);
    }

    public async Task<object> privateGetCopytradingTradePendingTpslByOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingTradePendingTpslByOrder",parameters);
    }

    public async Task<object> privatePostAccountSetMarginMode (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetMarginMode",parameters);
    }

    public async Task<object> privatePostAccountSetPositionMode (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetPositionMode",parameters);
    }

    public async Task<object> privatePostTradeOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeOrder",parameters);
    }

    public async Task<object> privatePostTradeOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradeCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelOrder",parameters);
    }

    public async Task<object> privatePostTradeCancelAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelAlgo",parameters);
    }

    public async Task<object> privatePostAccountSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetLeverage",parameters);
    }

    public async Task<object> privatePostTradeBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeBatchOrders",parameters);
    }

    public async Task<object> privatePostTradeOrderTpsl (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeOrderTpsl",parameters);
    }

    public async Task<object> privatePostTradeCancelBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelBatchOrders",parameters);
    }

    public async Task<object> privatePostTradeCancelTpsl (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelTpsl",parameters);
    }

    public async Task<object> privatePostTradeClosePosition (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeClosePosition",parameters);
    }

    public async Task<object> privatePostAssetTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetTransfer",parameters);
    }

    public async Task<object> privatePostCopytradingAccountSetPositionMode (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingAccountSetPositionMode",parameters);
    }

    public async Task<object> privatePostCopytradingAccountSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingAccountSetLeverage",parameters);
    }

    public async Task<object> privatePostCopytradingTradePlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingTradePlaceOrder",parameters);
    }

    public async Task<object> privatePostCopytradingTradeCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingTradeCancelOrder",parameters);
    }

    public async Task<object> privatePostCopytradingTradePlaceTpslByContract (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingTradePlaceTpslByContract",parameters);
    }

    public async Task<object> privatePostCopytradingTradeCancelTpslByContract (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingTradeCancelTpslByContract",parameters);
    }

    public async Task<object> privatePostCopytradingTradePlaceTpslByOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingTradePlaceTpslByOrder",parameters);
    }

    public async Task<object> privatePostCopytradingTradeCancelTpslByOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingTradeCancelTpslByOrder",parameters);
    }

    public async Task<object> privatePostCopytradingTradeClosePositionByOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingTradeClosePositionByOrder",parameters);
    }

    public async Task<object> privatePostCopytradingTradeClosePositionByContract (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingTradeClosePositionByContract",parameters);
    }

}