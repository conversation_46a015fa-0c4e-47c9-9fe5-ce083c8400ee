// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class coincheck : Exchange
{
    public coincheck (object args = null): base(args) {}

    public async Task<object> publicGetExchangeOrdersRate (object parameters = null)
    {
        return await this.callAsync ("publicGetExchangeOrdersRate",parameters);
    }

    public async Task<object> publicGetOrderBooks (object parameters = null)
    {
        return await this.callAsync ("publicGetOrderBooks",parameters);
    }

    public async Task<object> publicGetRatePair (object parameters = null)
    {
        return await this.callAsync ("publicGetRatePair",parameters);
    }

    public async Task<object> publicGetTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetTicker",parameters);
    }

    public async Task<object> publicGetTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetTrades",parameters);
    }

    public async Task<object> privateGetAccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetAccounts",parameters);
    }

    public async Task<object> privateGetAccountsBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsBalance",parameters);
    }

    public async Task<object> privateGetAccountsLeverageBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsLeverageBalance",parameters);
    }

    public async Task<object> privateGetBankAccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetBankAccounts",parameters);
    }

    public async Task<object> privateGetDepositMoney (object parameters = null)
    {
        return await this.callAsync ("privateGetDepositMoney",parameters);
    }

    public async Task<object> privateGetExchangeOrdersOpens (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrdersOpens",parameters);
    }

    public async Task<object> privateGetExchangeOrdersTransactions (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrdersTransactions",parameters);
    }

    public async Task<object> privateGetExchangeOrdersTransactionsPagination (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrdersTransactionsPagination",parameters);
    }

    public async Task<object> privateGetExchangeLeveragePositions (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeLeveragePositions",parameters);
    }

    public async Task<object> privateGetLendingBorrowsMatches (object parameters = null)
    {
        return await this.callAsync ("privateGetLendingBorrowsMatches",parameters);
    }

    public async Task<object> privateGetSendMoney (object parameters = null)
    {
        return await this.callAsync ("privateGetSendMoney",parameters);
    }

    public async Task<object> privateGetWithdraws (object parameters = null)
    {
        return await this.callAsync ("privateGetWithdraws",parameters);
    }

    public async Task<object> privatePostBankAccounts (object parameters = null)
    {
        return await this.callAsync ("privatePostBankAccounts",parameters);
    }

    public async Task<object> privatePostDepositMoneyIdFast (object parameters = null)
    {
        return await this.callAsync ("privatePostDepositMoneyIdFast",parameters);
    }

    public async Task<object> privatePostExchangeOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeOrders",parameters);
    }

    public async Task<object> privatePostExchangeTransfersToLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeTransfersToLeverage",parameters);
    }

    public async Task<object> privatePostExchangeTransfersFromLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeTransfersFromLeverage",parameters);
    }

    public async Task<object> privatePostLendingBorrows (object parameters = null)
    {
        return await this.callAsync ("privatePostLendingBorrows",parameters);
    }

    public async Task<object> privatePostLendingBorrowsIdRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostLendingBorrowsIdRepay",parameters);
    }

    public async Task<object> privatePostSendMoney (object parameters = null)
    {
        return await this.callAsync ("privatePostSendMoney",parameters);
    }

    public async Task<object> privatePostWithdraws (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdraws",parameters);
    }

    public async Task<object> privateDeleteBankAccountsId (object parameters = null)
    {
        return await this.callAsync ("privateDeleteBankAccountsId",parameters);
    }

    public async Task<object> privateDeleteExchangeOrdersId (object parameters = null)
    {
        return await this.callAsync ("privateDeleteExchangeOrdersId",parameters);
    }

    public async Task<object> privateDeleteWithdrawsId (object parameters = null)
    {
        return await this.callAsync ("privateDeleteWithdrawsId",parameters);
    }

}