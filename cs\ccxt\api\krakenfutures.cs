// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class krakenfutures : Exchange
{
    public krakenfutures (object args = null): base(args) {}

    public async Task<object> publicGetFeeschedules (object parameters = null)
    {
        return await this.callAsync ("publicGetFeeschedules",parameters);
    }

    public async Task<object> publicGetInstruments (object parameters = null)
    {
        return await this.callAsync ("publicGetInstruments",parameters);
    }

    public async Task<object> publicGetOrderbook (object parameters = null)
    {
        return await this.callAsync ("publicGetOrderbook",parameters);
    }

    public async Task<object> publicGetTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetTickers",parameters);
    }

    public async Task<object> publicGetHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetHistory",parameters);
    }

    public async Task<object> publicGetHistoricalfundingrates (object parameters = null)
    {
        return await this.callAsync ("publicGetHistoricalfundingrates",parameters);
    }

    public async Task<object> privateGetFeeschedulesVolumes (object parameters = null)
    {
        return await this.callAsync ("privateGetFeeschedulesVolumes",parameters);
    }

    public async Task<object> privateGetOpenpositions (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenpositions",parameters);
    }

    public async Task<object> privateGetNotifications (object parameters = null)
    {
        return await this.callAsync ("privateGetNotifications",parameters);
    }

    public async Task<object> privateGetAccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetAccounts",parameters);
    }

    public async Task<object> privateGetOpenorders (object parameters = null)
    {
        return await this.callAsync ("privateGetOpenorders",parameters);
    }

    public async Task<object> privateGetRecentorders (object parameters = null)
    {
        return await this.callAsync ("privateGetRecentorders",parameters);
    }

    public async Task<object> privateGetFills (object parameters = null)
    {
        return await this.callAsync ("privateGetFills",parameters);
    }

    public async Task<object> privateGetTransfers (object parameters = null)
    {
        return await this.callAsync ("privateGetTransfers",parameters);
    }

    public async Task<object> privateGetLeveragepreferences (object parameters = null)
    {
        return await this.callAsync ("privateGetLeveragepreferences",parameters);
    }

    public async Task<object> privateGetPnlpreferences (object parameters = null)
    {
        return await this.callAsync ("privateGetPnlpreferences",parameters);
    }

    public async Task<object> privateGetAssignmentprogramCurrent (object parameters = null)
    {
        return await this.callAsync ("privateGetAssignmentprogramCurrent",parameters);
    }

    public async Task<object> privateGetAssignmentprogramHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssignmentprogramHistory",parameters);
    }

    public async Task<object> privatePostSendorder (object parameters = null)
    {
        return await this.callAsync ("privatePostSendorder",parameters);
    }

    public async Task<object> privatePostEditorder (object parameters = null)
    {
        return await this.callAsync ("privatePostEditorder",parameters);
    }

    public async Task<object> privatePostCancelorder (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelorder",parameters);
    }

    public async Task<object> privatePostTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostTransfer",parameters);
    }

    public async Task<object> privatePostBatchorder (object parameters = null)
    {
        return await this.callAsync ("privatePostBatchorder",parameters);
    }

    public async Task<object> privatePostCancelallorders (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelallorders",parameters);
    }

    public async Task<object> privatePostCancelallordersafter (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelallordersafter",parameters);
    }

    public async Task<object> privatePostWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdrawal",parameters);
    }

    public async Task<object> privatePostAssignmentprogramAdd (object parameters = null)
    {
        return await this.callAsync ("privatePostAssignmentprogramAdd",parameters);
    }

    public async Task<object> privatePostAssignmentprogramDelete (object parameters = null)
    {
        return await this.callAsync ("privatePostAssignmentprogramDelete",parameters);
    }

    public async Task<object> privatePutLeveragepreferences (object parameters = null)
    {
        return await this.callAsync ("privatePutLeveragepreferences",parameters);
    }

    public async Task<object> privatePutPnlpreferences (object parameters = null)
    {
        return await this.callAsync ("privatePutPnlpreferences",parameters);
    }

    public async Task<object> chartsGetPriceTypeSymbolInterval (object parameters = null)
    {
        return await this.callAsync ("chartsGetPriceTypeSymbolInterval",parameters);
    }

    public async Task<object> historyGetOrders (object parameters = null)
    {
        return await this.callAsync ("historyGetOrders",parameters);
    }

    public async Task<object> historyGetExecutions (object parameters = null)
    {
        return await this.callAsync ("historyGetExecutions",parameters);
    }

    public async Task<object> historyGetTriggers (object parameters = null)
    {
        return await this.callAsync ("historyGetTriggers",parameters);
    }

    public async Task<object> historyGetAccountlogcsv (object parameters = null)
    {
        return await this.callAsync ("historyGetAccountlogcsv",parameters);
    }

    public async Task<object> historyGetAccountLog (object parameters = null)
    {
        return await this.callAsync ("historyGetAccountLog",parameters);
    }

    public async Task<object> historyGetMarketSymbolOrders (object parameters = null)
    {
        return await this.callAsync ("historyGetMarketSymbolOrders",parameters);
    }

    public async Task<object> historyGetMarketSymbolExecutions (object parameters = null)
    {
        return await this.callAsync ("historyGetMarketSymbolExecutions",parameters);
    }

}