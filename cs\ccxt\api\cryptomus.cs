// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class cryptomus : Exchange
{
    public cryptomus (object args = null): base(args) {}

    public async Task<object> publicGetV2UserApiExchangeMarkets (object parameters = null)
    {
        return await this.callAsync ("publicGetV2UserApiExchangeMarkets",parameters);
    }

    public async Task<object> publicGetV2UserApiExchangeMarketPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetV2UserApiExchangeMarketPrice",parameters);
    }

    public async Task<object> publicGetV1ExchangeMarketAssets (object parameters = null)
    {
        return await this.callAsync ("publicGetV1ExchangeMarketAssets",parameters);
    }

    public async Task<object> publicGetV1ExchangeMarketOrderBookCurrencyPair (object parameters = null)
    {
        return await this.callAsync ("publicGetV1ExchangeMarketOrderBookCurrencyPair",parameters);
    }

    public async Task<object> publicGetV1ExchangeMarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetV1ExchangeMarketTickers",parameters);
    }

    public async Task<object> publicGetV1ExchangeMarketTradesCurrencyPair (object parameters = null)
    {
        return await this.callAsync ("publicGetV1ExchangeMarketTradesCurrencyPair",parameters);
    }

    public async Task<object> privateGetV2UserApiExchangeOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetV2UserApiExchangeOrders",parameters);
    }

    public async Task<object> privateGetV2UserApiExchangeOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV2UserApiExchangeOrdersHistory",parameters);
    }

    public async Task<object> privateGetV2UserApiExchangeAccountBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetV2UserApiExchangeAccountBalance",parameters);
    }

    public async Task<object> privateGetV2UserApiExchangeAccountTariffs (object parameters = null)
    {
        return await this.callAsync ("privateGetV2UserApiExchangeAccountTariffs",parameters);
    }

    public async Task<object> privateGetV2UserApiPaymentServices (object parameters = null)
    {
        return await this.callAsync ("privateGetV2UserApiPaymentServices",parameters);
    }

    public async Task<object> privateGetV2UserApiPayoutServices (object parameters = null)
    {
        return await this.callAsync ("privateGetV2UserApiPayoutServices",parameters);
    }

    public async Task<object> privateGetV2UserApiTransactionList (object parameters = null)
    {
        return await this.callAsync ("privateGetV2UserApiTransactionList",parameters);
    }

    public async Task<object> privatePostV2UserApiExchangeOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostV2UserApiExchangeOrders",parameters);
    }

    public async Task<object> privatePostV2UserApiExchangeOrdersMarket (object parameters = null)
    {
        return await this.callAsync ("privatePostV2UserApiExchangeOrdersMarket",parameters);
    }

    public async Task<object> privateDeleteV2UserApiExchangeOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateDeleteV2UserApiExchangeOrdersOrderId",parameters);
    }

}