// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class okcoin : Exchange
{
    public okcoin (object args = null): base(args) {}

    public async Task<object> publicGetMarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTickers",parameters);
    }

    public async Task<object> publicGetMarketTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTicker",parameters);
    }

    public async Task<object> publicGetMarketBooks (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketBooks",parameters);
    }

    public async Task<object> publicGetMarketCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketCandles",parameters);
    }

    public async Task<object> publicGetMarketHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketHistoryCandles",parameters);
    }

    public async Task<object> publicGetMarketTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTrades",parameters);
    }

    public async Task<object> publicGetMarketHistoryTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketHistoryTrades",parameters);
    }

    public async Task<object> publicGetMarketPlatform24Volume (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketPlatform24Volume",parameters);
    }

    public async Task<object> publicGetMarketOpenOracle (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketOpenOracle",parameters);
    }

    public async Task<object> publicGetMarketExchangeRate (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketExchangeRate",parameters);
    }

    public async Task<object> publicGetPublicInstruments (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicInstruments",parameters);
    }

    public async Task<object> publicGetPublicTime (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicTime",parameters);
    }

    public async Task<object> privateGetTradeOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrder",parameters);
    }

    public async Task<object> privateGetTradeOrdersPending (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersPending",parameters);
    }

    public async Task<object> privateGetTradeOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersHistory",parameters);
    }

    public async Task<object> privateGetTradeOrdersHistoryArchive (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersHistoryArchive",parameters);
    }

    public async Task<object> privateGetTradeFills (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeFills",parameters);
    }

    public async Task<object> privateGetTradeFillsHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeFillsHistory",parameters);
    }

    public async Task<object> privateGetTradeFillsArchive (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeFillsArchive",parameters);
    }

    public async Task<object> privateGetTradeOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrderAlgo",parameters);
    }

    public async Task<object> privateGetTradeOrdersAlgoPending (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersAlgoPending",parameters);
    }

    public async Task<object> privateGetTradeOrdersAlgoHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersAlgoHistory",parameters);
    }

    public async Task<object> privateGetOtcRfqTrade (object parameters = null)
    {
        return await this.callAsync ("privateGetOtcRfqTrade",parameters);
    }

    public async Task<object> privateGetOtcRfqHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetOtcRfqHistory",parameters);
    }

    public async Task<object> privateGetAccountBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBalance",parameters);
    }

    public async Task<object> privateGetAccountBills (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBills",parameters);
    }

    public async Task<object> privateGetAccountBillsArchive (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBillsArchive",parameters);
    }

    public async Task<object> privateGetAccountConfig (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountConfig",parameters);
    }

    public async Task<object> privateGetAccountMaxSize (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMaxSize",parameters);
    }

    public async Task<object> privateGetAccountMaxAvailSize (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMaxAvailSize",parameters);
    }

    public async Task<object> privateGetAccountTradeFee (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountTradeFee",parameters);
    }

    public async Task<object> privateGetAccountMaxWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMaxWithdrawal",parameters);
    }

    public async Task<object> privateGetAssetCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetCurrencies",parameters);
    }

    public async Task<object> privateGetAssetBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetBalances",parameters);
    }

    public async Task<object> privateGetAssetAssetValuation (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetAssetValuation",parameters);
    }

    public async Task<object> privateGetAssetTransferState (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetTransferState",parameters);
    }

    public async Task<object> privateGetAssetBills (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetBills",parameters);
    }

    public async Task<object> privateGetAssetDepositLightning (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetDepositLightning",parameters);
    }

    public async Task<object> privateGetAssetDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetDepositAddress",parameters);
    }

    public async Task<object> privateGetAssetDepositHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetDepositHistory",parameters);
    }

    public async Task<object> privateGetAssetWithdrawalHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetWithdrawalHistory",parameters);
    }

    public async Task<object> privateGetAssetDepositWithdrawStatus (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetDepositWithdrawStatus",parameters);
    }

    public async Task<object> privateGetFiatDepositHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetFiatDepositHistory",parameters);
    }

    public async Task<object> privateGetFiatWithdrawHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetFiatWithdrawHistory",parameters);
    }

    public async Task<object> privateGetFiatChannel (object parameters = null)
    {
        return await this.callAsync ("privateGetFiatChannel",parameters);
    }

    public async Task<object> privateGetUsersSubaccountList (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersSubaccountList",parameters);
    }

    public async Task<object> privateGetUsersSubaccountApiKey (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersSubaccountApiKey",parameters);
    }

    public async Task<object> privateGetAccountSubaccountBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubaccountBalances",parameters);
    }

    public async Task<object> privateGetAssetSubaccountBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetSubaccountBalances",parameters);
    }

    public async Task<object> privateGetAssetSubaccountBills (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetSubaccountBills",parameters);
    }

    public async Task<object> privatePostTradeOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeOrder",parameters);
    }

    public async Task<object> privatePostTradeBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeBatchOrders",parameters);
    }

    public async Task<object> privatePostTradeCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelOrder",parameters);
    }

    public async Task<object> privatePostTradeCancelBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelBatchOrders",parameters);
    }

    public async Task<object> privatePostTradeAmendOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeAmendOrder",parameters);
    }

    public async Task<object> privatePostTradeAmendBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeAmendBatchOrders",parameters);
    }

    public async Task<object> privatePostTradeOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradeCancelAlgos (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelAlgos",parameters);
    }

    public async Task<object> privatePostTradeCancelAdvanceAlgos (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelAdvanceAlgos",parameters);
    }

    public async Task<object> privatePostOtcRfqQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostOtcRfqQuote",parameters);
    }

    public async Task<object> privatePostOtcRfqTrade (object parameters = null)
    {
        return await this.callAsync ("privatePostOtcRfqTrade",parameters);
    }

    public async Task<object> privatePostAssetTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetTransfer",parameters);
    }

    public async Task<object> privatePostAssetWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetWithdrawal",parameters);
    }

    public async Task<object> privatePostAssetWithdrawalLightning (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetWithdrawalLightning",parameters);
    }

    public async Task<object> privatePostAssetWithdrawalCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetWithdrawalCancel",parameters);
    }

    public async Task<object> privatePostFiatDeposit (object parameters = null)
    {
        return await this.callAsync ("privatePostFiatDeposit",parameters);
    }

    public async Task<object> privatePostFiatCancelDeposit (object parameters = null)
    {
        return await this.callAsync ("privatePostFiatCancelDeposit",parameters);
    }

    public async Task<object> privatePostFiatWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostFiatWithdrawal",parameters);
    }

    public async Task<object> privatePostFiatCancelWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostFiatCancelWithdrawal",parameters);
    }

    public async Task<object> privatePostAssetSubaccountTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetSubaccountTransfer",parameters);
    }

}