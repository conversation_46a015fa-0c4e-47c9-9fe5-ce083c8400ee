// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class alpaca : Exchange
{
    public alpaca (object args = null): base(args) {}

    public async Task<object> traderPrivateGetV2Account (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2Account",parameters);
    }

    public async Task<object> traderPrivateGetV2Orders (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2Orders",parameters);
    }

    public async Task<object> traderPrivateGetV2OrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2OrdersOrderId",parameters);
    }

    public async Task<object> traderPrivateGetV2Positions (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2Positions",parameters);
    }

    public async Task<object> traderPrivateGetV2PositionsSymbolOrAssetId (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2PositionsSymbolOrAssetId",parameters);
    }

    public async Task<object> traderPrivateGetV2AccountPortfolioHistory (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2AccountPortfolioHistory",parameters);
    }

    public async Task<object> traderPrivateGetV2Watchlists (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2Watchlists",parameters);
    }

    public async Task<object> traderPrivateGetV2WatchlistsWatchlistId (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2WatchlistsWatchlistId",parameters);
    }

    public async Task<object> traderPrivateGetV2WatchlistsByName (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2WatchlistsByName",parameters);
    }

    public async Task<object> traderPrivateGetV2AccountConfigurations (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2AccountConfigurations",parameters);
    }

    public async Task<object> traderPrivateGetV2AccountActivities (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2AccountActivities",parameters);
    }

    public async Task<object> traderPrivateGetV2AccountActivitiesActivityType (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2AccountActivitiesActivityType",parameters);
    }

    public async Task<object> traderPrivateGetV2Calendar (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2Calendar",parameters);
    }

    public async Task<object> traderPrivateGetV2Clock (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2Clock",parameters);
    }

    public async Task<object> traderPrivateGetV2Assets (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2Assets",parameters);
    }

    public async Task<object> traderPrivateGetV2AssetsSymbolOrAssetId (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2AssetsSymbolOrAssetId",parameters);
    }

    public async Task<object> traderPrivateGetV2CorporateActionsAnnouncementsId (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2CorporateActionsAnnouncementsId",parameters);
    }

    public async Task<object> traderPrivateGetV2CorporateActionsAnnouncements (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2CorporateActionsAnnouncements",parameters);
    }

    public async Task<object> traderPrivateGetV2Wallets (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2Wallets",parameters);
    }

    public async Task<object> traderPrivateGetV2WalletsTransfers (object parameters = null)
    {
        return await this.callAsync ("traderPrivateGetV2WalletsTransfers",parameters);
    }

    public async Task<object> traderPrivatePostV2Orders (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePostV2Orders",parameters);
    }

    public async Task<object> traderPrivatePostV2Watchlists (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePostV2Watchlists",parameters);
    }

    public async Task<object> traderPrivatePostV2WatchlistsWatchlistId (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePostV2WatchlistsWatchlistId",parameters);
    }

    public async Task<object> traderPrivatePostV2WatchlistsByName (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePostV2WatchlistsByName",parameters);
    }

    public async Task<object> traderPrivatePostV2WalletsTransfers (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePostV2WalletsTransfers",parameters);
    }

    public async Task<object> traderPrivatePutV2OrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePutV2OrdersOrderId",parameters);
    }

    public async Task<object> traderPrivatePutV2WatchlistsWatchlistId (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePutV2WatchlistsWatchlistId",parameters);
    }

    public async Task<object> traderPrivatePutV2WatchlistsByName (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePutV2WatchlistsByName",parameters);
    }

    public async Task<object> traderPrivatePatchV2OrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePatchV2OrdersOrderId",parameters);
    }

    public async Task<object> traderPrivatePatchV2AccountConfigurations (object parameters = null)
    {
        return await this.callAsync ("traderPrivatePatchV2AccountConfigurations",parameters);
    }

    public async Task<object> traderPrivateDeleteV2Orders (object parameters = null)
    {
        return await this.callAsync ("traderPrivateDeleteV2Orders",parameters);
    }

    public async Task<object> traderPrivateDeleteV2OrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("traderPrivateDeleteV2OrdersOrderId",parameters);
    }

    public async Task<object> traderPrivateDeleteV2Positions (object parameters = null)
    {
        return await this.callAsync ("traderPrivateDeleteV2Positions",parameters);
    }

    public async Task<object> traderPrivateDeleteV2PositionsSymbolOrAssetId (object parameters = null)
    {
        return await this.callAsync ("traderPrivateDeleteV2PositionsSymbolOrAssetId",parameters);
    }

    public async Task<object> traderPrivateDeleteV2WatchlistsWatchlistId (object parameters = null)
    {
        return await this.callAsync ("traderPrivateDeleteV2WatchlistsWatchlistId",parameters);
    }

    public async Task<object> traderPrivateDeleteV2WatchlistsByName (object parameters = null)
    {
        return await this.callAsync ("traderPrivateDeleteV2WatchlistsByName",parameters);
    }

    public async Task<object> traderPrivateDeleteV2WatchlistsWatchlistIdSymbol (object parameters = null)
    {
        return await this.callAsync ("traderPrivateDeleteV2WatchlistsWatchlistIdSymbol",parameters);
    }

    public async Task<object> marketPublicGetV1beta3CryptoLocBars (object parameters = null)
    {
        return await this.callAsync ("marketPublicGetV1beta3CryptoLocBars",parameters);
    }

    public async Task<object> marketPublicGetV1beta3CryptoLocLatestBars (object parameters = null)
    {
        return await this.callAsync ("marketPublicGetV1beta3CryptoLocLatestBars",parameters);
    }

    public async Task<object> marketPublicGetV1beta3CryptoLocLatestOrderbooks (object parameters = null)
    {
        return await this.callAsync ("marketPublicGetV1beta3CryptoLocLatestOrderbooks",parameters);
    }

    public async Task<object> marketPublicGetV1beta3CryptoLocLatestQuotes (object parameters = null)
    {
        return await this.callAsync ("marketPublicGetV1beta3CryptoLocLatestQuotes",parameters);
    }

    public async Task<object> marketPublicGetV1beta3CryptoLocLatestTrades (object parameters = null)
    {
        return await this.callAsync ("marketPublicGetV1beta3CryptoLocLatestTrades",parameters);
    }

    public async Task<object> marketPublicGetV1beta3CryptoLocQuotes (object parameters = null)
    {
        return await this.callAsync ("marketPublicGetV1beta3CryptoLocQuotes",parameters);
    }

    public async Task<object> marketPublicGetV1beta3CryptoLocSnapshots (object parameters = null)
    {
        return await this.callAsync ("marketPublicGetV1beta3CryptoLocSnapshots",parameters);
    }

    public async Task<object> marketPublicGetV1beta3CryptoLocTrades (object parameters = null)
    {
        return await this.callAsync ("marketPublicGetV1beta3CryptoLocTrades",parameters);
    }

    public async Task<object> marketPrivateGetV1beta1CorporateActions (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV1beta1CorporateActions",parameters);
    }

    public async Task<object> marketPrivateGetV1beta1ForexLatestRates (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV1beta1ForexLatestRates",parameters);
    }

    public async Task<object> marketPrivateGetV1beta1ForexRates (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV1beta1ForexRates",parameters);
    }

    public async Task<object> marketPrivateGetV1beta1LogosSymbol (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV1beta1LogosSymbol",parameters);
    }

    public async Task<object> marketPrivateGetV1beta1News (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV1beta1News",parameters);
    }

    public async Task<object> marketPrivateGetV1beta1ScreenerStocksMostActives (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV1beta1ScreenerStocksMostActives",parameters);
    }

    public async Task<object> marketPrivateGetV1beta1ScreenerMarketTypeMovers (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV1beta1ScreenerMarketTypeMovers",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksAuctions (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksAuctions",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksBars (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksBars",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksBarsLatest (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksBarsLatest",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksMetaConditionsTicktype (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksMetaConditionsTicktype",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksMetaExchanges (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksMetaExchanges",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksQuotes (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksQuotes",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksQuotesLatest (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksQuotesLatest",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksSnapshots (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksSnapshots",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksTrades (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksTrades",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksTradesLatest (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksTradesLatest",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksSymbolAuctions (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksSymbolAuctions",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksSymbolBars (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksSymbolBars",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksSymbolBarsLatest (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksSymbolBarsLatest",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksSymbolQuotes (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksSymbolQuotes",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksSymbolQuotesLatest (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksSymbolQuotesLatest",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksSymbolSnapshot (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksSymbolSnapshot",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksSymbolTrades (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksSymbolTrades",parameters);
    }

    public async Task<object> marketPrivateGetV2StocksSymbolTradesLatest (object parameters = null)
    {
        return await this.callAsync ("marketPrivateGetV2StocksSymbolTradesLatest",parameters);
    }

}