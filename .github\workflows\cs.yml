name: C#

on:
  workflow_dispatch:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    if: ${{ !startsWith(github.event.head_commit.message, '[Automated changes]') && !contains(github.event.head_commit.message, 'Merge branch ''master'' of https://github.com/ccxt/ccxt') }}
    steps:
    - uses: actions/checkout@v4
      if: github.ref == 'refs/heads/master'
      with:
        token: ${{ secrets.GH_TOKEN }}
        fetch-depth: 2
    - uses: actions/checkout@v4
      if: github.ref != 'refs/heads/master'
      with:
        fetch-depth: 2
    - name: Skip Automated Push Commits
      id: check_commit
      run: |
        if [[ "${{ github.event.head_commit.message }}" == *"[Automated changes]"* ]]; then
          echo "This commit was made by the action. Skipping."
          exit 0
        fi
    - uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
        dotnet-quality: 'preview'
    - uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    - name: Install npm dependencies
      run: npm ci --include=dev
    - name: Determine modified files
      run: ./utils/init_actions.sh
    - name: Pre-transpile (export exchanges and emit API)
      run: npm run pre-transpile-cs
    - name: Transpile to C#
      if: env.important_modified == 'true'
      run: npm run transpileCS
    - name: Transpile to C# (specific)
      if: env.important_modified == 'false'
      run: |
        cleaned_rest_files=$(echo ${{ env.rest_files }} | tr -s ' ')
        for exchange in $cleaned_rest_files; do
          npm run transpileCsSingle -- $exchange
        done
        cleaned_ws_files=$(echo ${{ env.ws_files }} | tr -s ' ')
        for exchange in $cleaned_ws_files; do
          npm run transpileCsSingle -- --ws $exchange
        done
    - name: Build Project
      run: npm run buildCS
    - name: Run Base Tests
      if: env.important_modified == 'true'
      run: npm run test-base-rest-cs
    - name: Run Base Ws Tests
      if: env.important_modified == 'true'
      run: npm run test-base-ws-cs
    - name: Run Id Tests
      if: env.important_modified == 'true'
      run: npm run id-tests-cs
    - name: Request tests
      if: env.important_modified == 'true'
      run: npm run request-cs
    - name: Request tests (specific)
      if: env.important_modified == 'false'
      run: |
        cleaned_rest_files=$(echo ${{ env.rest_files }} | tr -s ' ')
        for exchange in $cleaned_rest_files; do
          npm run request-cs -- $exchange
        done
    - name: Response tests
      if: env.important_modified == 'true'
      run: npm run response-cs
    - name: Response tests (specific)
      if: env.important_modified == 'false'
      run: |
        cleaned_rest_files=$(echo ${{ env.rest_files }} | tr -s ' ')
        for exchange in $cleaned_rest_files; do
          npm run response-cs -- $exchange
        done
    - name: Upload shared_env.txt
      uses: actions/upload-artifact@v4
      with:
          name: shared_env
          path: shared_env.txt

    - name: Upload Cs Files
      uses: actions/upload-artifact@v4
      with:
        name: cs-files
        path: cs/
    - name: Push C# changes to master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      if: github.ref == 'refs/heads/master'
      run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git add cs/
          git commit -m "[Automated changes] C# files" || echo "No changes to commit."
          git remote set-url origin https://${GITHUB_TOKEN}@github.com/${{ github.repository }}
          git config --global pull.rebase false
          git pull --rebase --autostash origin master
          git push

  live-tests:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Download shared_env
        uses: actions/download-artifact@v4
        with:
          name: shared_env
      - uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '9.0.x'
          dotnet-quality: 'preview'
      - name: Download CS Files
        uses: actions/download-artifact@v4
        with:
          name: cs-files
          path: cs/
      - name: Restore shared_env
        run: ./utils/restore_shared_env.sh
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      - name: Install npm dependencies
        run: npm ci --include=dev
      - name: Export exchanges
        run: npm run export-exchanges
      - name: Live tests
        if: env.important_modified == 'true'
        run: ./run-tests-simul.sh --csharp
      - name: Live tests (specific)
        if: env.important_modified == 'false'
        run: ./run-tests-simul.sh --csharp "${{ env.rest_files }}" "${{ env.ws_files }}"
