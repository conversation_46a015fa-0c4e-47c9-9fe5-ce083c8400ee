// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class indodax : Exchange
{
    public indodax (object args = null): base(args) {}

    public async Task<object> publicGetApiServerTime (object parameters = null)
    {
        return await this.callAsync ("publicGetApiServerTime",parameters);
    }

    public async Task<object> publicGetApiPairs (object parameters = null)
    {
        return await this.callAsync ("publicGetApiPairs",parameters);
    }

    public async Task<object> publicGetApiPriceIncrements (object parameters = null)
    {
        return await this.callAsync ("publicGetApiPriceIncrements",parameters);
    }

    public async Task<object> publicGetApiSummaries (object parameters = null)
    {
        return await this.callAsync ("publicGetApiSummaries",parameters);
    }

    public async Task<object> publicGetApiTickerPair (object parameters = null)
    {
        return await this.callAsync ("publicGetApiTickerPair",parameters);
    }

    public async Task<object> publicGetApiTickerAll (object parameters = null)
    {
        return await this.callAsync ("publicGetApiTickerAll",parameters);
    }

    public async Task<object> publicGetApiTradesPair (object parameters = null)
    {
        return await this.callAsync ("publicGetApiTradesPair",parameters);
    }

    public async Task<object> publicGetApiDepthPair (object parameters = null)
    {
        return await this.callAsync ("publicGetApiDepthPair",parameters);
    }

    public async Task<object> publicGetTradingviewHistoryV2 (object parameters = null)
    {
        return await this.callAsync ("publicGetTradingviewHistoryV2",parameters);
    }

    public async Task<object> privatePostGetInfo (object parameters = null)
    {
        return await this.callAsync ("privatePostGetInfo",parameters);
    }

    public async Task<object> privatePostTransHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostTransHistory",parameters);
    }

    public async Task<object> privatePostTrade (object parameters = null)
    {
        return await this.callAsync ("privatePostTrade",parameters);
    }

    public async Task<object> privatePostTradeHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeHistory",parameters);
    }

    public async Task<object> privatePostOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOpenOrders",parameters);
    }

    public async Task<object> privatePostOrderHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderHistory",parameters);
    }

    public async Task<object> privatePostGetOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostGetOrder",parameters);
    }

    public async Task<object> privatePostCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelOrder",parameters);
    }

    public async Task<object> privatePostWithdrawFee (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdrawFee",parameters);
    }

    public async Task<object> privatePostWithdrawCoin (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdrawCoin",parameters);
    }

    public async Task<object> privatePostListDownline (object parameters = null)
    {
        return await this.callAsync ("privatePostListDownline",parameters);
    }

    public async Task<object> privatePostCheckDownline (object parameters = null)
    {
        return await this.callAsync ("privatePostCheckDownline",parameters);
    }

    public async Task<object> privatePostCreateVoucher (object parameters = null)
    {
        return await this.callAsync ("privatePostCreateVoucher",parameters);
    }

}