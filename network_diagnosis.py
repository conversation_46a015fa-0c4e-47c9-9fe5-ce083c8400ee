#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网络连接诊断工具
检查币安API连接问题
"""

import socket
import requests
import urllib3
import ssl
import time
from datetime import datetime
import json

def test_basic_internet():
    """测试基本网络连接"""
    print("🌐 测试基本网络连接...")
    
    test_sites = [
        ("百度", "www.baidu.com", 80),
        ("谷歌", "www.google.com", 80),
        ("GitHub", "github.com", 443),
    ]
    
    for name, host, port in test_sites:
        try:
            socket.create_connection((host, port), timeout=5)
            print(f"  ✅ {name} ({host}) - 连接成功")
        except Exception as e:
            print(f"  ❌ {name} ({host}) - 连接失败: {e}")

def test_dns_resolution():
    """测试DNS解析"""
    print("\n🔍 测试DNS解析...")
    
    hosts = [
        "api.binance.com",
        "www.binance.com",
        "stream.binance.com"
    ]
    
    for host in hosts:
        try:
            ip = socket.gethostbyname(host)
            print(f"  ✅ {host} -> {ip}")
        except Exception as e:
            print(f"  ❌ {host} - 解析失败: {e}")

def test_binance_connectivity():
    """测试币安连接"""
    print("\n💰 测试币安API连接...")
    
    endpoints = [
        ("币安主站", "https://www.binance.com"),
        ("API服务器时间", "https://api.binance.com/api/v3/time"),
        ("交易所信息", "https://api.binance.com/api/v3/exchangeInfo"),
        ("BTC/USDT行情", "https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT"),
    ]
    
    for name, url in endpoints:
        try:
            print(f"  测试 {name}...")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"    ✅ 连接成功 (状态码: {response.status_code})")
                
                # 如果是API响应，显示部分内容
                if "api.binance.com" in url:
                    try:
                        data = response.json()
                        if "serverTime" in data:
                            server_time = datetime.fromtimestamp(data["serverTime"] / 1000)
                            print(f"    📅 服务器时间: {server_time}")
                        elif "symbol" in data:
                            print(f"    💰 BTC价格: ${float(data['lastPrice']):,.2f}")
                        elif "timezone" in data:
                            print(f"    🌍 时区: {data['timezone']}")
                    except:
                        print(f"    📄 响应长度: {len(response.text)} 字符")
            else:
                print(f"    ❌ HTTP错误 (状态码: {response.status_code})")
                
        except requests.exceptions.Timeout:
            print(f"    ⏰ 连接超时")
        except requests.exceptions.ConnectionError as e:
            print(f"    🔌 连接错误: {e}")
        except Exception as e:
            print(f"    ❌ 其他错误: {e}")

def test_ssl_certificate():
    """测试SSL证书"""
    print("\n🔒 测试SSL证书...")
    
    try:
        context = ssl.create_default_context()
        with socket.create_connection(("api.binance.com", 443), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname="api.binance.com") as ssock:
                cert = ssock.getpeercert()
                print(f"  ✅ SSL证书验证成功")
                print(f"    颁发给: {cert['subject'][0][0][1]}")
                print(f"    颁发者: {cert['issuer'][1][0][1]}")
                print(f"    有效期至: {cert['notAfter']}")
    except Exception as e:
        print(f"  ❌ SSL证书验证失败: {e}")

def test_proxy_settings():
    """检查代理设置"""
    print("\n🔄 检查代理设置...")
    
    import os
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    proxy_found = False
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"  🔍 发现代理设置: {var} = {value}")
            proxy_found = True
    
    if not proxy_found:
        print(f"  ℹ️ 未发现系统代理设置")

def test_ccxt_connection():
    """测试CCXT连接"""
    print("\n🐍 测试CCXT库连接...")
    
    try:
        import ccxt
        print(f"  ✅ CCXT库版本: {ccxt.__version__}")
        
        # 创建交易所实例
        exchange = ccxt.binance({
            'enableRateLimit': True,
            'timeout': 15000,
            'options': {
                'defaultType': 'spot',
            }
        })
        
        print(f"  📡 尝试获取服务器时间...")
        server_time = exchange.fetch_time()
        server_datetime = datetime.fromtimestamp(server_time / 1000)
        print(f"  ✅ CCXT连接成功！")
        print(f"    服务器时间: {server_datetime}")
        print(f"    本地时间: {datetime.now()}")
        
        return True
        
    except ImportError:
        print(f"  ❌ CCXT库未安装")
        return False
    except Exception as e:
        print(f"  ❌ CCXT连接失败: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 可能的解决方案:")
    print("1. 检查网络连接:")
    print("   - 确保能正常访问互联网")
    print("   - 尝试访问 https://www.binance.com")
    
    print("\n2. 防火墙设置:")
    print("   - 检查Windows防火墙是否阻止Python")
    print("   - 临时关闭杀毒软件测试")
    
    print("\n3. 代理设置:")
    print("   - 如果使用代理，配置CCXT使用代理:")
    print("   exchange = ccxt.binance({")
    print("       'proxies': {")
    print("           'http': 'http://proxy:port',")
    print("           'https': 'https://proxy:port'")
    print("       }")
    print("   })")
    
    print("\n4. DNS设置:")
    print("   - 尝试更换DNS服务器 (如*******)")
    print("   - 清除DNS缓存: ipconfig /flushdns")
    
    print("\n5. 地区限制:")
    print("   - 检查您所在地区是否限制访问币安")
    print("   - 考虑使用VPN")
    
    print("\n6. 使用其他交易所:")
    print("   - 尝试其他交易所如OKX、Gate.io等")

def main():
    """主函数"""
    print("🔧 币安网络连接诊断工具")
    print("=" * 60)
    print(f"诊断时间: {datetime.now()}")
    print("=" * 60)
    
    # 运行所有测试
    test_basic_internet()
    test_dns_resolution()
    test_binance_connectivity()
    test_ssl_certificate()
    test_proxy_settings()
    ccxt_success = test_ccxt_connection()
    
    print("\n" + "=" * 60)
    if ccxt_success:
        print("🎉 诊断完成！CCXT可以正常连接币安API")
    else:
        print("❌ 诊断完成！发现连接问题")
        suggest_solutions()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 诊断被用户中断")
    except Exception as e:
        print(f"\n💥 诊断工具出错: {e}")
