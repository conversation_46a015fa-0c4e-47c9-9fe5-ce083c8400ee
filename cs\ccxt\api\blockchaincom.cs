// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class blockchaincom : Exchange
{
    public blockchaincom (object args = null): base(args) {}

    public async Task<object> publicGetTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetTickers",parameters);
    }

    public async Task<object> publicGetTickersSymbol (object parameters = null)
    {
        return await this.callAsync ("publicGetTickersSymbol",parameters);
    }

    public async Task<object> publicGetSymbols (object parameters = null)
    {
        return await this.callAsync ("publicGetSymbols",parameters);
    }

    public async Task<object> publicGetSymbolsSymbol (object parameters = null)
    {
        return await this.callAsync ("publicGetSymbolsSymbol",parameters);
    }

    public async Task<object> publicGetL2Symbol (object parameters = null)
    {
        return await this.callAsync ("publicGetL2Symbol",parameters);
    }

    public async Task<object> publicGetL3Symbol (object parameters = null)
    {
        return await this.callAsync ("publicGetL3Symbol",parameters);
    }

    public async Task<object> privateGetFees (object parameters = null)
    {
        return await this.callAsync ("privateGetFees",parameters);
    }

    public async Task<object> privateGetOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetOrders",parameters);
    }

    public async Task<object> privateGetOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersOrderId",parameters);
    }

    public async Task<object> privateGetTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetTrades",parameters);
    }

    public async Task<object> privateGetFills (object parameters = null)
    {
        return await this.callAsync ("privateGetFills",parameters);
    }

    public async Task<object> privateGetDeposits (object parameters = null)
    {
        return await this.callAsync ("privateGetDeposits",parameters);
    }

    public async Task<object> privateGetDepositsDepositId (object parameters = null)
    {
        return await this.callAsync ("privateGetDepositsDepositId",parameters);
    }

    public async Task<object> privateGetAccounts (object parameters = null)
    {
        return await this.callAsync ("privateGetAccounts",parameters);
    }

    public async Task<object> privateGetAccountsAccountCurrency (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsAccountCurrency",parameters);
    }

    public async Task<object> privateGetWhitelist (object parameters = null)
    {
        return await this.callAsync ("privateGetWhitelist",parameters);
    }

    public async Task<object> privateGetWhitelistCurrency (object parameters = null)
    {
        return await this.callAsync ("privateGetWhitelistCurrency",parameters);
    }

    public async Task<object> privateGetWithdrawals (object parameters = null)
    {
        return await this.callAsync ("privateGetWithdrawals",parameters);
    }

    public async Task<object> privateGetWithdrawalsWithdrawalId (object parameters = null)
    {
        return await this.callAsync ("privateGetWithdrawalsWithdrawalId",parameters);
    }

    public async Task<object> privatePostOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrders",parameters);
    }

    public async Task<object> privatePostDepositsCurrency (object parameters = null)
    {
        return await this.callAsync ("privatePostDepositsCurrency",parameters);
    }

    public async Task<object> privatePostWithdrawals (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdrawals",parameters);
    }

    public async Task<object> privateDeleteOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrders",parameters);
    }

    public async Task<object> privateDeleteOrdersOrderId (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrdersOrderId",parameters);
    }

}