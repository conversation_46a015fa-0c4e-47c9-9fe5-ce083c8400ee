#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
币安公开数据获取器
获取币安交易所所有币种的所有公开数据
"""

import ccxt
import asyncio
import json
import csv
import os
import time
from datetime import datetime
import pandas as pd

class BinanceDataCollector:
    def __init__(self, save_to_file=True, output_dir="binance_data"):
        """
        初始化币安数据收集器
        
        Args:
            save_to_file (bool): 是否保存数据到文件
            output_dir (str): 输出目录
        """
        self.exchange = ccxt.binance({
            'enableRateLimit': True,
            'timeout': 30000,
        })
        self.save_to_file = save_to_file
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if save_to_file:
            os.makedirs(output_dir, exist_ok=True)
    
    async def get_all_markets(self):
        """获取所有市场信息"""
        print("📊 正在获取所有市场信息...")
        try:
            markets = await self.exchange.load_markets()
            print(f"✅ 成功获取 {len(markets)} 个交易对")
            
            if self.save_to_file:
                # 保存市场信息到 JSON
                with open(f"{self.output_dir}/markets_{self.timestamp}.json", 'w', encoding='utf-8') as f:
                    json.dump(markets, f, indent=2, ensure_ascii=False, default=str)
                
                # 保存市场信息到 CSV
                market_data = []
                for symbol, market in markets.items():
                    market_data.append({
                        'symbol': symbol,
                        'base': market['base'],
                        'quote': market['quote'],
                        'active': market['active'],
                        'type': market['type'],
                        'spot': market['spot'],
                        'margin': market['margin'],
                        'future': market['future'],
                        'option': market['option'],
                        'contract': market['contract'],
                        'precision_amount': market['precision']['amount'],
                        'precision_price': market['precision']['price'],
                        'limits_amount_min': market['limits']['amount']['min'],
                        'limits_amount_max': market['limits']['amount']['max'],
                        'limits_price_min': market['limits']['price']['min'],
                        'limits_price_max': market['limits']['price']['max'],
                        'limits_cost_min': market['limits']['cost']['min'],
                        'limits_cost_max': market['limits']['cost']['max'],
                    })
                
                df = pd.DataFrame(market_data)
                df.to_csv(f"{self.output_dir}/markets_{self.timestamp}.csv", index=False, encoding='utf-8')
                print(f"💾 市场数据已保存到 {self.output_dir}/markets_{self.timestamp}.csv")
            
            return markets
        except Exception as e:
            print(f"❌ 获取市场信息失败: {e}")
            return {}
    
    async def get_all_tickers(self):
        """获取所有币种的行情数据"""
        print("📈 正在获取所有币种行情数据...")
        try:
            tickers = await self.exchange.fetch_tickers()
            print(f"✅ 成功获取 {len(tickers)} 个币种的行情数据")
            
            if self.save_to_file:
                # 保存到 JSON
                with open(f"{self.output_dir}/tickers_{self.timestamp}.json", 'w', encoding='utf-8') as f:
                    json.dump(tickers, f, indent=2, ensure_ascii=False, default=str)
                
                # 保存到 CSV
                ticker_data = []
                for symbol, ticker in tickers.items():
                    ticker_data.append({
                        'symbol': symbol,
                        'timestamp': ticker['timestamp'],
                        'datetime': ticker['datetime'],
                        'high': ticker['high'],
                        'low': ticker['low'],
                        'bid': ticker['bid'],
                        'bidVolume': ticker['bidVolume'],
                        'ask': ticker['ask'],
                        'askVolume': ticker['askVolume'],
                        'vwap': ticker['vwap'],
                        'open': ticker['open'],
                        'close': ticker['close'],
                        'last': ticker['last'],
                        'previousClose': ticker['previousClose'],
                        'change': ticker['change'],
                        'percentage': ticker['percentage'],
                        'average': ticker['average'],
                        'baseVolume': ticker['baseVolume'],
                        'quoteVolume': ticker['quoteVolume'],
                    })
                
                df = pd.DataFrame(ticker_data)
                df.to_csv(f"{self.output_dir}/tickers_{self.timestamp}.csv", index=False, encoding='utf-8')
                print(f"💾 行情数据已保存到 {self.output_dir}/tickers_{self.timestamp}.csv")
            
            return tickers
        except Exception as e:
            print(f"❌ 获取行情数据失败: {e}")
            return {}
    
    async def get_orderbooks(self, symbols=None, limit=100):
        """获取订单簿数据"""
        if symbols is None:
            markets = await self.exchange.load_markets()
            # 只获取活跃的现货交易对的前50个，避免请求过多
            symbols = [symbol for symbol, market in markets.items() 
                      if market['active'] and market['spot']][:50]
        
        print(f"📖 正在获取 {len(symbols)} 个交易对的订单簿数据...")
        orderbooks = {}
        
        for i, symbol in enumerate(symbols, 1):
            try:
                print(f"  {i}/{len(symbols)} 获取 {symbol} 订单簿...")
                orderbook = await self.exchange.fetch_order_book(symbol, limit)
                orderbooks[symbol] = orderbook
                
                # 添加延迟避免触发速率限制
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"    ❌ 获取 {symbol} 订单簿失败: {e}")
                continue
        
        print(f"✅ 成功获取 {len(orderbooks)} 个订单簿")
        
        if self.save_to_file and orderbooks:
            # 保存到 JSON
            with open(f"{self.output_dir}/orderbooks_{self.timestamp}.json", 'w', encoding='utf-8') as f:
                json.dump(orderbooks, f, indent=2, ensure_ascii=False, default=str)
            
            # 保存到 CSV（展开格式）
            orderbook_data = []
            for symbol, orderbook in orderbooks.items():
                # 买单
                for i, (price, amount) in enumerate(orderbook['bids'][:10]):  # 只保存前10档
                    orderbook_data.append({
                        'symbol': symbol,
                        'side': 'bid',
                        'level': i + 1,
                        'price': price,
                        'amount': amount,
                        'timestamp': orderbook['timestamp'],
                        'datetime': orderbook['datetime']
                    })
                
                # 卖单
                for i, (price, amount) in enumerate(orderbook['asks'][:10]):  # 只保存前10档
                    orderbook_data.append({
                        'symbol': symbol,
                        'side': 'ask',
                        'level': i + 1,
                        'price': price,
                        'amount': amount,
                        'timestamp': orderbook['timestamp'],
                        'datetime': orderbook['datetime']
                    })
            
            df = pd.DataFrame(orderbook_data)
            df.to_csv(f"{self.output_dir}/orderbooks_{self.timestamp}.csv", index=False, encoding='utf-8')
            print(f"💾 订单簿数据已保存到 {self.output_dir}/orderbooks_{self.timestamp}.csv")
        
        return orderbooks
    
    async def get_recent_trades(self, symbols=None, limit=100):
        """获取最近交易数据"""
        if symbols is None:
            markets = await self.exchange.load_markets()
            # 只获取前20个活跃现货交易对
            symbols = [symbol for symbol, market in markets.items() 
                      if market['active'] and market['spot']][:20]
        
        print(f"💱 正在获取 {len(symbols)} 个交易对的最近交易数据...")
        all_trades = {}
        
        for i, symbol in enumerate(symbols, 1):
            try:
                print(f"  {i}/{len(symbols)} 获取 {symbol} 交易数据...")
                trades = await self.exchange.fetch_trades(symbol, limit=limit)
                all_trades[symbol] = trades
                
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"    ❌ 获取 {symbol} 交易数据失败: {e}")
                continue
        
        print(f"✅ 成功获取 {len(all_trades)} 个交易对的交易数据")
        
        if self.save_to_file and all_trades:
            # 保存到 JSON
            with open(f"{self.output_dir}/trades_{self.timestamp}.json", 'w', encoding='utf-8') as f:
                json.dump(all_trades, f, indent=2, ensure_ascii=False, default=str)
            
            # 保存到 CSV
            trade_data = []
            for symbol, trades in all_trades.items():
                for trade in trades:
                    trade_data.append({
                        'symbol': symbol,
                        'id': trade['id'],
                        'timestamp': trade['timestamp'],
                        'datetime': trade['datetime'],
                        'amount': trade['amount'],
                        'price': trade['price'],
                        'cost': trade['cost'],
                        'side': trade['side'],
                        'type': trade['type'],
                        'takerOrMaker': trade['takerOrMaker']
                    })
            
            df = pd.DataFrame(trade_data)
            df.to_csv(f"{self.output_dir}/trades_{self.timestamp}.csv", index=False, encoding='utf-8')
            print(f"💾 交易数据已保存到 {self.output_dir}/trades_{self.timestamp}.csv")
        
        return all_trades
    
    async def get_exchange_info(self):
        """获取交易所基本信息"""
        print("ℹ️ 正在获取交易所信息...")
        
        info = {
            'exchange_id': self.exchange.id,
            'exchange_name': self.exchange.name,
            'version': getattr(self.exchange, 'version', 'N/A'),
            'rateLimit': self.exchange.rateLimit,
            'has': self.exchange.has,
            'timeframes': getattr(self.exchange, 'timeframes', {}),
            'urls': self.exchange.urls,
            'api': self.exchange.api,
            'fees': self.exchange.fees,
            'timestamp': int(time.time() * 1000),
            'datetime': datetime.now().isoformat()
        }
        
        if self.save_to_file:
            with open(f"{self.output_dir}/exchange_info_{self.timestamp}.json", 'w', encoding='utf-8') as f:
                json.dump(info, f, indent=2, ensure_ascii=False, default=str)
            print(f"💾 交易所信息已保存到 {self.output_dir}/exchange_info_{self.timestamp}.json")
        
        return info
    
    async def get_ohlcv_data(self, symbols=None, timeframe='1h', limit=100):
        """获取K线数据"""
        if symbols is None:
            markets = await self.exchange.load_markets()
            # 只获取前10个主要交易对的K线数据
            major_symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'XRP/USDT',
                           'SOL/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'MATIC/USDT']
            symbols = [s for s in major_symbols if s in markets]

        print(f"📊 正在获取 {len(symbols)} 个交易对的K线数据 ({timeframe})...")
        ohlcv_data = {}

        for i, symbol in enumerate(symbols, 1):
            try:
                print(f"  {i}/{len(symbols)} 获取 {symbol} K线数据...")
                ohlcv = await self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
                ohlcv_data[symbol] = ohlcv

                await asyncio.sleep(0.2)

            except Exception as e:
                print(f"    ❌ 获取 {symbol} K线数据失败: {e}")
                continue

        print(f"✅ 成功获取 {len(ohlcv_data)} 个交易对的K线数据")

        if self.save_to_file and ohlcv_data:
            # 保存到 JSON
            with open(f"{self.output_dir}/ohlcv_{timeframe}_{self.timestamp}.json", 'w', encoding='utf-8') as f:
                json.dump(ohlcv_data, f, indent=2, ensure_ascii=False, default=str)

            # 保存到 CSV
            kline_data = []
            for symbol, ohlcv_list in ohlcv_data.items():
                for ohlcv in ohlcv_list:
                    kline_data.append({
                        'symbol': symbol,
                        'timestamp': ohlcv[0],
                        'datetime': datetime.fromtimestamp(ohlcv[0] / 1000).isoformat(),
                        'open': ohlcv[1],
                        'high': ohlcv[2],
                        'low': ohlcv[3],
                        'close': ohlcv[4],
                        'volume': ohlcv[5],
                        'timeframe': timeframe
                    })

            df = pd.DataFrame(kline_data)
            df.to_csv(f"{self.output_dir}/ohlcv_{timeframe}_{self.timestamp}.csv", index=False, encoding='utf-8')
            print(f"💾 K线数据已保存到 {self.output_dir}/ohlcv_{timeframe}_{self.timestamp}.csv")

        return ohlcv_data

    async def collect_all_data(self):
        """收集所有数据"""
        print("🚀 开始收集币安所有公开数据...")
        print(f"📅 时间戳: {self.timestamp}")
        print("=" * 60)

        try:
            # 1. 获取交易所信息
            exchange_info = await self.get_exchange_info()

            # 2. 获取所有市场信息
            markets = await self.get_all_markets()

            # 3. 获取所有行情数据
            tickers = await self.get_all_tickers()

            # 4. 获取部分订单簿数据
            orderbooks = await self.get_orderbooks()

            # 5. 获取部分交易数据
            trades = await self.get_recent_trades()

            # 6. 获取K线数据
            ohlcv_1h = await self.get_ohlcv_data(timeframe='1h', limit=24)  # 24小时数据
            ohlcv_1d = await self.get_ohlcv_data(timeframe='1d', limit=30)  # 30天数据

            print("=" * 60)
            print("📊 数据收集完成！")
            print(f"  - 交易对数量: {len(markets)}")
            print(f"  - 行情数据: {len(tickers)}")
            print(f"  - 订单簿数据: {len(orderbooks)}")
            print(f"  - 交易数据: {len(trades)}")
            print(f"  - 1小时K线: {len(ohlcv_1h)}")
            print(f"  - 1日K线: {len(ohlcv_1d)}")

            if self.save_to_file:
                print(f"  - 数据保存目录: {self.output_dir}")

            return {
                'exchange_info': exchange_info,
                'markets': markets,
                'tickers': tickers,
                'orderbooks': orderbooks,
                'trades': trades,
                'ohlcv_1h': ohlcv_1h,
                'ohlcv_1d': ohlcv_1d
            }

        except Exception as e:
            print(f"❌ 数据收集过程中出错: {e}")
            return None

        finally:
            await self.exchange.close()

async def main():
    """主函数"""
    print("🔗 币安公开数据收集器")
    print("=" * 60)
    
    # 创建数据收集器
    collector = BinanceDataCollector(save_to_file=True)
    
    # 收集所有数据
    data = await collector.collect_all_data()
    
    if data:
        print("\n✅ 所有数据收集完成！")
        print("📁 查看输出目录中的文件获取详细数据")
    else:
        print("\n❌ 数据收集失败")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")
