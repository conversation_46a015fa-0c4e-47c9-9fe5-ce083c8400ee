// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class novadax : Exchange
{
    public novadax (object args = null): base(args) {}

    public async Task<object> publicGetCommonSymbol (object parameters = null)
    {
        return await this.callAsync ("publicGetCommonSymbol",parameters);
    }

    public async Task<object> publicGetCommonSymbols (object parameters = null)
    {
        return await this.callAsync ("publicGetCommonSymbols",parameters);
    }

    public async Task<object> publicGetCommonTimestamp (object parameters = null)
    {
        return await this.callAsync ("publicGetCommonTimestamp",parameters);
    }

    public async Task<object> publicGetMarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTickers",parameters);
    }

    public async Task<object> publicGetMarketTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTicker",parameters);
    }

    public async Task<object> publicGetMarketDepth (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketDepth",parameters);
    }

    public async Task<object> publicGetMarketTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTrades",parameters);
    }

    public async Task<object> publicGetMarketKlineHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketKlineHistory",parameters);
    }

    public async Task<object> privateGetOrdersGet (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersGet",parameters);
    }

    public async Task<object> privateGetOrdersList (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersList",parameters);
    }

    public async Task<object> privateGetOrdersFill (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersFill",parameters);
    }

    public async Task<object> privateGetOrdersFills (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersFills",parameters);
    }

    public async Task<object> privateGetAccountGetBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountGetBalance",parameters);
    }

    public async Task<object> privateGetAccountSubs (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubs",parameters);
    }

    public async Task<object> privateGetAccountSubsBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubsBalance",parameters);
    }

    public async Task<object> privateGetAccountSubsTransferRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubsTransferRecord",parameters);
    }

    public async Task<object> privateGetWalletQueryDepositWithdraw (object parameters = null)
    {
        return await this.callAsync ("privateGetWalletQueryDepositWithdraw",parameters);
    }

    public async Task<object> privatePostOrdersCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersCreate",parameters);
    }

    public async Task<object> privatePostOrdersBatchCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersBatchCreate",parameters);
    }

    public async Task<object> privatePostOrdersCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersCancel",parameters);
    }

    public async Task<object> privatePostOrdersBatchCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersBatchCancel",parameters);
    }

    public async Task<object> privatePostOrdersCancelBySymbol (object parameters = null)
    {
        return await this.callAsync ("privatePostOrdersCancelBySymbol",parameters);
    }

    public async Task<object> privatePostAccountSubsTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSubsTransfer",parameters);
    }

    public async Task<object> privatePostWalletWithdrawCoin (object parameters = null)
    {
        return await this.callAsync ("privatePostWalletWithdrawCoin",parameters);
    }

    public async Task<object> privatePostAccountWithdrawCoin (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountWithdrawCoin",parameters);
    }

}