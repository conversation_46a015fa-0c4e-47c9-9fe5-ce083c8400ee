// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class coinone : Exchange
{
    public coinone (object args = null): base(args) {}

    public async Task<object> publicGetOrderbook (object parameters = null)
    {
        return await this.callAsync ("publicGetOrderbook",parameters);
    }

    public async Task<object> publicGetTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetTicker",parameters);
    }

    public async Task<object> publicGetTickerUtc (object parameters = null)
    {
        return await this.callAsync ("publicGetTickerUtc",parameters);
    }

    public async Task<object> publicGetTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetTrades",parameters);
    }

    public async Task<object> v2PublicGetRangeUnits (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetRangeUnits",parameters);
    }

    public async Task<object> v2PublicGetMarketsQuoteCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetMarketsQuoteCurrency",parameters);
    }

    public async Task<object> v2PublicGetMarketsQuoteCurrencyTargetCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetMarketsQuoteCurrencyTargetCurrency",parameters);
    }

    public async Task<object> v2PublicGetOrderbookQuoteCurrencyTargetCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetOrderbookQuoteCurrencyTargetCurrency",parameters);
    }

    public async Task<object> v2PublicGetTradesQuoteCurrencyTargetCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetTradesQuoteCurrencyTargetCurrency",parameters);
    }

    public async Task<object> v2PublicGetTickerNewQuoteCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetTickerNewQuoteCurrency",parameters);
    }

    public async Task<object> v2PublicGetTickerNewQuoteCurrencyTargetCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetTickerNewQuoteCurrencyTargetCurrency",parameters);
    }

    public async Task<object> v2PublicGetTickerUtcNewQuoteCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetTickerUtcNewQuoteCurrency",parameters);
    }

    public async Task<object> v2PublicGetTickerUtcNewQuoteCurrencyTargetCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetTickerUtcNewQuoteCurrencyTargetCurrency",parameters);
    }

    public async Task<object> v2PublicGetCurrencies (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetCurrencies",parameters);
    }

    public async Task<object> v2PublicGetCurrenciesCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetCurrenciesCurrency",parameters);
    }

    public async Task<object> v2PublicGetChartQuoteCurrencyTargetCurrency (object parameters = null)
    {
        return await this.callAsync ("v2PublicGetChartQuoteCurrencyTargetCurrency",parameters);
    }

    public async Task<object> privatePostAccountDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountDepositAddress",parameters);
    }

    public async Task<object> privatePostAccountBtcDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountBtcDepositAddress",parameters);
    }

    public async Task<object> privatePostAccountBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountBalance",parameters);
    }

    public async Task<object> privatePostAccountDailyBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountDailyBalance",parameters);
    }

    public async Task<object> privatePostAccountUserInfo (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountUserInfo",parameters);
    }

    public async Task<object> privatePostAccountVirtualAccount (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountVirtualAccount",parameters);
    }

    public async Task<object> privatePostOrderCancelAll (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderCancelAll",parameters);
    }

    public async Task<object> privatePostOrderCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderCancel",parameters);
    }

    public async Task<object> privatePostOrderLimitBuy (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderLimitBuy",parameters);
    }

    public async Task<object> privatePostOrderLimitSell (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderLimitSell",parameters);
    }

    public async Task<object> privatePostOrderCompleteOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderCompleteOrders",parameters);
    }

    public async Task<object> privatePostOrderLimitOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderLimitOrders",parameters);
    }

    public async Task<object> privatePostOrderOrderInfo (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderOrderInfo",parameters);
    }

    public async Task<object> privatePostTransactionAuthNumber (object parameters = null)
    {
        return await this.callAsync ("privatePostTransactionAuthNumber",parameters);
    }

    public async Task<object> privatePostTransactionHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostTransactionHistory",parameters);
    }

    public async Task<object> privatePostTransactionKrwHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostTransactionKrwHistory",parameters);
    }

    public async Task<object> privatePostTransactionBtc (object parameters = null)
    {
        return await this.callAsync ("privatePostTransactionBtc",parameters);
    }

    public async Task<object> privatePostTransactionCoin (object parameters = null)
    {
        return await this.callAsync ("privatePostTransactionCoin",parameters);
    }

    public async Task<object> v2PrivatePostAccountBalance (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountBalance",parameters);
    }

    public async Task<object> v2PrivatePostAccountDepositAddress (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountDepositAddress",parameters);
    }

    public async Task<object> v2PrivatePostAccountUserInfo (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountUserInfo",parameters);
    }

    public async Task<object> v2PrivatePostAccountVirtualAccount (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostAccountVirtualAccount",parameters);
    }

    public async Task<object> v2PrivatePostOrderCancel (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostOrderCancel",parameters);
    }

    public async Task<object> v2PrivatePostOrderLimitBuy (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostOrderLimitBuy",parameters);
    }

    public async Task<object> v2PrivatePostOrderLimitSell (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostOrderLimitSell",parameters);
    }

    public async Task<object> v2PrivatePostOrderLimitOrders (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostOrderLimitOrders",parameters);
    }

    public async Task<object> v2PrivatePostOrderCompleteOrders (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostOrderCompleteOrders",parameters);
    }

    public async Task<object> v2PrivatePostOrderQueryOrder (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostOrderQueryOrder",parameters);
    }

    public async Task<object> v2PrivatePostTransactionAuthNumber (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostTransactionAuthNumber",parameters);
    }

    public async Task<object> v2PrivatePostTransactionBtc (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostTransactionBtc",parameters);
    }

    public async Task<object> v2PrivatePostTransactionHistory (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostTransactionHistory",parameters);
    }

    public async Task<object> v2PrivatePostTransactionKrwHistory (object parameters = null)
    {
        return await this.callAsync ("v2PrivatePostTransactionKrwHistory",parameters);
    }

    public async Task<object> v2_1PrivatePostAccountBalanceAll (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostAccountBalanceAll",parameters);
    }

    public async Task<object> v2_1PrivatePostAccountBalance (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostAccountBalance",parameters);
    }

    public async Task<object> v2_1PrivatePostAccountTradeFee (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostAccountTradeFee",parameters);
    }

    public async Task<object> v2_1PrivatePostAccountTradeFeeQuoteCurrencyTargetCurrency (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostAccountTradeFeeQuoteCurrencyTargetCurrency",parameters);
    }

    public async Task<object> v2_1PrivatePostOrderLimit (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostOrderLimit",parameters);
    }

    public async Task<object> v2_1PrivatePostOrderCancel (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostOrderCancel",parameters);
    }

    public async Task<object> v2_1PrivatePostOrderCancelAll (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostOrderCancelAll",parameters);
    }

    public async Task<object> v2_1PrivatePostOrderOpenOrders (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostOrderOpenOrders",parameters);
    }

    public async Task<object> v2_1PrivatePostOrderOpenOrdersAll (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostOrderOpenOrdersAll",parameters);
    }

    public async Task<object> v2_1PrivatePostOrderCompleteOrders (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostOrderCompleteOrders",parameters);
    }

    public async Task<object> v2_1PrivatePostOrderCompleteOrdersAll (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostOrderCompleteOrdersAll",parameters);
    }

    public async Task<object> v2_1PrivatePostOrderInfo (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostOrderInfo",parameters);
    }

    public async Task<object> v2_1PrivatePostTransactionKrwHistory (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostTransactionKrwHistory",parameters);
    }

    public async Task<object> v2_1PrivatePostTransactionCoinHistory (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostTransactionCoinHistory",parameters);
    }

    public async Task<object> v2_1PrivatePostTransactionCoinWithdrawalLimit (object parameters = null)
    {
        return await this.callAsync ("v2_1PrivatePostTransactionCoinWithdrawalLimit",parameters);
    }

}