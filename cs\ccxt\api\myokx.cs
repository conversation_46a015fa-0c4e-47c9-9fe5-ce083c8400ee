// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class myokx : okx
{
    public myokx (object args = null): base(args) {}

    public async Task<object> publicGetMarketBooksFull (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketBooksFull",parameters);
    }

    public async Task<object> publicGetMarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTickers",parameters);
    }

    public async Task<object> publicGetMarketTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTicker",parameters);
    }

    public async Task<object> publicGetMarketIndexTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketIndexTickers",parameters);
    }

    public async Task<object> publicGetMarketBooks (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketBooks",parameters);
    }

    public async Task<object> publicGetMarketBooksLite (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketBooksLite",parameters);
    }

    public async Task<object> publicGetMarketCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketCandles",parameters);
    }

    public async Task<object> publicGetMarketHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketHistoryCandles",parameters);
    }

    public async Task<object> publicGetMarketIndexCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketIndexCandles",parameters);
    }

    public async Task<object> publicGetMarketHistoryIndexCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketHistoryIndexCandles",parameters);
    }

    public async Task<object> publicGetMarketMarkPriceCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketMarkPriceCandles",parameters);
    }

    public async Task<object> publicGetMarketHistoryMarkPriceCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketHistoryMarkPriceCandles",parameters);
    }

    public async Task<object> publicGetMarketTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketTrades",parameters);
    }

    public async Task<object> publicGetMarketHistoryTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketHistoryTrades",parameters);
    }

    public async Task<object> publicGetMarketOptionInstrumentFamilyTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketOptionInstrumentFamilyTrades",parameters);
    }

    public async Task<object> publicGetMarketPlatform24Volume (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketPlatform24Volume",parameters);
    }

    public async Task<object> publicGetMarketOpenOracle (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketOpenOracle",parameters);
    }

    public async Task<object> publicGetMarketExchangeRate (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketExchangeRate",parameters);
    }

    public async Task<object> publicGetMarketIndexComponents (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketIndexComponents",parameters);
    }

    public async Task<object> publicGetPublicEconomicCalendar (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicEconomicCalendar",parameters);
    }

    public async Task<object> publicGetMarketBlockTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketBlockTickers",parameters);
    }

    public async Task<object> publicGetMarketBlockTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketBlockTicker",parameters);
    }

    public async Task<object> publicGetPublicBlockTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicBlockTrades",parameters);
    }

    public async Task<object> publicGetPublicInstruments (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicInstruments",parameters);
    }

    public async Task<object> publicGetPublicDeliveryExerciseHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicDeliveryExerciseHistory",parameters);
    }

    public async Task<object> publicGetPublicOpenInterest (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicOpenInterest",parameters);
    }

    public async Task<object> publicGetPublicFundingRate (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicFundingRate",parameters);
    }

    public async Task<object> publicGetPublicFundingRateHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicFundingRateHistory",parameters);
    }

    public async Task<object> publicGetPublicPriceLimit (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicPriceLimit",parameters);
    }

    public async Task<object> publicGetPublicOptSummary (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicOptSummary",parameters);
    }

    public async Task<object> publicGetPublicEstimatedPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicEstimatedPrice",parameters);
    }

    public async Task<object> publicGetPublicDiscountRateInterestFreeQuota (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicDiscountRateInterestFreeQuota",parameters);
    }

    public async Task<object> publicGetPublicTime (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicTime",parameters);
    }

    public async Task<object> publicGetPublicMarkPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicMarkPrice",parameters);
    }

    public async Task<object> publicGetPublicPositionTiers (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicPositionTiers",parameters);
    }

    public async Task<object> publicGetPublicInterestRateLoanQuota (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicInterestRateLoanQuota",parameters);
    }

    public async Task<object> publicGetPublicVipInterestRateLoanQuota (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicVipInterestRateLoanQuota",parameters);
    }

    public async Task<object> publicGetPublicUnderlying (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicUnderlying",parameters);
    }

    public async Task<object> publicGetPublicInsuranceFund (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicInsuranceFund",parameters);
    }

    public async Task<object> publicGetPublicConvertContractCoin (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicConvertContractCoin",parameters);
    }

    public async Task<object> publicGetPublicOptionTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicOptionTrades",parameters);
    }

    public async Task<object> publicGetPublicInstrumentTickBands (object parameters = null)
    {
        return await this.callAsync ("publicGetPublicInstrumentTickBands",parameters);
    }

    public async Task<object> publicGetRubikStatTradingDataSupportCoin (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatTradingDataSupportCoin",parameters);
    }

    public async Task<object> publicGetRubikStatTakerVolume (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatTakerVolume",parameters);
    }

    public async Task<object> publicGetRubikStatMarginLoanRatio (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatMarginLoanRatio",parameters);
    }

    public async Task<object> publicGetRubikStatContractsLongShortAccountRatio (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatContractsLongShortAccountRatio",parameters);
    }

    public async Task<object> publicGetRubikStatContractsLongShortAccountRatioContract (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatContractsLongShortAccountRatioContract",parameters);
    }

    public async Task<object> publicGetRubikStatContractsOpenInterestVolume (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatContractsOpenInterestVolume",parameters);
    }

    public async Task<object> publicGetRubikStatOptionOpenInterestVolume (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatOptionOpenInterestVolume",parameters);
    }

    public async Task<object> publicGetRubikStatOptionOpenInterestVolumeRatio (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatOptionOpenInterestVolumeRatio",parameters);
    }

    public async Task<object> publicGetRubikStatOptionOpenInterestVolumeExpiry (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatOptionOpenInterestVolumeExpiry",parameters);
    }

    public async Task<object> publicGetRubikStatOptionOpenInterestVolumeStrike (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatOptionOpenInterestVolumeStrike",parameters);
    }

    public async Task<object> publicGetRubikStatOptionTakerBlockVolume (object parameters = null)
    {
        return await this.callAsync ("publicGetRubikStatOptionTakerBlockVolume",parameters);
    }

    public async Task<object> publicGetSystemStatus (object parameters = null)
    {
        return await this.callAsync ("publicGetSystemStatus",parameters);
    }

    public async Task<object> publicGetSprdSpreads (object parameters = null)
    {
        return await this.callAsync ("publicGetSprdSpreads",parameters);
    }

    public async Task<object> publicGetSprdBooks (object parameters = null)
    {
        return await this.callAsync ("publicGetSprdBooks",parameters);
    }

    public async Task<object> publicGetSprdTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetSprdTicker",parameters);
    }

    public async Task<object> publicGetSprdPublicTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetSprdPublicTrades",parameters);
    }

    public async Task<object> publicGetMarketSprdTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketSprdTicker",parameters);
    }

    public async Task<object> publicGetMarketSprdCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketSprdCandles",parameters);
    }

    public async Task<object> publicGetMarketSprdHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicGetMarketSprdHistoryCandles",parameters);
    }

    public async Task<object> publicGetTradingBotGridAiParam (object parameters = null)
    {
        return await this.callAsync ("publicGetTradingBotGridAiParam",parameters);
    }

    public async Task<object> publicGetTradingBotGridMinInvestment (object parameters = null)
    {
        return await this.callAsync ("publicGetTradingBotGridMinInvestment",parameters);
    }

    public async Task<object> publicGetTradingBotPublicRsiBackTesting (object parameters = null)
    {
        return await this.callAsync ("publicGetTradingBotPublicRsiBackTesting",parameters);
    }

    public async Task<object> publicGetAssetExchangeList (object parameters = null)
    {
        return await this.callAsync ("publicGetAssetExchangeList",parameters);
    }

    public async Task<object> publicGetFinanceStakingDefiEthApyHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetFinanceStakingDefiEthApyHistory",parameters);
    }

    public async Task<object> publicGetFinanceStakingDefiSolApyHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetFinanceStakingDefiSolApyHistory",parameters);
    }

    public async Task<object> publicGetFinanceSavingsLendingRateSummary (object parameters = null)
    {
        return await this.callAsync ("publicGetFinanceSavingsLendingRateSummary",parameters);
    }

    public async Task<object> publicGetFinanceSavingsLendingRateHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetFinanceSavingsLendingRateHistory",parameters);
    }

    public async Task<object> publicGetFinanceFixedLoanLendingOffers (object parameters = null)
    {
        return await this.callAsync ("publicGetFinanceFixedLoanLendingOffers",parameters);
    }

    public async Task<object> publicGetFinanceFixedLoanLendingApyHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetFinanceFixedLoanLendingApyHistory",parameters);
    }

    public async Task<object> publicGetFinanceFixedLoanPendingLendingVolume (object parameters = null)
    {
        return await this.callAsync ("publicGetFinanceFixedLoanPendingLendingVolume",parameters);
    }

    public async Task<object> publicGetFinanceSfpDcdProducts (object parameters = null)
    {
        return await this.callAsync ("publicGetFinanceSfpDcdProducts",parameters);
    }

    public async Task<object> publicGetCopytradingPublicLeadTraders (object parameters = null)
    {
        return await this.callAsync ("publicGetCopytradingPublicLeadTraders",parameters);
    }

    public async Task<object> publicGetCopytradingPublicWeeklyPnl (object parameters = null)
    {
        return await this.callAsync ("publicGetCopytradingPublicWeeklyPnl",parameters);
    }

    public async Task<object> publicGetCopytradingPublicStats (object parameters = null)
    {
        return await this.callAsync ("publicGetCopytradingPublicStats",parameters);
    }

    public async Task<object> publicGetCopytradingPublicPreferenceCurrency (object parameters = null)
    {
        return await this.callAsync ("publicGetCopytradingPublicPreferenceCurrency",parameters);
    }

    public async Task<object> publicGetCopytradingPublicCurrentSubpositions (object parameters = null)
    {
        return await this.callAsync ("publicGetCopytradingPublicCurrentSubpositions",parameters);
    }

    public async Task<object> publicGetCopytradingPublicSubpositionsHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetCopytradingPublicSubpositionsHistory",parameters);
    }

    public async Task<object> publicGetSupportAnnouncementsTypes (object parameters = null)
    {
        return await this.callAsync ("publicGetSupportAnnouncementsTypes",parameters);
    }

    public async Task<object> privateGetRfqCounterparties (object parameters = null)
    {
        return await this.callAsync ("privateGetRfqCounterparties",parameters);
    }

    public async Task<object> privateGetRfqMakerInstrumentSettings (object parameters = null)
    {
        return await this.callAsync ("privateGetRfqMakerInstrumentSettings",parameters);
    }

    public async Task<object> privateGetRfqMmpConfig (object parameters = null)
    {
        return await this.callAsync ("privateGetRfqMmpConfig",parameters);
    }

    public async Task<object> privateGetRfqRfqs (object parameters = null)
    {
        return await this.callAsync ("privateGetRfqRfqs",parameters);
    }

    public async Task<object> privateGetRfqQuotes (object parameters = null)
    {
        return await this.callAsync ("privateGetRfqQuotes",parameters);
    }

    public async Task<object> privateGetRfqTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetRfqTrades",parameters);
    }

    public async Task<object> privateGetRfqPublicTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetRfqPublicTrades",parameters);
    }

    public async Task<object> privateGetSprdOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetSprdOrder",parameters);
    }

    public async Task<object> privateGetSprdOrdersPending (object parameters = null)
    {
        return await this.callAsync ("privateGetSprdOrdersPending",parameters);
    }

    public async Task<object> privateGetSprdOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetSprdOrdersHistory",parameters);
    }

    public async Task<object> privateGetSprdOrdersHistoryArchive (object parameters = null)
    {
        return await this.callAsync ("privateGetSprdOrdersHistoryArchive",parameters);
    }

    public async Task<object> privateGetSprdTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetSprdTrades",parameters);
    }

    public async Task<object> privateGetTradeOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrder",parameters);
    }

    public async Task<object> privateGetTradeOrdersPending (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersPending",parameters);
    }

    public async Task<object> privateGetTradeOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersHistory",parameters);
    }

    public async Task<object> privateGetTradeOrdersHistoryArchive (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersHistoryArchive",parameters);
    }

    public async Task<object> privateGetTradeFills (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeFills",parameters);
    }

    public async Task<object> privateGetTradeFillsHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeFillsHistory",parameters);
    }

    public async Task<object> privateGetTradeFillsArchive (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeFillsArchive",parameters);
    }

    public async Task<object> privateGetTradeOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrderAlgo",parameters);
    }

    public async Task<object> privateGetTradeOrdersAlgoPending (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersAlgoPending",parameters);
    }

    public async Task<object> privateGetTradeOrdersAlgoHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOrdersAlgoHistory",parameters);
    }

    public async Task<object> privateGetTradeEasyConvertCurrencyList (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeEasyConvertCurrencyList",parameters);
    }

    public async Task<object> privateGetTradeEasyConvertHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeEasyConvertHistory",parameters);
    }

    public async Task<object> privateGetTradeOneClickRepayCurrencyList (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOneClickRepayCurrencyList",parameters);
    }

    public async Task<object> privateGetTradeOneClickRepayCurrencyListV2 (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOneClickRepayCurrencyListV2",parameters);
    }

    public async Task<object> privateGetTradeOneClickRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOneClickRepayHistory",parameters);
    }

    public async Task<object> privateGetTradeOneClickRepayHistoryV2 (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeOneClickRepayHistoryV2",parameters);
    }

    public async Task<object> privateGetTradeAccountRateLimit (object parameters = null)
    {
        return await this.callAsync ("privateGetTradeAccountRateLimit",parameters);
    }

    public async Task<object> privateGetAssetCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetCurrencies",parameters);
    }

    public async Task<object> privateGetAssetBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetBalances",parameters);
    }

    public async Task<object> privateGetAssetNonTradableAssets (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetNonTradableAssets",parameters);
    }

    public async Task<object> privateGetAssetAssetValuation (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetAssetValuation",parameters);
    }

    public async Task<object> privateGetAssetTransferState (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetTransferState",parameters);
    }

    public async Task<object> privateGetAssetBills (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetBills",parameters);
    }

    public async Task<object> privateGetAssetDepositLightning (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetDepositLightning",parameters);
    }

    public async Task<object> privateGetAssetDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetDepositAddress",parameters);
    }

    public async Task<object> privateGetAssetDepositHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetDepositHistory",parameters);
    }

    public async Task<object> privateGetAssetWithdrawalHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetWithdrawalHistory",parameters);
    }

    public async Task<object> privateGetAssetDepositWithdrawStatus (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetDepositWithdrawStatus",parameters);
    }

    public async Task<object> privateGetAssetConvertCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetConvertCurrencies",parameters);
    }

    public async Task<object> privateGetAssetConvertCurrencyPair (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetConvertCurrencyPair",parameters);
    }

    public async Task<object> privateGetAssetConvertHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetConvertHistory",parameters);
    }

    public async Task<object> privateGetAssetMonthlyStatement (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetMonthlyStatement",parameters);
    }

    public async Task<object> privateGetAccountInstruments (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountInstruments",parameters);
    }

    public async Task<object> privateGetAccountBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBalance",parameters);
    }

    public async Task<object> privateGetAccountPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountPositions",parameters);
    }

    public async Task<object> privateGetAccountPositionsHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountPositionsHistory",parameters);
    }

    public async Task<object> privateGetAccountAccountPositionRisk (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountAccountPositionRisk",parameters);
    }

    public async Task<object> privateGetAccountBills (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBills",parameters);
    }

    public async Task<object> privateGetAccountBillsArchive (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBillsArchive",parameters);
    }

    public async Task<object> privateGetAccountBillsHistoryArchive (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBillsHistoryArchive",parameters);
    }

    public async Task<object> privateGetAccountConfig (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountConfig",parameters);
    }

    public async Task<object> privateGetAccountMaxSize (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMaxSize",parameters);
    }

    public async Task<object> privateGetAccountMaxAvailSize (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMaxAvailSize",parameters);
    }

    public async Task<object> privateGetAccountLeverageInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountLeverageInfo",parameters);
    }

    public async Task<object> privateGetAccountAdjustLeverageInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountAdjustLeverageInfo",parameters);
    }

    public async Task<object> privateGetAccountMaxLoan (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMaxLoan",parameters);
    }

    public async Task<object> privateGetAccountTradeFee (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountTradeFee",parameters);
    }

    public async Task<object> privateGetAccountInterestAccrued (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountInterestAccrued",parameters);
    }

    public async Task<object> privateGetAccountInterestRate (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountInterestRate",parameters);
    }

    public async Task<object> privateGetAccountMaxWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMaxWithdrawal",parameters);
    }

    public async Task<object> privateGetAccountRiskState (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountRiskState",parameters);
    }

    public async Task<object> privateGetAccountQuickMarginBorrowRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountQuickMarginBorrowRepayHistory",parameters);
    }

    public async Task<object> privateGetAccountBorrowRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountBorrowRepayHistory",parameters);
    }

    public async Task<object> privateGetAccountVipInterestAccrued (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountVipInterestAccrued",parameters);
    }

    public async Task<object> privateGetAccountVipInterestDeducted (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountVipInterestDeducted",parameters);
    }

    public async Task<object> privateGetAccountVipLoanOrderList (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountVipLoanOrderList",parameters);
    }

    public async Task<object> privateGetAccountVipLoanOrderDetail (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountVipLoanOrderDetail",parameters);
    }

    public async Task<object> privateGetAccountInterestLimits (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountInterestLimits",parameters);
    }

    public async Task<object> privateGetAccountGreeks (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountGreeks",parameters);
    }

    public async Task<object> privateGetAccountPositionTiers (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountPositionTiers",parameters);
    }

    public async Task<object> privateGetAccountMmpConfig (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMmpConfig",parameters);
    }

    public async Task<object> privateGetAccountFixedLoanBorrowingLimit (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountFixedLoanBorrowingLimit",parameters);
    }

    public async Task<object> privateGetAccountFixedLoanBorrowingQuote (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountFixedLoanBorrowingQuote",parameters);
    }

    public async Task<object> privateGetAccountFixedLoanBorrowingOrdersList (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountFixedLoanBorrowingOrdersList",parameters);
    }

    public async Task<object> privateGetAccountSpotManualBorrowRepay (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSpotManualBorrowRepay",parameters);
    }

    public async Task<object> privateGetAccountSetAutoRepay (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSetAutoRepay",parameters);
    }

    public async Task<object> privateGetAccountSpotBorrowRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSpotBorrowRepayHistory",parameters);
    }

    public async Task<object> privateGetAccountMovePositionsHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountMovePositionsHistory",parameters);
    }

    public async Task<object> privateGetUsersSubaccountList (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersSubaccountList",parameters);
    }

    public async Task<object> privateGetAccountSubaccountBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubaccountBalances",parameters);
    }

    public async Task<object> privateGetAssetSubaccountBalances (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetSubaccountBalances",parameters);
    }

    public async Task<object> privateGetAccountSubaccountMaxWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubaccountMaxWithdrawal",parameters);
    }

    public async Task<object> privateGetAssetSubaccountBills (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetSubaccountBills",parameters);
    }

    public async Task<object> privateGetAssetSubaccountManagedSubaccountBills (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetSubaccountManagedSubaccountBills",parameters);
    }

    public async Task<object> privateGetUsersEntrustSubaccountList (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersEntrustSubaccountList",parameters);
    }

    public async Task<object> privateGetAccountSubaccountInterestLimits (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountSubaccountInterestLimits",parameters);
    }

    public async Task<object> privateGetUsersSubaccountApikey (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersSubaccountApikey",parameters);
    }

    public async Task<object> privateGetTradingBotGridOrdersAlgoPending (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotGridOrdersAlgoPending",parameters);
    }

    public async Task<object> privateGetTradingBotGridOrdersAlgoHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotGridOrdersAlgoHistory",parameters);
    }

    public async Task<object> privateGetTradingBotGridOrdersAlgoDetails (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotGridOrdersAlgoDetails",parameters);
    }

    public async Task<object> privateGetTradingBotGridSubOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotGridSubOrders",parameters);
    }

    public async Task<object> privateGetTradingBotGridPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotGridPositions",parameters);
    }

    public async Task<object> privateGetTradingBotGridAiParam (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotGridAiParam",parameters);
    }

    public async Task<object> privateGetTradingBotSignalSignals (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotSignalSignals",parameters);
    }

    public async Task<object> privateGetTradingBotSignalOrdersAlgoDetails (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotSignalOrdersAlgoDetails",parameters);
    }

    public async Task<object> privateGetTradingBotSignalOrdersAlgoHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotSignalOrdersAlgoHistory",parameters);
    }

    public async Task<object> privateGetTradingBotSignalPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotSignalPositions",parameters);
    }

    public async Task<object> privateGetTradingBotSignalPositionsHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotSignalPositionsHistory",parameters);
    }

    public async Task<object> privateGetTradingBotSignalSubOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotSignalSubOrders",parameters);
    }

    public async Task<object> privateGetTradingBotSignalEventHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotSignalEventHistory",parameters);
    }

    public async Task<object> privateGetTradingBotRecurringOrdersAlgoPending (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotRecurringOrdersAlgoPending",parameters);
    }

    public async Task<object> privateGetTradingBotRecurringOrdersAlgoHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotRecurringOrdersAlgoHistory",parameters);
    }

    public async Task<object> privateGetTradingBotRecurringOrdersAlgoDetails (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotRecurringOrdersAlgoDetails",parameters);
    }

    public async Task<object> privateGetTradingBotRecurringSubOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetTradingBotRecurringSubOrders",parameters);
    }

    public async Task<object> privateGetFinanceSavingsBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceSavingsBalance",parameters);
    }

    public async Task<object> privateGetFinanceSavingsLendingHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceSavingsLendingHistory",parameters);
    }

    public async Task<object> privateGetFinanceStakingDefiOffers (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceStakingDefiOffers",parameters);
    }

    public async Task<object> privateGetFinanceStakingDefiOrdersActive (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceStakingDefiOrdersActive",parameters);
    }

    public async Task<object> privateGetFinanceStakingDefiOrdersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceStakingDefiOrdersHistory",parameters);
    }

    public async Task<object> privateGetFinanceStakingDefiEthBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceStakingDefiEthBalance",parameters);
    }

    public async Task<object> privateGetFinanceStakingDefiEthPurchaseRedeemHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceStakingDefiEthPurchaseRedeemHistory",parameters);
    }

    public async Task<object> privateGetFinanceStakingDefiEthProductInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceStakingDefiEthProductInfo",parameters);
    }

    public async Task<object> privateGetFinanceStakingDefiSolBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceStakingDefiSolBalance",parameters);
    }

    public async Task<object> privateGetFinanceStakingDefiSolPurchaseRedeemHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceStakingDefiSolPurchaseRedeemHistory",parameters);
    }

    public async Task<object> privateGetCopytradingCurrentSubpositions (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingCurrentSubpositions",parameters);
    }

    public async Task<object> privateGetCopytradingSubpositionsHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingSubpositionsHistory",parameters);
    }

    public async Task<object> privateGetCopytradingInstruments (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingInstruments",parameters);
    }

    public async Task<object> privateGetCopytradingProfitSharingDetails (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingProfitSharingDetails",parameters);
    }

    public async Task<object> privateGetCopytradingTotalProfitSharing (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingTotalProfitSharing",parameters);
    }

    public async Task<object> privateGetCopytradingUnrealizedProfitSharingDetails (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingUnrealizedProfitSharingDetails",parameters);
    }

    public async Task<object> privateGetCopytradingCopySettings (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingCopySettings",parameters);
    }

    public async Task<object> privateGetCopytradingBatchLeverageInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingBatchLeverageInfo",parameters);
    }

    public async Task<object> privateGetCopytradingCurrentLeadTraders (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingCurrentLeadTraders",parameters);
    }

    public async Task<object> privateGetCopytradingLeadTradersHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetCopytradingLeadTradersHistory",parameters);
    }

    public async Task<object> privateGetBrokerNdInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetBrokerNdInfo",parameters);
    }

    public async Task<object> privateGetBrokerNdSubaccountInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetBrokerNdSubaccountInfo",parameters);
    }

    public async Task<object> privateGetBrokerNdSubaccountApikey (object parameters = null)
    {
        return await this.callAsync ("privateGetBrokerNdSubaccountApikey",parameters);
    }

    public async Task<object> privateGetAssetBrokerNdSubaccountDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetBrokerNdSubaccountDepositAddress",parameters);
    }

    public async Task<object> privateGetAssetBrokerNdSubaccountDepositHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetBrokerNdSubaccountDepositHistory",parameters);
    }

    public async Task<object> privateGetAssetBrokerNdSubaccountWithdrawalHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetBrokerNdSubaccountWithdrawalHistory",parameters);
    }

    public async Task<object> privateGetBrokerNdRebateDaily (object parameters = null)
    {
        return await this.callAsync ("privateGetBrokerNdRebateDaily",parameters);
    }

    public async Task<object> privateGetBrokerNdRebatePerOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetBrokerNdRebatePerOrders",parameters);
    }

    public async Task<object> privateGetFinanceSfpDcdOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceSfpDcdOrder",parameters);
    }

    public async Task<object> privateGetFinanceSfpDcdOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetFinanceSfpDcdOrders",parameters);
    }

    public async Task<object> privateGetBrokerFdRebatePerOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetBrokerFdRebatePerOrders",parameters);
    }

    public async Task<object> privateGetBrokerFdIfRebate (object parameters = null)
    {
        return await this.callAsync ("privateGetBrokerFdIfRebate",parameters);
    }

    public async Task<object> privateGetAffiliateInviteeDetail (object parameters = null)
    {
        return await this.callAsync ("privateGetAffiliateInviteeDetail",parameters);
    }

    public async Task<object> privateGetUsersPartnerIfRebate (object parameters = null)
    {
        return await this.callAsync ("privateGetUsersPartnerIfRebate",parameters);
    }

    public async Task<object> privateGetSupportAnnouncements (object parameters = null)
    {
        return await this.callAsync ("privateGetSupportAnnouncements",parameters);
    }

    public async Task<object> privatePostRfqCreateRfq (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqCreateRfq",parameters);
    }

    public async Task<object> privatePostRfqCancelRfq (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqCancelRfq",parameters);
    }

    public async Task<object> privatePostRfqCancelBatchRfqs (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqCancelBatchRfqs",parameters);
    }

    public async Task<object> privatePostRfqCancelAllRfqs (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqCancelAllRfqs",parameters);
    }

    public async Task<object> privatePostRfqExecuteQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqExecuteQuote",parameters);
    }

    public async Task<object> privatePostRfqMakerInstrumentSettings (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqMakerInstrumentSettings",parameters);
    }

    public async Task<object> privatePostRfqMmpReset (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqMmpReset",parameters);
    }

    public async Task<object> privatePostRfqMmpConfig (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqMmpConfig",parameters);
    }

    public async Task<object> privatePostRfqCreateQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqCreateQuote",parameters);
    }

    public async Task<object> privatePostRfqCancelQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqCancelQuote",parameters);
    }

    public async Task<object> privatePostRfqCancelBatchQuotes (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqCancelBatchQuotes",parameters);
    }

    public async Task<object> privatePostRfqCancelAllQuotes (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqCancelAllQuotes",parameters);
    }

    public async Task<object> privatePostSprdOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSprdOrder",parameters);
    }

    public async Task<object> privatePostSprdCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSprdCancelOrder",parameters);
    }

    public async Task<object> privatePostSprdMassCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostSprdMassCancel",parameters);
    }

    public async Task<object> privatePostSprdAmendOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSprdAmendOrder",parameters);
    }

    public async Task<object> privatePostSprdCancelAllAfter (object parameters = null)
    {
        return await this.callAsync ("privatePostSprdCancelAllAfter",parameters);
    }

    public async Task<object> privatePostTradeOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeOrder",parameters);
    }

    public async Task<object> privatePostTradeBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeBatchOrders",parameters);
    }

    public async Task<object> privatePostTradeCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelOrder",parameters);
    }

    public async Task<object> privatePostTradeCancelBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelBatchOrders",parameters);
    }

    public async Task<object> privatePostTradeAmendOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeAmendOrder",parameters);
    }

    public async Task<object> privatePostTradeAmendBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeAmendBatchOrders",parameters);
    }

    public async Task<object> privatePostTradeClosePosition (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeClosePosition",parameters);
    }

    public async Task<object> privatePostTradeFillsArchive (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeFillsArchive",parameters);
    }

    public async Task<object> privatePostTradeOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradeCancelAlgos (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelAlgos",parameters);
    }

    public async Task<object> privatePostTradeAmendAlgos (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeAmendAlgos",parameters);
    }

    public async Task<object> privatePostTradeCancelAdvanceAlgos (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelAdvanceAlgos",parameters);
    }

    public async Task<object> privatePostTradeEasyConvert (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeEasyConvert",parameters);
    }

    public async Task<object> privatePostTradeOneClickRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeOneClickRepay",parameters);
    }

    public async Task<object> privatePostTradeOneClickRepayV2 (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeOneClickRepayV2",parameters);
    }

    public async Task<object> privatePostTradeMassCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeMassCancel",parameters);
    }

    public async Task<object> privatePostTradeCancelAllAfter (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeCancelAllAfter",parameters);
    }

    public async Task<object> privatePostAssetTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetTransfer",parameters);
    }

    public async Task<object> privatePostAssetWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetWithdrawal",parameters);
    }

    public async Task<object> privatePostAssetWithdrawalLightning (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetWithdrawalLightning",parameters);
    }

    public async Task<object> privatePostAssetCancelWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetCancelWithdrawal",parameters);
    }

    public async Task<object> privatePostAssetConvertDustAssets (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetConvertDustAssets",parameters);
    }

    public async Task<object> privatePostAssetConvertEstimateQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetConvertEstimateQuote",parameters);
    }

    public async Task<object> privatePostAssetConvertTrade (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetConvertTrade",parameters);
    }

    public async Task<object> privatePostAssetMonthlyStatement (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetMonthlyStatement",parameters);
    }

    public async Task<object> privatePostAccountSetPositionMode (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetPositionMode",parameters);
    }

    public async Task<object> privatePostAccountSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetLeverage",parameters);
    }

    public async Task<object> privatePostAccountPositionMarginBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountPositionMarginBalance",parameters);
    }

    public async Task<object> privatePostAccountSetGreeks (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetGreeks",parameters);
    }

    public async Task<object> privatePostAccountSetIsolatedMode (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetIsolatedMode",parameters);
    }

    public async Task<object> privatePostAccountQuickMarginBorrowRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountQuickMarginBorrowRepay",parameters);
    }

    public async Task<object> privatePostAccountBorrowRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountBorrowRepay",parameters);
    }

    public async Task<object> privatePostAccountSimulatedMargin (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSimulatedMargin",parameters);
    }

    public async Task<object> privatePostAccountPositionBuilder (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountPositionBuilder",parameters);
    }

    public async Task<object> privatePostAccountSetRiskOffsetType (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetRiskOffsetType",parameters);
    }

    public async Task<object> privatePostAccountActivateOption (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountActivateOption",parameters);
    }

    public async Task<object> privatePostAccountSetAutoLoan (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetAutoLoan",parameters);
    }

    public async Task<object> privatePostAccountSetAccountLevel (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSetAccountLevel",parameters);
    }

    public async Task<object> privatePostAccountMmpReset (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountMmpReset",parameters);
    }

    public async Task<object> privatePostAccountMmpConfig (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountMmpConfig",parameters);
    }

    public async Task<object> privatePostAccountFixedLoanBorrowingOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountFixedLoanBorrowingOrder",parameters);
    }

    public async Task<object> privatePostAccountFixedLoanAmendBorrowingOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountFixedLoanAmendBorrowingOrder",parameters);
    }

    public async Task<object> privatePostAccountFixedLoanManualReborrow (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountFixedLoanManualReborrow",parameters);
    }

    public async Task<object> privatePostAccountFixedLoanRepayBorrowingOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountFixedLoanRepayBorrowingOrder",parameters);
    }

    public async Task<object> privatePostAccountBillsHistoryArchive (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountBillsHistoryArchive",parameters);
    }

    public async Task<object> privatePostAccountMovePositions (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountMovePositions",parameters);
    }

    public async Task<object> privatePostUsersSubaccountModifyApikey (object parameters = null)
    {
        return await this.callAsync ("privatePostUsersSubaccountModifyApikey",parameters);
    }

    public async Task<object> privatePostAssetSubaccountTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetSubaccountTransfer",parameters);
    }

    public async Task<object> privatePostUsersSubaccountSetTransferOut (object parameters = null)
    {
        return await this.callAsync ("privatePostUsersSubaccountSetTransferOut",parameters);
    }

    public async Task<object> privatePostAccountSubaccountSetLoanAllocation (object parameters = null)
    {
        return await this.callAsync ("privatePostAccountSubaccountSetLoanAllocation",parameters);
    }

    public async Task<object> privatePostUsersSubaccountCreateSubaccount (object parameters = null)
    {
        return await this.callAsync ("privatePostUsersSubaccountCreateSubaccount",parameters);
    }

    public async Task<object> privatePostUsersSubaccountSubaccountApikey (object parameters = null)
    {
        return await this.callAsync ("privatePostUsersSubaccountSubaccountApikey",parameters);
    }

    public async Task<object> privatePostUsersSubaccountDeleteApikey (object parameters = null)
    {
        return await this.callAsync ("privatePostUsersSubaccountDeleteApikey",parameters);
    }

    public async Task<object> privatePostTradingBotGridOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradingBotGridAmendOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridAmendOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradingBotGridStopOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridStopOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradingBotGridClosePosition (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridClosePosition",parameters);
    }

    public async Task<object> privatePostTradingBotGridCancelCloseOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridCancelCloseOrder",parameters);
    }

    public async Task<object> privatePostTradingBotGridOrderInstantTrigger (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridOrderInstantTrigger",parameters);
    }

    public async Task<object> privatePostTradingBotGridWithdrawIncome (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridWithdrawIncome",parameters);
    }

    public async Task<object> privatePostTradingBotGridComputeMarginBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridComputeMarginBalance",parameters);
    }

    public async Task<object> privatePostTradingBotGridMarginBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridMarginBalance",parameters);
    }

    public async Task<object> privatePostTradingBotGridMinInvestment (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridMinInvestment",parameters);
    }

    public async Task<object> privatePostTradingBotGridAdjustInvestment (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotGridAdjustInvestment",parameters);
    }

    public async Task<object> privatePostTradingBotSignalCreateSignal (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotSignalCreateSignal",parameters);
    }

    public async Task<object> privatePostTradingBotSignalOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotSignalOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradingBotSignalStopOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotSignalStopOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradingBotSignalMarginBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotSignalMarginBalance",parameters);
    }

    public async Task<object> privatePostTradingBotSignalAmendTPSL (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotSignalAmendTPSL",parameters);
    }

    public async Task<object> privatePostTradingBotSignalSetInstruments (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotSignalSetInstruments",parameters);
    }

    public async Task<object> privatePostTradingBotSignalClosePosition (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotSignalClosePosition",parameters);
    }

    public async Task<object> privatePostTradingBotSignalSubOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotSignalSubOrder",parameters);
    }

    public async Task<object> privatePostTradingBotSignalCancelSubOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotSignalCancelSubOrder",parameters);
    }

    public async Task<object> privatePostTradingBotRecurringOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotRecurringOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradingBotRecurringAmendOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotRecurringAmendOrderAlgo",parameters);
    }

    public async Task<object> privatePostTradingBotRecurringStopOrderAlgo (object parameters = null)
    {
        return await this.callAsync ("privatePostTradingBotRecurringStopOrderAlgo",parameters);
    }

    public async Task<object> privatePostFinanceSavingsPurchaseRedempt (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceSavingsPurchaseRedempt",parameters);
    }

    public async Task<object> privatePostFinanceSavingsSetLendingRate (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceSavingsSetLendingRate",parameters);
    }

    public async Task<object> privatePostFinanceStakingDefiPurchase (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceStakingDefiPurchase",parameters);
    }

    public async Task<object> privatePostFinanceStakingDefiRedeem (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceStakingDefiRedeem",parameters);
    }

    public async Task<object> privatePostFinanceStakingDefiCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceStakingDefiCancel",parameters);
    }

    public async Task<object> privatePostFinanceStakingDefiEthPurchase (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceStakingDefiEthPurchase",parameters);
    }

    public async Task<object> privatePostFinanceStakingDefiEthRedeem (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceStakingDefiEthRedeem",parameters);
    }

    public async Task<object> privatePostFinanceStakingDefiSolPurchase (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceStakingDefiSolPurchase",parameters);
    }

    public async Task<object> privatePostFinanceStakingDefiSolRedeem (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceStakingDefiSolRedeem",parameters);
    }

    public async Task<object> privatePostCopytradingAlgoOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingAlgoOrder",parameters);
    }

    public async Task<object> privatePostCopytradingCloseSubposition (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingCloseSubposition",parameters);
    }

    public async Task<object> privatePostCopytradingSetInstruments (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingSetInstruments",parameters);
    }

    public async Task<object> privatePostCopytradingFirstCopySettings (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingFirstCopySettings",parameters);
    }

    public async Task<object> privatePostCopytradingAmendCopySettings (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingAmendCopySettings",parameters);
    }

    public async Task<object> privatePostCopytradingStopCopyTrading (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingStopCopyTrading",parameters);
    }

    public async Task<object> privatePostCopytradingBatchSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostCopytradingBatchSetLeverage",parameters);
    }

    public async Task<object> privatePostBrokerNdCreateSubaccount (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdCreateSubaccount",parameters);
    }

    public async Task<object> privatePostBrokerNdDeleteSubaccount (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdDeleteSubaccount",parameters);
    }

    public async Task<object> privatePostBrokerNdSubaccountApikey (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdSubaccountApikey",parameters);
    }

    public async Task<object> privatePostBrokerNdSubaccountModifyApikey (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdSubaccountModifyApikey",parameters);
    }

    public async Task<object> privatePostBrokerNdSubaccountDeleteApikey (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdSubaccountDeleteApikey",parameters);
    }

    public async Task<object> privatePostBrokerNdSetSubaccountLevel (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdSetSubaccountLevel",parameters);
    }

    public async Task<object> privatePostBrokerNdSetSubaccountFeeRate (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdSetSubaccountFeeRate",parameters);
    }

    public async Task<object> privatePostBrokerNdSetSubaccountAssets (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdSetSubaccountAssets",parameters);
    }

    public async Task<object> privatePostAssetBrokerNdSubaccountDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetBrokerNdSubaccountDepositAddress",parameters);
    }

    public async Task<object> privatePostAssetBrokerNdModifySubaccountDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetBrokerNdModifySubaccountDepositAddress",parameters);
    }

    public async Task<object> privatePostBrokerNdRebatePerOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdRebatePerOrders",parameters);
    }

    public async Task<object> privatePostFinanceSfpDcdQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceSfpDcdQuote",parameters);
    }

    public async Task<object> privatePostFinanceSfpDcdOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostFinanceSfpDcdOrder",parameters);
    }

    public async Task<object> privatePostBrokerNdReportSubaccountIp (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerNdReportSubaccountIp",parameters);
    }

    public async Task<object> privatePostBrokerFdRebatePerOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostBrokerFdRebatePerOrders",parameters);
    }

}