// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class independentreserve : Exchange
{
    public independentreserve (object args = null): base(args) {}

    public async Task<object> publicGetGetValidPrimaryCurrencyCodes (object parameters = null)
    {
        return await this.callAsync ("publicGetGetValidPrimaryCurrencyCodes",parameters);
    }

    public async Task<object> publicGetGetValidSecondaryCurrencyCodes (object parameters = null)
    {
        return await this.callAsync ("publicGetGetValidSecondaryCurrencyCodes",parameters);
    }

    public async Task<object> publicGetGetValidLimitOrderTypes (object parameters = null)
    {
        return await this.callAsync ("publicGetGetValidLimitOrderTypes",parameters);
    }

    public async Task<object> publicGetGetValidMarketOrderTypes (object parameters = null)
    {
        return await this.callAsync ("publicGetGetValidMarketOrderTypes",parameters);
    }

    public async Task<object> publicGetGetValidOrderTypes (object parameters = null)
    {
        return await this.callAsync ("publicGetGetValidOrderTypes",parameters);
    }

    public async Task<object> publicGetGetValidTransactionTypes (object parameters = null)
    {
        return await this.callAsync ("publicGetGetValidTransactionTypes",parameters);
    }

    public async Task<object> publicGetGetMarketSummary (object parameters = null)
    {
        return await this.callAsync ("publicGetGetMarketSummary",parameters);
    }

    public async Task<object> publicGetGetOrderBook (object parameters = null)
    {
        return await this.callAsync ("publicGetGetOrderBook",parameters);
    }

    public async Task<object> publicGetGetAllOrders (object parameters = null)
    {
        return await this.callAsync ("publicGetGetAllOrders",parameters);
    }

    public async Task<object> publicGetGetTradeHistorySummary (object parameters = null)
    {
        return await this.callAsync ("publicGetGetTradeHistorySummary",parameters);
    }

    public async Task<object> publicGetGetRecentTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetGetRecentTrades",parameters);
    }

    public async Task<object> publicGetGetFxRates (object parameters = null)
    {
        return await this.callAsync ("publicGetGetFxRates",parameters);
    }

    public async Task<object> publicGetGetOrderMinimumVolumes (object parameters = null)
    {
        return await this.callAsync ("publicGetGetOrderMinimumVolumes",parameters);
    }

    public async Task<object> publicGetGetCryptoWithdrawalFees (object parameters = null)
    {
        return await this.callAsync ("publicGetGetCryptoWithdrawalFees",parameters);
    }

    public async Task<object> publicGetGetCryptoWithdrawalFees2 (object parameters = null)
    {
        return await this.callAsync ("publicGetGetCryptoWithdrawalFees2",parameters);
    }

    public async Task<object> publicGetGetNetworks (object parameters = null)
    {
        return await this.callAsync ("publicGetGetNetworks",parameters);
    }

    public async Task<object> publicGetGetPrimaryCurrencyConfig2 (object parameters = null)
    {
        return await this.callAsync ("publicGetGetPrimaryCurrencyConfig2",parameters);
    }

    public async Task<object> privatePostGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostGetOpenOrders",parameters);
    }

    public async Task<object> privatePostGetClosedOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostGetClosedOrders",parameters);
    }

    public async Task<object> privatePostGetClosedFilledOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostGetClosedFilledOrders",parameters);
    }

    public async Task<object> privatePostGetOrderDetails (object parameters = null)
    {
        return await this.callAsync ("privatePostGetOrderDetails",parameters);
    }

    public async Task<object> privatePostGetAccounts (object parameters = null)
    {
        return await this.callAsync ("privatePostGetAccounts",parameters);
    }

    public async Task<object> privatePostGetTransactions (object parameters = null)
    {
        return await this.callAsync ("privatePostGetTransactions",parameters);
    }

    public async Task<object> privatePostGetFiatBankAccounts (object parameters = null)
    {
        return await this.callAsync ("privatePostGetFiatBankAccounts",parameters);
    }

    public async Task<object> privatePostGetDigitalCurrencyDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostGetDigitalCurrencyDepositAddress",parameters);
    }

    public async Task<object> privatePostGetDigitalCurrencyDepositAddress2 (object parameters = null)
    {
        return await this.callAsync ("privatePostGetDigitalCurrencyDepositAddress2",parameters);
    }

    public async Task<object> privatePostGetDigitalCurrencyDepositAddresses (object parameters = null)
    {
        return await this.callAsync ("privatePostGetDigitalCurrencyDepositAddresses",parameters);
    }

    public async Task<object> privatePostGetDigitalCurrencyDepositAddresses2 (object parameters = null)
    {
        return await this.callAsync ("privatePostGetDigitalCurrencyDepositAddresses2",parameters);
    }

    public async Task<object> privatePostGetTrades (object parameters = null)
    {
        return await this.callAsync ("privatePostGetTrades",parameters);
    }

    public async Task<object> privatePostGetBrokerageFees (object parameters = null)
    {
        return await this.callAsync ("privatePostGetBrokerageFees",parameters);
    }

    public async Task<object> privatePostGetDigitalCurrencyWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostGetDigitalCurrencyWithdrawal",parameters);
    }

    public async Task<object> privatePostPlaceLimitOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostPlaceLimitOrder",parameters);
    }

    public async Task<object> privatePostPlaceMarketOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostPlaceMarketOrder",parameters);
    }

    public async Task<object> privatePostCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelOrder",parameters);
    }

    public async Task<object> privatePostSynchDigitalCurrencyDepositAddressWithBlockchain (object parameters = null)
    {
        return await this.callAsync ("privatePostSynchDigitalCurrencyDepositAddressWithBlockchain",parameters);
    }

    public async Task<object> privatePostRequestFiatWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privatePostRequestFiatWithdrawal",parameters);
    }

    public async Task<object> privatePostWithdrawFiatCurrency (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdrawFiatCurrency",parameters);
    }

    public async Task<object> privatePostWithdrawDigitalCurrency (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdrawDigitalCurrency",parameters);
    }

    public async Task<object> privatePostWithdrawCrypto (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdrawCrypto",parameters);
    }

}