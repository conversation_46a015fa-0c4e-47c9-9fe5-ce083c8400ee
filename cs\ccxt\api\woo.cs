// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class woo : Exchange
{
    public woo (object args = null): base(args) {}

    public async Task<object> v1PubGetHistKline (object parameters = null)
    {
        return await this.callAsync ("v1PubGetHistKline",parameters);
    }

    public async Task<object> v1PubGetHistTrades (object parameters = null)
    {
        return await this.callAsync ("v1PubGetHistTrades",parameters);
    }

    public async Task<object> v1PublicGetInfo (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetInfo",parameters);
    }

    public async Task<object> v1PublicGetInfoSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetInfoSymbol",parameters);
    }

    public async Task<object> v1PublicGetSystemInfo (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetSystemInfo",parameters);
    }

    public async Task<object> v1PublicGetMarketTrades (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetMarketTrades",parameters);
    }

    public async Task<object> v1PublicGetToken (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetToken",parameters);
    }

    public async Task<object> v1PublicGetTokenNetwork (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetTokenNetwork",parameters);
    }

    public async Task<object> v1PublicGetFundingRates (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetFundingRates",parameters);
    }

    public async Task<object> v1PublicGetFundingRateSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetFundingRateSymbol",parameters);
    }

    public async Task<object> v1PublicGetFundingRateHistory (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetFundingRateHistory",parameters);
    }

    public async Task<object> v1PublicGetFutures (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetFutures",parameters);
    }

    public async Task<object> v1PublicGetFuturesSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetFuturesSymbol",parameters);
    }

    public async Task<object> v1PublicGetOrderbookSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetOrderbookSymbol",parameters);
    }

    public async Task<object> v1PublicGetKline (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetKline",parameters);
    }

    public async Task<object> v1PrivateGetClientToken (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientToken",parameters);
    }

    public async Task<object> v1PrivateGetOrderOid (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetOrderOid",parameters);
    }

    public async Task<object> v1PrivateGetClientOrderClientOrderId (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientOrderClientOrderId",parameters);
    }

    public async Task<object> v1PrivateGetOrders (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetOrders",parameters);
    }

    public async Task<object> v1PrivateGetClientTradeTid (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientTradeTid",parameters);
    }

    public async Task<object> v1PrivateGetOrderOidTrades (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetOrderOidTrades",parameters);
    }

    public async Task<object> v1PrivateGetClientTrades (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientTrades",parameters);
    }

    public async Task<object> v1PrivateGetClientHistTrades (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientHistTrades",parameters);
    }

    public async Task<object> v1PrivateGetStakingYieldHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetStakingYieldHistory",parameters);
    }

    public async Task<object> v1PrivateGetClientHolding (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientHolding",parameters);
    }

    public async Task<object> v1PrivateGetAssetDeposit (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetAssetDeposit",parameters);
    }

    public async Task<object> v1PrivateGetAssetHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetAssetHistory",parameters);
    }

    public async Task<object> v1PrivateGetSubAccountAll (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetSubAccountAll",parameters);
    }

    public async Task<object> v1PrivateGetSubAccountAssets (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetSubAccountAssets",parameters);
    }

    public async Task<object> v1PrivateGetSubAccountAssetDetail (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetSubAccountAssetDetail",parameters);
    }

    public async Task<object> v1PrivateGetSubAccountIpRestriction (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetSubAccountIpRestriction",parameters);
    }

    public async Task<object> v1PrivateGetAssetMainSubTransferHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetAssetMainSubTransferHistory",parameters);
    }

    public async Task<object> v1PrivateGetTokenInterest (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetTokenInterest",parameters);
    }

    public async Task<object> v1PrivateGetTokenInterestToken (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetTokenInterestToken",parameters);
    }

    public async Task<object> v1PrivateGetInterestHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetInterestHistory",parameters);
    }

    public async Task<object> v1PrivateGetInterestRepay (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetInterestRepay",parameters);
    }

    public async Task<object> v1PrivateGetFundingFeeHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetFundingFeeHistory",parameters);
    }

    public async Task<object> v1PrivateGetPositions (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetPositions",parameters);
    }

    public async Task<object> v1PrivateGetPositionSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetPositionSymbol",parameters);
    }

    public async Task<object> v1PrivateGetClientTransactionHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientTransactionHistory",parameters);
    }

    public async Task<object> v1PrivateGetClientFuturesLeverage (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientFuturesLeverage",parameters);
    }

    public async Task<object> v1PrivatePostOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostOrder",parameters);
    }

    public async Task<object> v1PrivatePostOrderCancelAllAfter (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostOrderCancelAllAfter",parameters);
    }

    public async Task<object> v1PrivatePostAssetMainSubTransfer (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAssetMainSubTransfer",parameters);
    }

    public async Task<object> v1PrivatePostAssetLtv (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAssetLtv",parameters);
    }

    public async Task<object> v1PrivatePostAssetWithdraw (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAssetWithdraw",parameters);
    }

    public async Task<object> v1PrivatePostAssetInternalWithdraw (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAssetInternalWithdraw",parameters);
    }

    public async Task<object> v1PrivatePostInterestRepay (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostInterestRepay",parameters);
    }

    public async Task<object> v1PrivatePostClientAccountMode (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClientAccountMode",parameters);
    }

    public async Task<object> v1PrivatePostClientPositionMode (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClientPositionMode",parameters);
    }

    public async Task<object> v1PrivatePostClientLeverage (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClientLeverage",parameters);
    }

    public async Task<object> v1PrivatePostClientFuturesLeverage (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClientFuturesLeverage",parameters);
    }

    public async Task<object> v1PrivatePostClientIsolatedMargin (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClientIsolatedMargin",parameters);
    }

    public async Task<object> v1PrivateDeleteOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteOrder",parameters);
    }

    public async Task<object> v1PrivateDeleteClientOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteClientOrder",parameters);
    }

    public async Task<object> v1PrivateDeleteOrders (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteOrders",parameters);
    }

    public async Task<object> v1PrivateDeleteAssetWithdraw (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteAssetWithdraw",parameters);
    }

    public async Task<object> v2PrivateGetClientHolding (object parameters = null)
    {
        return await this.callAsync ("v2PrivateGetClientHolding",parameters);
    }

    public async Task<object> v3PublicGetSystemInfo (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetSystemInfo",parameters);
    }

    public async Task<object> v3PublicGetInstruments (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetInstruments",parameters);
    }

    public async Task<object> v3PublicGetToken (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetToken",parameters);
    }

    public async Task<object> v3PublicGetTokenNetwork (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetTokenNetwork",parameters);
    }

    public async Task<object> v3PublicGetTokenInfo (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetTokenInfo",parameters);
    }

    public async Task<object> v3PublicGetMarketTrades (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetMarketTrades",parameters);
    }

    public async Task<object> v3PublicGetMarketTradesHistory (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetMarketTradesHistory",parameters);
    }

    public async Task<object> v3PublicGetOrderbook (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetOrderbook",parameters);
    }

    public async Task<object> v3PublicGetKline (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetKline",parameters);
    }

    public async Task<object> v3PublicGetKlineHistory (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetKlineHistory",parameters);
    }

    public async Task<object> v3PublicGetFutures (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetFutures",parameters);
    }

    public async Task<object> v3PublicGetFundingRate (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetFundingRate",parameters);
    }

    public async Task<object> v3PublicGetFundingRateHistory (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetFundingRateHistory",parameters);
    }

    public async Task<object> v3PublicGetInsuranceFund (object parameters = null)
    {
        return await this.callAsync ("v3PublicGetInsuranceFund",parameters);
    }

    public async Task<object> v3PrivateGetTradeOrder (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetTradeOrder",parameters);
    }

    public async Task<object> v3PrivateGetTradeOrders (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetTradeOrders",parameters);
    }

    public async Task<object> v3PrivateGetTradeAlgoOrder (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetTradeAlgoOrder",parameters);
    }

    public async Task<object> v3PrivateGetTradeAlgoOrders (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetTradeAlgoOrders",parameters);
    }

    public async Task<object> v3PrivateGetTradeTransaction (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetTradeTransaction",parameters);
    }

    public async Task<object> v3PrivateGetTradeTransactionHistory (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetTradeTransactionHistory",parameters);
    }

    public async Task<object> v3PrivateGetTradeTradingFee (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetTradeTradingFee",parameters);
    }

    public async Task<object> v3PrivateGetAccountInfo (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAccountInfo",parameters);
    }

    public async Task<object> v3PrivateGetAccountTokenConfig (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAccountTokenConfig",parameters);
    }

    public async Task<object> v3PrivateGetAccountSymbolConfig (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAccountSymbolConfig",parameters);
    }

    public async Task<object> v3PrivateGetAccountSubAccountsAll (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAccountSubAccountsAll",parameters);
    }

    public async Task<object> v3PrivateGetAccountReferralSummary (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAccountReferralSummary",parameters);
    }

    public async Task<object> v3PrivateGetAccountReferralRewardHistory (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAccountReferralRewardHistory",parameters);
    }

    public async Task<object> v3PrivateGetAccountCredentials (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAccountCredentials",parameters);
    }

    public async Task<object> v3PrivateGetAssetBalances (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAssetBalances",parameters);
    }

    public async Task<object> v3PrivateGetAssetTokenHistory (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAssetTokenHistory",parameters);
    }

    public async Task<object> v3PrivateGetAssetTransferHistory (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAssetTransferHistory",parameters);
    }

    public async Task<object> v3PrivateGetAssetWalletHistory (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAssetWalletHistory",parameters);
    }

    public async Task<object> v3PrivateGetAssetWalletDeposit (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAssetWalletDeposit",parameters);
    }

    public async Task<object> v3PrivateGetAssetStakingYieldHistory (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAssetStakingYieldHistory",parameters);
    }

    public async Task<object> v3PrivateGetFuturesPositions (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetFuturesPositions",parameters);
    }

    public async Task<object> v3PrivateGetFuturesLeverage (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetFuturesLeverage",parameters);
    }

    public async Task<object> v3PrivateGetFuturesDefaultMarginMode (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetFuturesDefaultMarginMode",parameters);
    }

    public async Task<object> v3PrivateGetFuturesFundingFeeHistory (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetFuturesFundingFeeHistory",parameters);
    }

    public async Task<object> v3PrivateGetSpotMarginInterestRate (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetSpotMarginInterestRate",parameters);
    }

    public async Task<object> v3PrivateGetSpotMarginInterestHistory (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetSpotMarginInterestHistory",parameters);
    }

    public async Task<object> v3PrivateGetSpotMarginMaxMargin (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetSpotMarginMaxMargin",parameters);
    }

    public async Task<object> v3PrivateGetAlgoOrderOid (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAlgoOrderOid",parameters);
    }

    public async Task<object> v3PrivateGetAlgoOrders (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetAlgoOrders",parameters);
    }

    public async Task<object> v3PrivateGetBalances (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBalances",parameters);
    }

    public async Task<object> v3PrivateGetPositions (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetPositions",parameters);
    }

    public async Task<object> v3PrivateGetBuypower (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetBuypower",parameters);
    }

    public async Task<object> v3PrivateGetConvertExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetConvertExchangeInfo",parameters);
    }

    public async Task<object> v3PrivateGetConvertAssetInfo (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetConvertAssetInfo",parameters);
    }

    public async Task<object> v3PrivateGetConvertRfq (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetConvertRfq",parameters);
    }

    public async Task<object> v3PrivateGetConvertTrade (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetConvertTrade",parameters);
    }

    public async Task<object> v3PrivateGetConvertTrades (object parameters = null)
    {
        return await this.callAsync ("v3PrivateGetConvertTrades",parameters);
    }

    public async Task<object> v3PrivatePostTradeOrder (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostTradeOrder",parameters);
    }

    public async Task<object> v3PrivatePostTradeAlgoOrder (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostTradeAlgoOrder",parameters);
    }

    public async Task<object> v3PrivatePostTradeCancelAllAfter (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostTradeCancelAllAfter",parameters);
    }

    public async Task<object> v3PrivatePostAccountTradingMode (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostAccountTradingMode",parameters);
    }

    public async Task<object> v3PrivatePostAccountListenKey (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostAccountListenKey",parameters);
    }

    public async Task<object> v3PrivatePostAssetTransfer (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostAssetTransfer",parameters);
    }

    public async Task<object> v3PrivatePostAssetWalletWithdraw (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostAssetWalletWithdraw",parameters);
    }

    public async Task<object> v3PrivatePostSpotMarginLeverage (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostSpotMarginLeverage",parameters);
    }

    public async Task<object> v3PrivatePostSpotMarginInterestRepay (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostSpotMarginInterestRepay",parameters);
    }

    public async Task<object> v3PrivatePostAlgoOrder (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostAlgoOrder",parameters);
    }

    public async Task<object> v3PrivatePostConvertRft (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePostConvertRft",parameters);
    }

    public async Task<object> v3PrivatePutTradeOrder (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePutTradeOrder",parameters);
    }

    public async Task<object> v3PrivatePutTradeAlgoOrder (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePutTradeAlgoOrder",parameters);
    }

    public async Task<object> v3PrivatePutFuturesLeverage (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePutFuturesLeverage",parameters);
    }

    public async Task<object> v3PrivatePutFuturesPositionMode (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePutFuturesPositionMode",parameters);
    }

    public async Task<object> v3PrivatePutOrderOid (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePutOrderOid",parameters);
    }

    public async Task<object> v3PrivatePutOrderClientClientOrderId (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePutOrderClientClientOrderId",parameters);
    }

    public async Task<object> v3PrivatePutAlgoOrderOid (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePutAlgoOrderOid",parameters);
    }

    public async Task<object> v3PrivatePutAlgoOrderClientClientOrderId (object parameters = null)
    {
        return await this.callAsync ("v3PrivatePutAlgoOrderClientClientOrderId",parameters);
    }

    public async Task<object> v3PrivateDeleteTradeOrder (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteTradeOrder",parameters);
    }

    public async Task<object> v3PrivateDeleteTradeOrders (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteTradeOrders",parameters);
    }

    public async Task<object> v3PrivateDeleteTradeAlgoOrder (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteTradeAlgoOrder",parameters);
    }

    public async Task<object> v3PrivateDeleteTradeAlgoOrders (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteTradeAlgoOrders",parameters);
    }

    public async Task<object> v3PrivateDeleteTradeAllOrders (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteTradeAllOrders",parameters);
    }

    public async Task<object> v3PrivateDeleteAlgoOrderOrderId (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteAlgoOrderOrderId",parameters);
    }

    public async Task<object> v3PrivateDeleteAlgoOrdersPending (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteAlgoOrdersPending",parameters);
    }

    public async Task<object> v3PrivateDeleteAlgoOrdersPendingSymbol (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteAlgoOrdersPendingSymbol",parameters);
    }

    public async Task<object> v3PrivateDeleteOrdersPending (object parameters = null)
    {
        return await this.callAsync ("v3PrivateDeleteOrdersPending",parameters);
    }

}