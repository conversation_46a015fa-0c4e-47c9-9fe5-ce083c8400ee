// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class defx : Exchange
{
    public defx (object args = null): base(args) {}

    public async Task<object> v1PublicGetHealthcheckPing (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetHealthcheckPing",parameters);
    }

    public async Task<object> v1PublicGetSymbolsSymbolOhlc (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetSymbolsSymbolOhlc",parameters);
    }

    public async Task<object> v1PublicGetSymbolsSymbolTrades (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetSymbolsSymbolTrades",parameters);
    }

    public async Task<object> v1PublicGetSymbolsSymbolPrices (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetSymbolsSymbolPrices",parameters);
    }

    public async Task<object> v1PublicGetSymbolsSymbolTicker24hr (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetSymbolsSymbolTicker24hr",parameters);
    }

    public async Task<object> v1PublicGetSymbolsSymbolDepthLevelSlab (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetSymbolsSymbolDepthLevelSlab",parameters);
    }

    public async Task<object> v1PublicGetTicker24HrAgg (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetTicker24HrAgg",parameters);
    }

    public async Task<object> v1PublicGetCMarkets (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetCMarkets",parameters);
    }

    public async Task<object> v1PublicGetCMarketsMetadata (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetCMarketsMetadata",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsNewUsers (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsNewUsers",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsTvl (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsTvl",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsVolumeByInstrument (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsVolumeByInstrument",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsLiquidation (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsLiquidation",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsTotalVolume (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsTotalVolume",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsOpenInterest (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsOpenInterest",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsTotalTrades (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsTotalTrades",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsBasis (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsBasis",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsInsuranceFund (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsInsuranceFund",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsLongAndShortRatio (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsLongAndShortRatio",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketStatsFundingRate (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketStatsFundingRate",parameters);
    }

    public async Task<object> v1PublicGetAnalyticsMarketOverview (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetAnalyticsMarketOverview",parameters);
    }

    public async Task<object> v1PublicGetExplorerSearch (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetExplorerSearch",parameters);
    }

    public async Task<object> v1PublicGetExplorerTransactions (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetExplorerTransactions",parameters);
    }

    public async Task<object> v1PublicGetExplorerBlocks (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetExplorerBlocks",parameters);
    }

    public async Task<object> v1PrivateGetApiOrderOrderId (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiOrderOrderId",parameters);
    }

    public async Task<object> v1PrivateGetApiOrders (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiOrders",parameters);
    }

    public async Task<object> v1PrivateGetApiOrdersOcoParentOrderId (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiOrdersOcoParentOrderId",parameters);
    }

    public async Task<object> v1PrivateGetApiTrades (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiTrades",parameters);
    }

    public async Task<object> v1PrivateGetApiPositionActive (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiPositionActive",parameters);
    }

    public async Task<object> v1PrivateGetApiUsersMetadataLeverage (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiUsersMetadataLeverage",parameters);
    }

    public async Task<object> v1PrivateGetApiUsersMetadataFeeMultiplier (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiUsersMetadataFeeMultiplier",parameters);
    }

    public async Task<object> v1PrivateGetApiUsersMetadataSlippage (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiUsersMetadataSlippage",parameters);
    }

    public async Task<object> v1PrivateGetApiUsersReferral (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiUsersReferral",parameters);
    }

    public async Task<object> v1PrivateGetApiUsersApikeys (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiUsersApikeys",parameters);
    }

    public async Task<object> v1PrivateGetConnectionSignatureMessageEvm (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetConnectionSignatureMessageEvm",parameters);
    }

    public async Task<object> v1PrivateGetApiUsersProfileWallets (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiUsersProfileWallets",parameters);
    }

    public async Task<object> v1PrivateGetApiNotifications (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiNotifications",parameters);
    }

    public async Task<object> v1PrivateGetApiWalletBalance (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiWalletBalance",parameters);
    }

    public async Task<object> v1PrivateGetApiWalletTransactions (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiWalletTransactions",parameters);
    }

    public async Task<object> v1PrivateGetApiAnalyticsUserOverview (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiAnalyticsUserOverview",parameters);
    }

    public async Task<object> v1PrivateGetApiAnalyticsUserPnl (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiAnalyticsUserPnl",parameters);
    }

    public async Task<object> v1PrivateGetApiAnalyticsPointsOverview (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiAnalyticsPointsOverview",parameters);
    }

    public async Task<object> v1PrivateGetApiAnalyticsPointsHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetApiAnalyticsPointsHistory",parameters);
    }

    public async Task<object> v1PrivatePostApiOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiOrder",parameters);
    }

    public async Task<object> v1PrivatePostApiPositionOco (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiPositionOco",parameters);
    }

    public async Task<object> v1PrivatePostApiUsersSocketListenKeys (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiUsersSocketListenKeys",parameters);
    }

    public async Task<object> v1PrivatePostApiUsersMetadataLeverage (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiUsersMetadataLeverage",parameters);
    }

    public async Task<object> v1PrivatePostApiUsersMetadataFeeMultiplier (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiUsersMetadataFeeMultiplier",parameters);
    }

    public async Task<object> v1PrivatePostApiUsersMetadataSlippage (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiUsersMetadataSlippage",parameters);
    }

    public async Task<object> v1PrivatePostApiUsersReferralRecordReferralSignup (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiUsersReferralRecordReferralSignup",parameters);
    }

    public async Task<object> v1PrivatePostApiUsersApikeys (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiUsersApikeys",parameters);
    }

    public async Task<object> v1PrivatePostApiUsersProfileWallets (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiUsersProfileWallets",parameters);
    }

    public async Task<object> v1PrivatePostApiTransfersWithdrawal (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiTransfersWithdrawal",parameters);
    }

    public async Task<object> v1PrivatePostApiTransfersBridgeWithdrawal (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostApiTransfersBridgeWithdrawal",parameters);
    }

    public async Task<object> v1PrivatePutApiPositionUpdatePositionMargin (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePutApiPositionUpdatePositionMargin",parameters);
    }

    public async Task<object> v1PrivatePutApiUsersSocketListenKeysListenKey (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePutApiUsersSocketListenKeysListenKey",parameters);
    }

    public async Task<object> v1PrivatePutApiUsersApikeysAccessKeyStatus (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePutApiUsersApikeysAccessKeyStatus",parameters);
    }

    public async Task<object> v1PrivatePutApiUsersReferral (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePutApiUsersReferral",parameters);
    }

    public async Task<object> v1PrivatePatchApiUsersApikeysAccessKey (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePatchApiUsersApikeysAccessKey",parameters);
    }

    public async Task<object> v1PrivateDeleteApiOrdersAllOpen (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteApiOrdersAllOpen",parameters);
    }

    public async Task<object> v1PrivateDeleteApiOrderOrderId (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteApiOrderOrderId",parameters);
    }

    public async Task<object> v1PrivateDeleteApiPositionPositionId (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteApiPositionPositionId",parameters);
    }

    public async Task<object> v1PrivateDeleteApiPositionAll (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteApiPositionAll",parameters);
    }

    public async Task<object> v1PrivateDeleteApiUsersSocketListenKeysListenKey (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteApiUsersSocketListenKeysListenKey",parameters);
    }

    public async Task<object> v1PrivateDeleteApiUsersApikeysAccessKey (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteApiUsersApikeysAccessKey",parameters);
    }

}