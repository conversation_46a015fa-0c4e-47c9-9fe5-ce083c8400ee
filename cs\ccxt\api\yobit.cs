// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class yobit : Exchange
{
    public yobit (object args = null): base(args) {}

    public async Task<object> publicGetDepthPair (object parameters = null)
    {
        return await this.callAsync ("publicGetDepthPair",parameters);
    }

    public async Task<object> publicGetInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetInfo",parameters);
    }

    public async Task<object> publicGetTickerPair (object parameters = null)
    {
        return await this.callAsync ("publicGetTickerPair",parameters);
    }

    public async Task<object> publicGetTradesPair (object parameters = null)
    {
        return await this.callAsync ("publicGetTradesPair",parameters);
    }

    public async Task<object> privatePostActiveOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostActiveOrders",parameters);
    }

    public async Task<object> privatePostCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelOrder",parameters);
    }

    public async Task<object> privatePostGetDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostGetDepositAddress",parameters);
    }

    public async Task<object> privatePostGetInfo (object parameters = null)
    {
        return await this.callAsync ("privatePostGetInfo",parameters);
    }

    public async Task<object> privatePostOrderInfo (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderInfo",parameters);
    }

    public async Task<object> privatePostTrade (object parameters = null)
    {
        return await this.callAsync ("privatePostTrade",parameters);
    }

    public async Task<object> privatePostTradeHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostTradeHistory",parameters);
    }

    public async Task<object> privatePostWithdrawCoinsToAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdrawCoinsToAddress",parameters);
    }

}