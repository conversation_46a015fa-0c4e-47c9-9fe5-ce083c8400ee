// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class phemex : Exchange
{
    public phemex (object args = null): base(args) {}

    public async Task<object> publicGetCfgV2Products (object parameters = null)
    {
        return await this.callAsync ("publicGetCfgV2Products",parameters);
    }

    public async Task<object> publicGetCfgFundingRates (object parameters = null)
    {
        return await this.callAsync ("publicGetCfgFundingRates",parameters);
    }

    public async Task<object> publicGetProducts (object parameters = null)
    {
        return await this.callAsync ("publicGetProducts",parameters);
    }

    public async Task<object> publicGetNomicsTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetNomicsTrades",parameters);
    }

    public async Task<object> publicGetMdKline (object parameters = null)
    {
        return await this.callAsync ("publicGetMdKline",parameters);
    }

    public async Task<object> publicGetMdV2KlineList (object parameters = null)
    {
        return await this.callAsync ("publicGetMdV2KlineList",parameters);
    }

    public async Task<object> publicGetMdV2Kline (object parameters = null)
    {
        return await this.callAsync ("publicGetMdV2Kline",parameters);
    }

    public async Task<object> publicGetMdV2KlineLast (object parameters = null)
    {
        return await this.callAsync ("publicGetMdV2KlineLast",parameters);
    }

    public async Task<object> publicGetMdOrderbook (object parameters = null)
    {
        return await this.callAsync ("publicGetMdOrderbook",parameters);
    }

    public async Task<object> publicGetMdTrade (object parameters = null)
    {
        return await this.callAsync ("publicGetMdTrade",parameters);
    }

    public async Task<object> publicGetMdSpotTicker24hr (object parameters = null)
    {
        return await this.callAsync ("publicGetMdSpotTicker24hr",parameters);
    }

    public async Task<object> publicGetExchangePublicCfgChainSettings (object parameters = null)
    {
        return await this.callAsync ("publicGetExchangePublicCfgChainSettings",parameters);
    }

    public async Task<object> v1GetMdFullbook (object parameters = null)
    {
        return await this.callAsync ("v1GetMdFullbook",parameters);
    }

    public async Task<object> v1GetMdOrderbook (object parameters = null)
    {
        return await this.callAsync ("v1GetMdOrderbook",parameters);
    }

    public async Task<object> v1GetMdTrade (object parameters = null)
    {
        return await this.callAsync ("v1GetMdTrade",parameters);
    }

    public async Task<object> v1GetMdTicker24hr (object parameters = null)
    {
        return await this.callAsync ("v1GetMdTicker24hr",parameters);
    }

    public async Task<object> v1GetMdTicker24hrAll (object parameters = null)
    {
        return await this.callAsync ("v1GetMdTicker24hrAll",parameters);
    }

    public async Task<object> v1GetMdSpotTicker24hr (object parameters = null)
    {
        return await this.callAsync ("v1GetMdSpotTicker24hr",parameters);
    }

    public async Task<object> v1GetMdSpotTicker24hrAll (object parameters = null)
    {
        return await this.callAsync ("v1GetMdSpotTicker24hrAll",parameters);
    }

    public async Task<object> v1GetExchangePublicProducts (object parameters = null)
    {
        return await this.callAsync ("v1GetExchangePublicProducts",parameters);
    }

    public async Task<object> v1GetApiDataPublicDataFundingRateHistory (object parameters = null)
    {
        return await this.callAsync ("v1GetApiDataPublicDataFundingRateHistory",parameters);
    }

    public async Task<object> v2GetPublicProducts (object parameters = null)
    {
        return await this.callAsync ("v2GetPublicProducts",parameters);
    }

    public async Task<object> v2GetPublicProductsPlus (object parameters = null)
    {
        return await this.callAsync ("v2GetPublicProductsPlus",parameters);
    }

    public async Task<object> v2GetMdV2Orderbook (object parameters = null)
    {
        return await this.callAsync ("v2GetMdV2Orderbook",parameters);
    }

    public async Task<object> v2GetMdV2Trade (object parameters = null)
    {
        return await this.callAsync ("v2GetMdV2Trade",parameters);
    }

    public async Task<object> v2GetMdV2Ticker24hr (object parameters = null)
    {
        return await this.callAsync ("v2GetMdV2Ticker24hr",parameters);
    }

    public async Task<object> v2GetMdV2Ticker24hrAll (object parameters = null)
    {
        return await this.callAsync ("v2GetMdV2Ticker24hrAll",parameters);
    }

    public async Task<object> v2GetApiDataPublicDataFundingRateHistory (object parameters = null)
    {
        return await this.callAsync ("v2GetApiDataPublicDataFundingRateHistory",parameters);
    }

    public async Task<object> privateGetSpotOrdersActive (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotOrdersActive",parameters);
    }

    public async Task<object> privateGetSpotOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotOrders",parameters);
    }

    public async Task<object> privateGetSpotWallets (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotWallets",parameters);
    }

    public async Task<object> privateGetExchangeSpotOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeSpotOrder",parameters);
    }

    public async Task<object> privateGetExchangeSpotOrderTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeSpotOrderTrades",parameters);
    }

    public async Task<object> privateGetExchangeOrderV2OrderList (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrderV2OrderList",parameters);
    }

    public async Task<object> privateGetExchangeOrderV2TradingList (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrderV2TradingList",parameters);
    }

    public async Task<object> privateGetAccountsAccountPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsAccountPositions",parameters);
    }

    public async Task<object> privateGetGAccountsAccountPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetGAccountsAccountPositions",parameters);
    }

    public async Task<object> privateGetAccountsPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetAccountsPositions",parameters);
    }

    public async Task<object> privateGetApiDataFuturesFundingFees (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataFuturesFundingFees",parameters);
    }

    public async Task<object> privateGetApiDataGFuturesFundingFees (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataGFuturesFundingFees",parameters);
    }

    public async Task<object> privateGetApiDataFuturesOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataFuturesOrders",parameters);
    }

    public async Task<object> privateGetApiDataGFuturesOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataGFuturesOrders",parameters);
    }

    public async Task<object> privateGetApiDataFuturesOrdersByOrderId (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataFuturesOrdersByOrderId",parameters);
    }

    public async Task<object> privateGetApiDataGFuturesOrdersByOrderId (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataGFuturesOrdersByOrderId",parameters);
    }

    public async Task<object> privateGetApiDataFuturesTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataFuturesTrades",parameters);
    }

    public async Task<object> privateGetApiDataGFuturesTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataGFuturesTrades",parameters);
    }

    public async Task<object> privateGetApiDataFuturesTradingFees (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataFuturesTradingFees",parameters);
    }

    public async Task<object> privateGetApiDataGFuturesTradingFees (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataGFuturesTradingFees",parameters);
    }

    public async Task<object> privateGetApiDataFuturesV2TradeAccountDetail (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataFuturesV2TradeAccountDetail",parameters);
    }

    public async Task<object> privateGetGOrdersActiveList (object parameters = null)
    {
        return await this.callAsync ("privateGetGOrdersActiveList",parameters);
    }

    public async Task<object> privateGetOrdersActiveList (object parameters = null)
    {
        return await this.callAsync ("privateGetOrdersActiveList",parameters);
    }

    public async Task<object> privateGetExchangeOrderList (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrderList",parameters);
    }

    public async Task<object> privateGetExchangeOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrder",parameters);
    }

    public async Task<object> privateGetExchangeOrderTrade (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeOrderTrade",parameters);
    }

    public async Task<object> privateGetPhemexUserUsersChildren (object parameters = null)
    {
        return await this.callAsync ("privateGetPhemexUserUsersChildren",parameters);
    }

    public async Task<object> privateGetPhemexUserWalletsV2DepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetPhemexUserWalletsV2DepositAddress",parameters);
    }

    public async Task<object> privateGetPhemexUserWalletsTradeAccountDetail (object parameters = null)
    {
        return await this.callAsync ("privateGetPhemexUserWalletsTradeAccountDetail",parameters);
    }

    public async Task<object> privateGetPhemexDepositWalletsApiDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetPhemexDepositWalletsApiDepositAddress",parameters);
    }

    public async Task<object> privateGetPhemexDepositWalletsApiDepositHist (object parameters = null)
    {
        return await this.callAsync ("privateGetPhemexDepositWalletsApiDepositHist",parameters);
    }

    public async Task<object> privateGetPhemexDepositWalletsApiChainCfg (object parameters = null)
    {
        return await this.callAsync ("privateGetPhemexDepositWalletsApiChainCfg",parameters);
    }

    public async Task<object> privateGetPhemexWithdrawWalletsApiWithdrawHist (object parameters = null)
    {
        return await this.callAsync ("privateGetPhemexWithdrawWalletsApiWithdrawHist",parameters);
    }

    public async Task<object> privateGetPhemexWithdrawWalletsApiAssetInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetPhemexWithdrawWalletsApiAssetInfo",parameters);
    }

    public async Task<object> privateGetPhemexUserOrderClosedPositionList (object parameters = null)
    {
        return await this.callAsync ("privateGetPhemexUserOrderClosedPositionList",parameters);
    }

    public async Task<object> privateGetExchangeMarginsTransfer (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeMarginsTransfer",parameters);
    }

    public async Task<object> privateGetExchangeWalletsConfirmWithdraw (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeWalletsConfirmWithdraw",parameters);
    }

    public async Task<object> privateGetExchangeWalletsWithdrawList (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeWalletsWithdrawList",parameters);
    }

    public async Task<object> privateGetExchangeWalletsDepositList (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeWalletsDepositList",parameters);
    }

    public async Task<object> privateGetExchangeWalletsV2DepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetExchangeWalletsV2DepositAddress",parameters);
    }

    public async Task<object> privateGetApiDataSpotsFunds (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataSpotsFunds",parameters);
    }

    public async Task<object> privateGetApiDataSpotsOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataSpotsOrders",parameters);
    }

    public async Task<object> privateGetApiDataSpotsOrdersByOrderId (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataSpotsOrdersByOrderId",parameters);
    }

    public async Task<object> privateGetApiDataSpotsPnls (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataSpotsPnls",parameters);
    }

    public async Task<object> privateGetApiDataSpotsTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataSpotsTrades",parameters);
    }

    public async Task<object> privateGetApiDataSpotsTradesByOrderId (object parameters = null)
    {
        return await this.callAsync ("privateGetApiDataSpotsTradesByOrderId",parameters);
    }

    public async Task<object> privateGetAssetsConvert (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetsConvert",parameters);
    }

    public async Task<object> privateGetAssetsTransfer (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetsTransfer",parameters);
    }

    public async Task<object> privateGetAssetsSpotsSubAccountsTransfer (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetsSpotsSubAccountsTransfer",parameters);
    }

    public async Task<object> privateGetAssetsFuturesSubAccountsTransfer (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetsFuturesSubAccountsTransfer",parameters);
    }

    public async Task<object> privateGetAssetsQuote (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetsQuote",parameters);
    }

    public async Task<object> privatePostSpotOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotOrders",parameters);
    }

    public async Task<object> privatePostOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostOrders",parameters);
    }

    public async Task<object> privatePostGOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostGOrders",parameters);
    }

    public async Task<object> privatePostPositionsAssign (object parameters = null)
    {
        return await this.callAsync ("privatePostPositionsAssign",parameters);
    }

    public async Task<object> privatePostExchangeWalletsTransferOut (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeWalletsTransferOut",parameters);
    }

    public async Task<object> privatePostExchangeWalletsTransferIn (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeWalletsTransferIn",parameters);
    }

    public async Task<object> privatePostExchangeMargins (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeMargins",parameters);
    }

    public async Task<object> privatePostExchangeWalletsCreateWithdraw (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeWalletsCreateWithdraw",parameters);
    }

    public async Task<object> privatePostExchangeWalletsCancelWithdraw (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeWalletsCancelWithdraw",parameters);
    }

    public async Task<object> privatePostExchangeWalletsCreateWithdrawAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostExchangeWalletsCreateWithdrawAddress",parameters);
    }

    public async Task<object> privatePostAssetsTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetsTransfer",parameters);
    }

    public async Task<object> privatePostAssetsSpotsSubAccountsTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetsSpotsSubAccountsTransfer",parameters);
    }

    public async Task<object> privatePostAssetsFuturesSubAccountsTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetsFuturesSubAccountsTransfer",parameters);
    }

    public async Task<object> privatePostAssetsUniversalTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetsUniversalTransfer",parameters);
    }

    public async Task<object> privatePostAssetsConvert (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetsConvert",parameters);
    }

    public async Task<object> privatePostPhemexWithdrawWalletsApiCreateWithdraw (object parameters = null)
    {
        return await this.callAsync ("privatePostPhemexWithdrawWalletsApiCreateWithdraw",parameters);
    }

    public async Task<object> privatePostPhemexWithdrawWalletsApiCancelWithdraw (object parameters = null)
    {
        return await this.callAsync ("privatePostPhemexWithdrawWalletsApiCancelWithdraw",parameters);
    }

    public async Task<object> privatePutSpotOrdersCreate (object parameters = null)
    {
        return await this.callAsync ("privatePutSpotOrdersCreate",parameters);
    }

    public async Task<object> privatePutSpotOrders (object parameters = null)
    {
        return await this.callAsync ("privatePutSpotOrders",parameters);
    }

    public async Task<object> privatePutOrdersReplace (object parameters = null)
    {
        return await this.callAsync ("privatePutOrdersReplace",parameters);
    }

    public async Task<object> privatePutGOrdersReplace (object parameters = null)
    {
        return await this.callAsync ("privatePutGOrdersReplace",parameters);
    }

    public async Task<object> privatePutPositionsLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePutPositionsLeverage",parameters);
    }

    public async Task<object> privatePutGPositionsLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePutGPositionsLeverage",parameters);
    }

    public async Task<object> privatePutGPositionsSwitchPosModeSync (object parameters = null)
    {
        return await this.callAsync ("privatePutGPositionsSwitchPosModeSync",parameters);
    }

    public async Task<object> privatePutPositionsRiskLimit (object parameters = null)
    {
        return await this.callAsync ("privatePutPositionsRiskLimit",parameters);
    }

    public async Task<object> privateDeleteSpotOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteSpotOrders",parameters);
    }

    public async Task<object> privateDeleteSpotOrdersAll (object parameters = null)
    {
        return await this.callAsync ("privateDeleteSpotOrdersAll",parameters);
    }

    public async Task<object> privateDeleteOrdersCancel (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrdersCancel",parameters);
    }

    public async Task<object> privateDeleteOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrders",parameters);
    }

    public async Task<object> privateDeleteOrdersAll (object parameters = null)
    {
        return await this.callAsync ("privateDeleteOrdersAll",parameters);
    }

    public async Task<object> privateDeleteGOrdersCancel (object parameters = null)
    {
        return await this.callAsync ("privateDeleteGOrdersCancel",parameters);
    }

    public async Task<object> privateDeleteGOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteGOrders",parameters);
    }

    public async Task<object> privateDeleteGOrdersAll (object parameters = null)
    {
        return await this.callAsync ("privateDeleteGOrdersAll",parameters);
    }

}