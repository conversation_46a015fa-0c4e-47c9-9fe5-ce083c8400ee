// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class wavesexchange : Exchange
{
    public wavesexchange (object args = null): base(args) {}

    public async Task<object> matcherGetMatcher (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcher",parameters);
    }

    public async Task<object> matcherGetMatcherSettings (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherSettings",parameters);
    }

    public async Task<object> matcherGetMatcherSettingsRates (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherSettingsRates",parameters);
    }

    public async Task<object> matcherGetMatcherBalanceReservedPublicKey (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherBalanceReservedPublicKey",parameters);
    }

    public async Task<object> matcherGetMatcherDebugAllSnashotOffsets (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherDebugAllSnashotOffsets",parameters);
    }

    public async Task<object> matcherGetMatcherDebugCurrentOffset (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherDebugCurrentOffset",parameters);
    }

    public async Task<object> matcherGetMatcherDebugLastOffset (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherDebugLastOffset",parameters);
    }

    public async Task<object> matcherGetMatcherDebugOldestSnapshotOffset (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherDebugOldestSnapshotOffset",parameters);
    }

    public async Task<object> matcherGetMatcherDebugConfig (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherDebugConfig",parameters);
    }

    public async Task<object> matcherGetMatcherDebugAddressAddress (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherDebugAddressAddress",parameters);
    }

    public async Task<object> matcherGetMatcherDebugStatus (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherDebugStatus",parameters);
    }

    public async Task<object> matcherGetMatcherDebugAddressAddressCheck (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherDebugAddressAddressCheck",parameters);
    }

    public async Task<object> matcherGetMatcherOrderbook (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrderbook",parameters);
    }

    public async Task<object> matcherGetMatcherOrderbookBaseIdQuoteId (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrderbookBaseIdQuoteId",parameters);
    }

    public async Task<object> matcherGetMatcherOrderbookBaseIdQuoteIdPublicKeyPublicKey (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrderbookBaseIdQuoteIdPublicKeyPublicKey",parameters);
    }

    public async Task<object> matcherGetMatcherOrderbookBaseIdQuoteIdOrderId (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrderbookBaseIdQuoteIdOrderId",parameters);
    }

    public async Task<object> matcherGetMatcherOrderbookBaseIdQuoteIdInfo (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrderbookBaseIdQuoteIdInfo",parameters);
    }

    public async Task<object> matcherGetMatcherOrderbookBaseIdQuoteIdStatus (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrderbookBaseIdQuoteIdStatus",parameters);
    }

    public async Task<object> matcherGetMatcherOrderbookBaseIdQuoteIdTradableBalanceAddress (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrderbookBaseIdQuoteIdTradableBalanceAddress",parameters);
    }

    public async Task<object> matcherGetMatcherOrderbookPublicKey (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrderbookPublicKey",parameters);
    }

    public async Task<object> matcherGetMatcherOrderbookPublicKeyOrderId (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrderbookPublicKeyOrderId",parameters);
    }

    public async Task<object> matcherGetMatcherOrdersAddress (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrdersAddress",parameters);
    }

    public async Task<object> matcherGetMatcherOrdersAddressOrderId (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherOrdersAddressOrderId",parameters);
    }

    public async Task<object> matcherGetMatcherTransactionsOrderId (object parameters = null)
    {
        return await this.callAsync ("matcherGetMatcherTransactionsOrderId",parameters);
    }

    public async Task<object> matcherGetApiV1OrderbookBaseIdQuoteId (object parameters = null)
    {
        return await this.callAsync ("matcherGetApiV1OrderbookBaseIdQuoteId",parameters);
    }

    public async Task<object> matcherPostMatcherOrderbook (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrderbook",parameters);
    }

    public async Task<object> matcherPostMatcherOrderbookMarket (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrderbookMarket",parameters);
    }

    public async Task<object> matcherPostMatcherOrderbookCancel (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrderbookCancel",parameters);
    }

    public async Task<object> matcherPostMatcherOrderbookBaseIdQuoteIdCancel (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrderbookBaseIdQuoteIdCancel",parameters);
    }

    public async Task<object> matcherPostMatcherOrderbookBaseIdQuoteIdCalculateFee (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrderbookBaseIdQuoteIdCalculateFee",parameters);
    }

    public async Task<object> matcherPostMatcherOrderbookBaseIdQuoteIdDelete (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrderbookBaseIdQuoteIdDelete",parameters);
    }

    public async Task<object> matcherPostMatcherOrderbookBaseIdQuoteIdCancelAll (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrderbookBaseIdQuoteIdCancelAll",parameters);
    }

    public async Task<object> matcherPostMatcherDebugSaveSnapshots (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherDebugSaveSnapshots",parameters);
    }

    public async Task<object> matcherPostMatcherOrdersAddressCancel (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrdersAddressCancel",parameters);
    }

    public async Task<object> matcherPostMatcherOrdersCancelOrderId (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrdersCancelOrderId",parameters);
    }

    public async Task<object> matcherPostMatcherOrdersSerialize (object parameters = null)
    {
        return await this.callAsync ("matcherPostMatcherOrdersSerialize",parameters);
    }

    public async Task<object> matcherDeleteMatcherOrderbookBaseIdQuoteId (object parameters = null)
    {
        return await this.callAsync ("matcherDeleteMatcherOrderbookBaseIdQuoteId",parameters);
    }

    public async Task<object> matcherDeleteMatcherSettingsRatesAssetId (object parameters = null)
    {
        return await this.callAsync ("matcherDeleteMatcherSettingsRatesAssetId",parameters);
    }

    public async Task<object> matcherPutMatcherSettingsRatesAssetId (object parameters = null)
    {
        return await this.callAsync ("matcherPutMatcherSettingsRatesAssetId",parameters);
    }

    public async Task<object> nodeGetAddresses (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddresses",parameters);
    }

    public async Task<object> nodeGetAddressesBalanceAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesBalanceAddress",parameters);
    }

    public async Task<object> nodeGetAddressesBalanceAddressConfirmations (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesBalanceAddressConfirmations",parameters);
    }

    public async Task<object> nodeGetAddressesBalanceDetailsAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesBalanceDetailsAddress",parameters);
    }

    public async Task<object> nodeGetAddressesDataAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesDataAddress",parameters);
    }

    public async Task<object> nodeGetAddressesDataAddressKey (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesDataAddressKey",parameters);
    }

    public async Task<object> nodeGetAddressesEffectiveBalanceAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesEffectiveBalanceAddress",parameters);
    }

    public async Task<object> nodeGetAddressesEffectiveBalanceAddressConfirmations (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesEffectiveBalanceAddressConfirmations",parameters);
    }

    public async Task<object> nodeGetAddressesPublicKeyPublicKey (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesPublicKeyPublicKey",parameters);
    }

    public async Task<object> nodeGetAddressesScriptInfoAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesScriptInfoAddress",parameters);
    }

    public async Task<object> nodeGetAddressesScriptInfoAddressMeta (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesScriptInfoAddressMeta",parameters);
    }

    public async Task<object> nodeGetAddressesSeedAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesSeedAddress",parameters);
    }

    public async Task<object> nodeGetAddressesSeqFromTo (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesSeqFromTo",parameters);
    }

    public async Task<object> nodeGetAddressesValidateAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetAddressesValidateAddress",parameters);
    }

    public async Task<object> nodeGetAliasByAddressAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetAliasByAddressAddress",parameters);
    }

    public async Task<object> nodeGetAliasByAliasAlias (object parameters = null)
    {
        return await this.callAsync ("nodeGetAliasByAliasAlias",parameters);
    }

    public async Task<object> nodeGetAssetsAssetIdDistributionHeightLimit (object parameters = null)
    {
        return await this.callAsync ("nodeGetAssetsAssetIdDistributionHeightLimit",parameters);
    }

    public async Task<object> nodeGetAssetsBalanceAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetAssetsBalanceAddress",parameters);
    }

    public async Task<object> nodeGetAssetsBalanceAddressAssetId (object parameters = null)
    {
        return await this.callAsync ("nodeGetAssetsBalanceAddressAssetId",parameters);
    }

    public async Task<object> nodeGetAssetsDetailsAssetId (object parameters = null)
    {
        return await this.callAsync ("nodeGetAssetsDetailsAssetId",parameters);
    }

    public async Task<object> nodeGetAssetsNftAddressLimitLimit (object parameters = null)
    {
        return await this.callAsync ("nodeGetAssetsNftAddressLimitLimit",parameters);
    }

    public async Task<object> nodeGetBlockchainRewards (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlockchainRewards",parameters);
    }

    public async Task<object> nodeGetBlockchainRewardsHeight (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlockchainRewardsHeight",parameters);
    }

    public async Task<object> nodeGetBlocksAddressAddressFromTo (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksAddressAddressFromTo",parameters);
    }

    public async Task<object> nodeGetBlocksAtHeight (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksAtHeight",parameters);
    }

    public async Task<object> nodeGetBlocksDelaySignatureBlockNum (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksDelaySignatureBlockNum",parameters);
    }

    public async Task<object> nodeGetBlocksFirst (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksFirst",parameters);
    }

    public async Task<object> nodeGetBlocksHeadersLast (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksHeadersLast",parameters);
    }

    public async Task<object> nodeGetBlocksHeadersSeqFromTo (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksHeadersSeqFromTo",parameters);
    }

    public async Task<object> nodeGetBlocksHeight (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksHeight",parameters);
    }

    public async Task<object> nodeGetBlocksHeightSignature (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksHeightSignature",parameters);
    }

    public async Task<object> nodeGetBlocksLast (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksLast",parameters);
    }

    public async Task<object> nodeGetBlocksSeqFromTo (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksSeqFromTo",parameters);
    }

    public async Task<object> nodeGetBlocksSignatureSignature (object parameters = null)
    {
        return await this.callAsync ("nodeGetBlocksSignatureSignature",parameters);
    }

    public async Task<object> nodeGetConsensusAlgo (object parameters = null)
    {
        return await this.callAsync ("nodeGetConsensusAlgo",parameters);
    }

    public async Task<object> nodeGetConsensusBasetarget (object parameters = null)
    {
        return await this.callAsync ("nodeGetConsensusBasetarget",parameters);
    }

    public async Task<object> nodeGetConsensusBasetargetBlockId (object parameters = null)
    {
        return await this.callAsync ("nodeGetConsensusBasetargetBlockId",parameters);
    }

    public async Task<object> nodeGetConsensusGeneratingbalanceAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetConsensusGeneratingbalanceAddress",parameters);
    }

    public async Task<object> nodeGetConsensusGenerationsignature (object parameters = null)
    {
        return await this.callAsync ("nodeGetConsensusGenerationsignature",parameters);
    }

    public async Task<object> nodeGetConsensusGenerationsignatureBlockId (object parameters = null)
    {
        return await this.callAsync ("nodeGetConsensusGenerationsignatureBlockId",parameters);
    }

    public async Task<object> nodeGetDebugBalancesHistoryAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugBalancesHistoryAddress",parameters);
    }

    public async Task<object> nodeGetDebugBlocksHowMany (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugBlocksHowMany",parameters);
    }

    public async Task<object> nodeGetDebugConfigInfo (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugConfigInfo",parameters);
    }

    public async Task<object> nodeGetDebugHistoryInfo (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugHistoryInfo",parameters);
    }

    public async Task<object> nodeGetDebugInfo (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugInfo",parameters);
    }

    public async Task<object> nodeGetDebugMinerInfo (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugMinerInfo",parameters);
    }

    public async Task<object> nodeGetDebugPortfoliosAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugPortfoliosAddress",parameters);
    }

    public async Task<object> nodeGetDebugState (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugState",parameters);
    }

    public async Task<object> nodeGetDebugStateChangesAddressAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugStateChangesAddressAddress",parameters);
    }

    public async Task<object> nodeGetDebugStateChangesInfoId (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugStateChangesInfoId",parameters);
    }

    public async Task<object> nodeGetDebugStateWavesHeight (object parameters = null)
    {
        return await this.callAsync ("nodeGetDebugStateWavesHeight",parameters);
    }

    public async Task<object> nodeGetLeasingActiveAddress (object parameters = null)
    {
        return await this.callAsync ("nodeGetLeasingActiveAddress",parameters);
    }

    public async Task<object> nodeGetNodeState (object parameters = null)
    {
        return await this.callAsync ("nodeGetNodeState",parameters);
    }

    public async Task<object> nodeGetNodeVersion (object parameters = null)
    {
        return await this.callAsync ("nodeGetNodeVersion",parameters);
    }

    public async Task<object> nodeGetPeersAll (object parameters = null)
    {
        return await this.callAsync ("nodeGetPeersAll",parameters);
    }

    public async Task<object> nodeGetPeersBlacklisted (object parameters = null)
    {
        return await this.callAsync ("nodeGetPeersBlacklisted",parameters);
    }

    public async Task<object> nodeGetPeersConnected (object parameters = null)
    {
        return await this.callAsync ("nodeGetPeersConnected",parameters);
    }

    public async Task<object> nodeGetPeersSuspended (object parameters = null)
    {
        return await this.callAsync ("nodeGetPeersSuspended",parameters);
    }

    public async Task<object> nodeGetTransactionsAddressAddressLimitLimit (object parameters = null)
    {
        return await this.callAsync ("nodeGetTransactionsAddressAddressLimitLimit",parameters);
    }

    public async Task<object> nodeGetTransactionsInfoId (object parameters = null)
    {
        return await this.callAsync ("nodeGetTransactionsInfoId",parameters);
    }

    public async Task<object> nodeGetTransactionsStatus (object parameters = null)
    {
        return await this.callAsync ("nodeGetTransactionsStatus",parameters);
    }

    public async Task<object> nodeGetTransactionsUnconfirmed (object parameters = null)
    {
        return await this.callAsync ("nodeGetTransactionsUnconfirmed",parameters);
    }

    public async Task<object> nodeGetTransactionsUnconfirmedInfoId (object parameters = null)
    {
        return await this.callAsync ("nodeGetTransactionsUnconfirmedInfoId",parameters);
    }

    public async Task<object> nodeGetTransactionsUnconfirmedSize (object parameters = null)
    {
        return await this.callAsync ("nodeGetTransactionsUnconfirmedSize",parameters);
    }

    public async Task<object> nodeGetUtilsSeed (object parameters = null)
    {
        return await this.callAsync ("nodeGetUtilsSeed",parameters);
    }

    public async Task<object> nodeGetUtilsSeedLength (object parameters = null)
    {
        return await this.callAsync ("nodeGetUtilsSeedLength",parameters);
    }

    public async Task<object> nodeGetUtilsTime (object parameters = null)
    {
        return await this.callAsync ("nodeGetUtilsTime",parameters);
    }

    public async Task<object> nodeGetWalletSeed (object parameters = null)
    {
        return await this.callAsync ("nodeGetWalletSeed",parameters);
    }

    public async Task<object> nodePostAddresses (object parameters = null)
    {
        return await this.callAsync ("nodePostAddresses",parameters);
    }

    public async Task<object> nodePostAddressesDataAddress (object parameters = null)
    {
        return await this.callAsync ("nodePostAddressesDataAddress",parameters);
    }

    public async Task<object> nodePostAddressesSignAddress (object parameters = null)
    {
        return await this.callAsync ("nodePostAddressesSignAddress",parameters);
    }

    public async Task<object> nodePostAddressesSignTextAddress (object parameters = null)
    {
        return await this.callAsync ("nodePostAddressesSignTextAddress",parameters);
    }

    public async Task<object> nodePostAddressesVerifyAddress (object parameters = null)
    {
        return await this.callAsync ("nodePostAddressesVerifyAddress",parameters);
    }

    public async Task<object> nodePostAddressesVerifyTextAddress (object parameters = null)
    {
        return await this.callAsync ("nodePostAddressesVerifyTextAddress",parameters);
    }

    public async Task<object> nodePostDebugBlacklist (object parameters = null)
    {
        return await this.callAsync ("nodePostDebugBlacklist",parameters);
    }

    public async Task<object> nodePostDebugPrint (object parameters = null)
    {
        return await this.callAsync ("nodePostDebugPrint",parameters);
    }

    public async Task<object> nodePostDebugRollback (object parameters = null)
    {
        return await this.callAsync ("nodePostDebugRollback",parameters);
    }

    public async Task<object> nodePostDebugValidate (object parameters = null)
    {
        return await this.callAsync ("nodePostDebugValidate",parameters);
    }

    public async Task<object> nodePostNodeStop (object parameters = null)
    {
        return await this.callAsync ("nodePostNodeStop",parameters);
    }

    public async Task<object> nodePostPeersClearblacklist (object parameters = null)
    {
        return await this.callAsync ("nodePostPeersClearblacklist",parameters);
    }

    public async Task<object> nodePostPeersConnect (object parameters = null)
    {
        return await this.callAsync ("nodePostPeersConnect",parameters);
    }

    public async Task<object> nodePostTransactionsBroadcast (object parameters = null)
    {
        return await this.callAsync ("nodePostTransactionsBroadcast",parameters);
    }

    public async Task<object> nodePostTransactionsCalculateFee (object parameters = null)
    {
        return await this.callAsync ("nodePostTransactionsCalculateFee",parameters);
    }

    public async Task<object> nodePostTranasctionsSign (object parameters = null)
    {
        return await this.callAsync ("nodePostTranasctionsSign",parameters);
    }

    public async Task<object> nodePostTransactionsSignSignerAddress (object parameters = null)
    {
        return await this.callAsync ("nodePostTransactionsSignSignerAddress",parameters);
    }

    public async Task<object> nodePostTranasctionsStatus (object parameters = null)
    {
        return await this.callAsync ("nodePostTranasctionsStatus",parameters);
    }

    public async Task<object> nodePostUtilsHashFast (object parameters = null)
    {
        return await this.callAsync ("nodePostUtilsHashFast",parameters);
    }

    public async Task<object> nodePostUtilsHashSecure (object parameters = null)
    {
        return await this.callAsync ("nodePostUtilsHashSecure",parameters);
    }

    public async Task<object> nodePostUtilsScriptCompileCode (object parameters = null)
    {
        return await this.callAsync ("nodePostUtilsScriptCompileCode",parameters);
    }

    public async Task<object> nodePostUtilsScriptCompileWithImports (object parameters = null)
    {
        return await this.callAsync ("nodePostUtilsScriptCompileWithImports",parameters);
    }

    public async Task<object> nodePostUtilsScriptDecompile (object parameters = null)
    {
        return await this.callAsync ("nodePostUtilsScriptDecompile",parameters);
    }

    public async Task<object> nodePostUtilsScriptEstimate (object parameters = null)
    {
        return await this.callAsync ("nodePostUtilsScriptEstimate",parameters);
    }

    public async Task<object> nodePostUtilsSignPrivateKey (object parameters = null)
    {
        return await this.callAsync ("nodePostUtilsSignPrivateKey",parameters);
    }

    public async Task<object> nodePostUtilsTransactionsSerialize (object parameters = null)
    {
        return await this.callAsync ("nodePostUtilsTransactionsSerialize",parameters);
    }

    public async Task<object> nodeDeleteAddressesAddress (object parameters = null)
    {
        return await this.callAsync ("nodeDeleteAddressesAddress",parameters);
    }

    public async Task<object> nodeDeleteDebugRollbackToSignature (object parameters = null)
    {
        return await this.callAsync ("nodeDeleteDebugRollbackToSignature",parameters);
    }

    public async Task<object> publicGetAssets (object parameters = null)
    {
        return await this.callAsync ("publicGetAssets",parameters);
    }

    public async Task<object> publicGetPairs (object parameters = null)
    {
        return await this.callAsync ("publicGetPairs",parameters);
    }

    public async Task<object> publicGetCandlesBaseIdQuoteId (object parameters = null)
    {
        return await this.callAsync ("publicGetCandlesBaseIdQuoteId",parameters);
    }

    public async Task<object> publicGetTransactionsExchange (object parameters = null)
    {
        return await this.callAsync ("publicGetTransactionsExchange",parameters);
    }

    public async Task<object> privateGetDepositAddressesCurrency (object parameters = null)
    {
        return await this.callAsync ("privateGetDepositAddressesCurrency",parameters);
    }

    public async Task<object> privateGetDepositAddressesCurrencyPlatform (object parameters = null)
    {
        return await this.callAsync ("privateGetDepositAddressesCurrencyPlatform",parameters);
    }

    public async Task<object> privateGetPlatforms (object parameters = null)
    {
        return await this.callAsync ("privateGetPlatforms",parameters);
    }

    public async Task<object> privateGetDepositCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateGetDepositCurrencies",parameters);
    }

    public async Task<object> privateGetWithdrawCurrencies (object parameters = null)
    {
        return await this.callAsync ("privateGetWithdrawCurrencies",parameters);
    }

    public async Task<object> privateGetWithdrawAddressesCurrencyAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetWithdrawAddressesCurrencyAddress",parameters);
    }

    public async Task<object> privatePostOauth2Token (object parameters = null)
    {
        return await this.callAsync ("privatePostOauth2Token",parameters);
    }

    public async Task<object> forwardGetMatcherOrdersAddress (object parameters = null)
    {
        return await this.callAsync ("forwardGetMatcherOrdersAddress",parameters);
    }

    public async Task<object> forwardGetMatcherOrdersAddressOrderId (object parameters = null)
    {
        return await this.callAsync ("forwardGetMatcherOrdersAddressOrderId",parameters);
    }

    public async Task<object> forwardPostMatcherOrdersWavesAddressCancel (object parameters = null)
    {
        return await this.callAsync ("forwardPostMatcherOrdersWavesAddressCancel",parameters);
    }

    public async Task<object> marketGetTickers (object parameters = null)
    {
        return await this.callAsync ("marketGetTickers",parameters);
    }

}