name: New Exchange Request
description: Request the implementation of an exchange not yet implemented in the CCXT library
labels: ["new exchange"]
body:
  - type: input
    id: exchange-name
    attributes:
      label: Exchange Name
    validations:
      required: true

  - type: input
    id: exchange-url
    attributes:
      label: URL of the Exchange
      placeholder: https://www.binance.com
    validations:
      required: true

  - type: input
    id: api-docs
    attributes:
      label: URL of the Exchange's API docs
      placeholder: https://binance-docs.github.io/apidocs
    validations:
      required: true

  - type: textarea
    attributes:
      label: Why should this exchange be implemented?
      description: We would love to add this exchange to CCXT. There are a lot of other exchanges that we would also love to add to CCXT. Why should resources be devoted to adding this exchange to CCXT before adding other exchanges?
    validations:
      required: false
