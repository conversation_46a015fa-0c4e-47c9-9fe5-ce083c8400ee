// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class derive : Exchange
{
    public derive (object args = null): base(args) {}

    public async Task<object> publicGetGetAllCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicGetGetAllCurrencies",parameters);
    }

    public async Task<object> publicPostBuildRegisterSessionKeyTx (object parameters = null)
    {
        return await this.callAsync ("publicPostBuildRegisterSessionKeyTx",parameters);
    }

    public async Task<object> publicPostRegisterSessionKey (object parameters = null)
    {
        return await this.callAsync ("publicPostRegisterSessionKey",parameters);
    }

    public async Task<object> publicPostDeregisterSessionKey (object parameters = null)
    {
        return await this.callAsync ("publicPostDeregisterSessionKey",parameters);
    }

    public async Task<object> publicPostLogin (object parameters = null)
    {
        return await this.callAsync ("publicPostLogin",parameters);
    }

    public async Task<object> publicPostStatistics (object parameters = null)
    {
        return await this.callAsync ("publicPostStatistics",parameters);
    }

    public async Task<object> publicPostGetAllCurrencies (object parameters = null)
    {
        return await this.callAsync ("publicPostGetAllCurrencies",parameters);
    }

    public async Task<object> publicPostGetCurrency (object parameters = null)
    {
        return await this.callAsync ("publicPostGetCurrency",parameters);
    }

    public async Task<object> publicPostGetInstrument (object parameters = null)
    {
        return await this.callAsync ("publicPostGetInstrument",parameters);
    }

    public async Task<object> publicPostGetAllInstruments (object parameters = null)
    {
        return await this.callAsync ("publicPostGetAllInstruments",parameters);
    }

    public async Task<object> publicPostGetInstruments (object parameters = null)
    {
        return await this.callAsync ("publicPostGetInstruments",parameters);
    }

    public async Task<object> publicPostGetTicker (object parameters = null)
    {
        return await this.callAsync ("publicPostGetTicker",parameters);
    }

    public async Task<object> publicPostGetLatestSignedFeeds (object parameters = null)
    {
        return await this.callAsync ("publicPostGetLatestSignedFeeds",parameters);
    }

    public async Task<object> publicPostGetOptionSettlementPrices (object parameters = null)
    {
        return await this.callAsync ("publicPostGetOptionSettlementPrices",parameters);
    }

    public async Task<object> publicPostGetSpotFeedHistory (object parameters = null)
    {
        return await this.callAsync ("publicPostGetSpotFeedHistory",parameters);
    }

    public async Task<object> publicPostGetSpotFeedHistoryCandles (object parameters = null)
    {
        return await this.callAsync ("publicPostGetSpotFeedHistoryCandles",parameters);
    }

    public async Task<object> publicPostGetFundingRateHistory (object parameters = null)
    {
        return await this.callAsync ("publicPostGetFundingRateHistory",parameters);
    }

    public async Task<object> publicPostGetTradeHistory (object parameters = null)
    {
        return await this.callAsync ("publicPostGetTradeHistory",parameters);
    }

    public async Task<object> publicPostGetOptionSettlementHistory (object parameters = null)
    {
        return await this.callAsync ("publicPostGetOptionSettlementHistory",parameters);
    }

    public async Task<object> publicPostGetLiquidationHistory (object parameters = null)
    {
        return await this.callAsync ("publicPostGetLiquidationHistory",parameters);
    }

    public async Task<object> publicPostGetInterestRateHistory (object parameters = null)
    {
        return await this.callAsync ("publicPostGetInterestRateHistory",parameters);
    }

    public async Task<object> publicPostGetTransaction (object parameters = null)
    {
        return await this.callAsync ("publicPostGetTransaction",parameters);
    }

    public async Task<object> publicPostGetMargin (object parameters = null)
    {
        return await this.callAsync ("publicPostGetMargin",parameters);
    }

    public async Task<object> publicPostMarginWatch (object parameters = null)
    {
        return await this.callAsync ("publicPostMarginWatch",parameters);
    }

    public async Task<object> publicPostValidateInviteCode (object parameters = null)
    {
        return await this.callAsync ("publicPostValidateInviteCode",parameters);
    }

    public async Task<object> publicPostGetPoints (object parameters = null)
    {
        return await this.callAsync ("publicPostGetPoints",parameters);
    }

    public async Task<object> publicPostGetAllPoints (object parameters = null)
    {
        return await this.callAsync ("publicPostGetAllPoints",parameters);
    }

    public async Task<object> publicPostGetPointsLeaderboard (object parameters = null)
    {
        return await this.callAsync ("publicPostGetPointsLeaderboard",parameters);
    }

    public async Task<object> publicPostGetDescendantTree (object parameters = null)
    {
        return await this.callAsync ("publicPostGetDescendantTree",parameters);
    }

    public async Task<object> publicPostGetTreeRoots (object parameters = null)
    {
        return await this.callAsync ("publicPostGetTreeRoots",parameters);
    }

    public async Task<object> publicPostGetSwellPercentPoints (object parameters = null)
    {
        return await this.callAsync ("publicPostGetSwellPercentPoints",parameters);
    }

    public async Task<object> publicPostGetVaultAssets (object parameters = null)
    {
        return await this.callAsync ("publicPostGetVaultAssets",parameters);
    }

    public async Task<object> publicPostGetEtherfiEffectiveBalances (object parameters = null)
    {
        return await this.callAsync ("publicPostGetEtherfiEffectiveBalances",parameters);
    }

    public async Task<object> publicPostGetKelpEffectiveBalances (object parameters = null)
    {
        return await this.callAsync ("publicPostGetKelpEffectiveBalances",parameters);
    }

    public async Task<object> publicPostGetBridgeBalances (object parameters = null)
    {
        return await this.callAsync ("publicPostGetBridgeBalances",parameters);
    }

    public async Task<object> publicPostGetEthenaParticipants (object parameters = null)
    {
        return await this.callAsync ("publicPostGetEthenaParticipants",parameters);
    }

    public async Task<object> publicPostGetVaultShare (object parameters = null)
    {
        return await this.callAsync ("publicPostGetVaultShare",parameters);
    }

    public async Task<object> publicPostGetVaultStatistics (object parameters = null)
    {
        return await this.callAsync ("publicPostGetVaultStatistics",parameters);
    }

    public async Task<object> publicPostGetVaultBalances (object parameters = null)
    {
        return await this.callAsync ("publicPostGetVaultBalances",parameters);
    }

    public async Task<object> publicPostEstimateIntegratorPoints (object parameters = null)
    {
        return await this.callAsync ("publicPostEstimateIntegratorPoints",parameters);
    }

    public async Task<object> publicPostCreateSubaccountDebug (object parameters = null)
    {
        return await this.callAsync ("publicPostCreateSubaccountDebug",parameters);
    }

    public async Task<object> publicPostDepositDebug (object parameters = null)
    {
        return await this.callAsync ("publicPostDepositDebug",parameters);
    }

    public async Task<object> publicPostWithdrawDebug (object parameters = null)
    {
        return await this.callAsync ("publicPostWithdrawDebug",parameters);
    }

    public async Task<object> publicPostSendQuoteDebug (object parameters = null)
    {
        return await this.callAsync ("publicPostSendQuoteDebug",parameters);
    }

    public async Task<object> publicPostExecuteQuoteDebug (object parameters = null)
    {
        return await this.callAsync ("publicPostExecuteQuoteDebug",parameters);
    }

    public async Task<object> publicPostGetInviteCode (object parameters = null)
    {
        return await this.callAsync ("publicPostGetInviteCode",parameters);
    }

    public async Task<object> publicPostRegisterInvite (object parameters = null)
    {
        return await this.callAsync ("publicPostRegisterInvite",parameters);
    }

    public async Task<object> publicPostGetTime (object parameters = null)
    {
        return await this.callAsync ("publicPostGetTime",parameters);
    }

    public async Task<object> publicPostGetLiveIncidents (object parameters = null)
    {
        return await this.callAsync ("publicPostGetLiveIncidents",parameters);
    }

    public async Task<object> publicPostGetMakerPrograms (object parameters = null)
    {
        return await this.callAsync ("publicPostGetMakerPrograms",parameters);
    }

    public async Task<object> publicPostGetMakerProgramScores (object parameters = null)
    {
        return await this.callAsync ("publicPostGetMakerProgramScores",parameters);
    }

    public async Task<object> privatePostGetAccount (object parameters = null)
    {
        return await this.callAsync ("privatePostGetAccount",parameters);
    }

    public async Task<object> privatePostCreateSubaccount (object parameters = null)
    {
        return await this.callAsync ("privatePostCreateSubaccount",parameters);
    }

    public async Task<object> privatePostGetSubaccount (object parameters = null)
    {
        return await this.callAsync ("privatePostGetSubaccount",parameters);
    }

    public async Task<object> privatePostGetSubaccounts (object parameters = null)
    {
        return await this.callAsync ("privatePostGetSubaccounts",parameters);
    }

    public async Task<object> privatePostGetAllPortfolios (object parameters = null)
    {
        return await this.callAsync ("privatePostGetAllPortfolios",parameters);
    }

    public async Task<object> privatePostChangeSubaccountLabel (object parameters = null)
    {
        return await this.callAsync ("privatePostChangeSubaccountLabel",parameters);
    }

    public async Task<object> privatePostGetNotificationsv (object parameters = null)
    {
        return await this.callAsync ("privatePostGetNotificationsv",parameters);
    }

    public async Task<object> privatePostUpdateNotifications (object parameters = null)
    {
        return await this.callAsync ("privatePostUpdateNotifications",parameters);
    }

    public async Task<object> privatePostDeposit (object parameters = null)
    {
        return await this.callAsync ("privatePostDeposit",parameters);
    }

    public async Task<object> privatePostWithdraw (object parameters = null)
    {
        return await this.callAsync ("privatePostWithdraw",parameters);
    }

    public async Task<object> privatePostTransferErc20 (object parameters = null)
    {
        return await this.callAsync ("privatePostTransferErc20",parameters);
    }

    public async Task<object> privatePostTransferPosition (object parameters = null)
    {
        return await this.callAsync ("privatePostTransferPosition",parameters);
    }

    public async Task<object> privatePostTransferPositions (object parameters = null)
    {
        return await this.callAsync ("privatePostTransferPositions",parameters);
    }

    public async Task<object> privatePostOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostOrder",parameters);
    }

    public async Task<object> privatePostReplace (object parameters = null)
    {
        return await this.callAsync ("privatePostReplace",parameters);
    }

    public async Task<object> privatePostOrderDebug (object parameters = null)
    {
        return await this.callAsync ("privatePostOrderDebug",parameters);
    }

    public async Task<object> privatePostGetOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostGetOrder",parameters);
    }

    public async Task<object> privatePostGetOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostGetOrders",parameters);
    }

    public async Task<object> privatePostGetOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostGetOpenOrders",parameters);
    }

    public async Task<object> privatePostCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostCancel",parameters);
    }

    public async Task<object> privatePostCancelByLabel (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelByLabel",parameters);
    }

    public async Task<object> privatePostCancelByNonce (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelByNonce",parameters);
    }

    public async Task<object> privatePostCancelByInstrument (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelByInstrument",parameters);
    }

    public async Task<object> privatePostCancelAll (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelAll",parameters);
    }

    public async Task<object> privatePostCancelTriggerOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelTriggerOrder",parameters);
    }

    public async Task<object> privatePostGetOrderHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetOrderHistory",parameters);
    }

    public async Task<object> privatePostGetTradeHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetTradeHistory",parameters);
    }

    public async Task<object> privatePostGetDepositHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetDepositHistory",parameters);
    }

    public async Task<object> privatePostGetWithdrawalHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetWithdrawalHistory",parameters);
    }

    public async Task<object> privatePostSendRfq (object parameters = null)
    {
        return await this.callAsync ("privatePostSendRfq",parameters);
    }

    public async Task<object> privatePostCancelRfq (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelRfq",parameters);
    }

    public async Task<object> privatePostCancelBatchRfqs (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelBatchRfqs",parameters);
    }

    public async Task<object> privatePostGetRfqs (object parameters = null)
    {
        return await this.callAsync ("privatePostGetRfqs",parameters);
    }

    public async Task<object> privatePostPollRfqs (object parameters = null)
    {
        return await this.callAsync ("privatePostPollRfqs",parameters);
    }

    public async Task<object> privatePostSendQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostSendQuote",parameters);
    }

    public async Task<object> privatePostCancelQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelQuote",parameters);
    }

    public async Task<object> privatePostCancelBatchQuotes (object parameters = null)
    {
        return await this.callAsync ("privatePostCancelBatchQuotes",parameters);
    }

    public async Task<object> privatePostGetQuotes (object parameters = null)
    {
        return await this.callAsync ("privatePostGetQuotes",parameters);
    }

    public async Task<object> privatePostPollQuotes (object parameters = null)
    {
        return await this.callAsync ("privatePostPollQuotes",parameters);
    }

    public async Task<object> privatePostExecuteQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostExecuteQuote",parameters);
    }

    public async Task<object> privatePostRfqGetBestQuote (object parameters = null)
    {
        return await this.callAsync ("privatePostRfqGetBestQuote",parameters);
    }

    public async Task<object> privatePostGetMargin (object parameters = null)
    {
        return await this.callAsync ("privatePostGetMargin",parameters);
    }

    public async Task<object> privatePostGetCollaterals (object parameters = null)
    {
        return await this.callAsync ("privatePostGetCollaterals",parameters);
    }

    public async Task<object> privatePostGetPositions (object parameters = null)
    {
        return await this.callAsync ("privatePostGetPositions",parameters);
    }

    public async Task<object> privatePostGetOptionSettlementHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetOptionSettlementHistory",parameters);
    }

    public async Task<object> privatePostGetSubaccountValueHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetSubaccountValueHistory",parameters);
    }

    public async Task<object> privatePostExpiredAndCancelledHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostExpiredAndCancelledHistory",parameters);
    }

    public async Task<object> privatePostGetFundingHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetFundingHistory",parameters);
    }

    public async Task<object> privatePostGetInterestHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetInterestHistory",parameters);
    }

    public async Task<object> privatePostGetErc20TransferHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetErc20TransferHistory",parameters);
    }

    public async Task<object> privatePostGetLiquidationHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetLiquidationHistory",parameters);
    }

    public async Task<object> privatePostLiquidate (object parameters = null)
    {
        return await this.callAsync ("privatePostLiquidate",parameters);
    }

    public async Task<object> privatePostGetLiquidatorHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetLiquidatorHistory",parameters);
    }

    public async Task<object> privatePostSessionKeys (object parameters = null)
    {
        return await this.callAsync ("privatePostSessionKeys",parameters);
    }

    public async Task<object> privatePostEditSessionKey (object parameters = null)
    {
        return await this.callAsync ("privatePostEditSessionKey",parameters);
    }

    public async Task<object> privatePostRegisterScopedSessionKey (object parameters = null)
    {
        return await this.callAsync ("privatePostRegisterScopedSessionKey",parameters);
    }

    public async Task<object> privatePostGetMmpConfig (object parameters = null)
    {
        return await this.callAsync ("privatePostGetMmpConfig",parameters);
    }

    public async Task<object> privatePostSetMmpConfig (object parameters = null)
    {
        return await this.callAsync ("privatePostSetMmpConfig",parameters);
    }

    public async Task<object> privatePostResetMmp (object parameters = null)
    {
        return await this.callAsync ("privatePostResetMmp",parameters);
    }

    public async Task<object> privatePostSetCancelOnDisconnect (object parameters = null)
    {
        return await this.callAsync ("privatePostSetCancelOnDisconnect",parameters);
    }

    public async Task<object> privatePostGetInviteCode (object parameters = null)
    {
        return await this.callAsync ("privatePostGetInviteCode",parameters);
    }

    public async Task<object> privatePostRegisterInvite (object parameters = null)
    {
        return await this.callAsync ("privatePostRegisterInvite",parameters);
    }

}