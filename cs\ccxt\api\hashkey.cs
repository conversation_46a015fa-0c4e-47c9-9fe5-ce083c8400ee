// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class hashkey : Exchange
{
    public hashkey (object args = null): base(args) {}

    public async Task<object> publicGetApiV1ExchangeInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetApiV1ExchangeInfo",parameters);
    }

    public async Task<object> publicGetQuoteV1Depth (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteV1Depth",parameters);
    }

    public async Task<object> publicGetQuoteV1Trades (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteV1Trades",parameters);
    }

    public async Task<object> publicGetQuoteV1Klines (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteV1Klines",parameters);
    }

    public async Task<object> publicGetQuoteV1Ticker24hr (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteV1Ticker24hr",parameters);
    }

    public async Task<object> publicGetQuoteV1TickerPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteV1TickerPrice",parameters);
    }

    public async Task<object> publicGetQuoteV1TickerBookTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteV1TickerBookTicker",parameters);
    }

    public async Task<object> publicGetQuoteV1DepthMerged (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteV1DepthMerged",parameters);
    }

    public async Task<object> publicGetQuoteV1MarkPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteV1MarkPrice",parameters);
    }

    public async Task<object> publicGetQuoteV1Index (object parameters = null)
    {
        return await this.callAsync ("publicGetQuoteV1Index",parameters);
    }

    public async Task<object> publicGetApiV1FuturesFundingRate (object parameters = null)
    {
        return await this.callAsync ("publicGetApiV1FuturesFundingRate",parameters);
    }

    public async Task<object> publicGetApiV1FuturesHistoryFundingRate (object parameters = null)
    {
        return await this.callAsync ("publicGetApiV1FuturesHistoryFundingRate",parameters);
    }

    public async Task<object> publicGetApiV1Ping (object parameters = null)
    {
        return await this.callAsync ("publicGetApiV1Ping",parameters);
    }

    public async Task<object> publicGetApiV1Time (object parameters = null)
    {
        return await this.callAsync ("publicGetApiV1Time",parameters);
    }

    public async Task<object> privateGetApiV1SpotOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1SpotOrder",parameters);
    }

    public async Task<object> privateGetApiV1SpotOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1SpotOpenOrders",parameters);
    }

    public async Task<object> privateGetApiV1SpotTradeOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1SpotTradeOrders",parameters);
    }

    public async Task<object> privateGetApiV1FuturesLeverage (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesLeverage",parameters);
    }

    public async Task<object> privateGetApiV1FuturesOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesOrder",parameters);
    }

    public async Task<object> privateGetApiV1FuturesOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesOpenOrders",parameters);
    }

    public async Task<object> privateGetApiV1FuturesUserTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesUserTrades",parameters);
    }

    public async Task<object> privateGetApiV1FuturesPositions (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesPositions",parameters);
    }

    public async Task<object> privateGetApiV1FuturesHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesHistoryOrders",parameters);
    }

    public async Task<object> privateGetApiV1FuturesBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesBalance",parameters);
    }

    public async Task<object> privateGetApiV1FuturesLiquidationAssignStatus (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesLiquidationAssignStatus",parameters);
    }

    public async Task<object> privateGetApiV1FuturesRiskLimit (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesRiskLimit",parameters);
    }

    public async Task<object> privateGetApiV1FuturesCommissionRate (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesCommissionRate",parameters);
    }

    public async Task<object> privateGetApiV1FuturesGetBestOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesGetBestOrder",parameters);
    }

    public async Task<object> privateGetApiV1AccountVipInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1AccountVipInfo",parameters);
    }

    public async Task<object> privateGetApiV1Account (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1Account",parameters);
    }

    public async Task<object> privateGetApiV1AccountTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1AccountTrades",parameters);
    }

    public async Task<object> privateGetApiV1AccountType (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1AccountType",parameters);
    }

    public async Task<object> privateGetApiV1AccountCheckApiKey (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1AccountCheckApiKey",parameters);
    }

    public async Task<object> privateGetApiV1AccountBalanceFlow (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1AccountBalanceFlow",parameters);
    }

    public async Task<object> privateGetApiV1SpotSubAccountOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1SpotSubAccountOpenOrders",parameters);
    }

    public async Task<object> privateGetApiV1SpotSubAccountTradeOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1SpotSubAccountTradeOrders",parameters);
    }

    public async Task<object> privateGetApiV1SubAccountTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1SubAccountTrades",parameters);
    }

    public async Task<object> privateGetApiV1FuturesSubAccountOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesSubAccountOpenOrders",parameters);
    }

    public async Task<object> privateGetApiV1FuturesSubAccountHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesSubAccountHistoryOrders",parameters);
    }

    public async Task<object> privateGetApiV1FuturesSubAccountUserTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1FuturesSubAccountUserTrades",parameters);
    }

    public async Task<object> privateGetApiV1AccountDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1AccountDepositAddress",parameters);
    }

    public async Task<object> privateGetApiV1AccountDepositOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1AccountDepositOrders",parameters);
    }

    public async Task<object> privateGetApiV1AccountWithdrawOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetApiV1AccountWithdrawOrders",parameters);
    }

    public async Task<object> privatePostApiV1UserDataStream (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1UserDataStream",parameters);
    }

    public async Task<object> privatePostApiV1SpotOrderTest (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1SpotOrderTest",parameters);
    }

    public async Task<object> privatePostApiV1SpotOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1SpotOrder",parameters);
    }

    public async Task<object> privatePostApiV11SpotOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV11SpotOrder",parameters);
    }

    public async Task<object> privatePostApiV1SpotBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1SpotBatchOrders",parameters);
    }

    public async Task<object> privatePostApiV1FuturesLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1FuturesLeverage",parameters);
    }

    public async Task<object> privatePostApiV1FuturesOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1FuturesOrder",parameters);
    }

    public async Task<object> privatePostApiV1FuturesPositionTradingStop (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1FuturesPositionTradingStop",parameters);
    }

    public async Task<object> privatePostApiV1FuturesBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1FuturesBatchOrders",parameters);
    }

    public async Task<object> privatePostApiV1AccountAssetTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1AccountAssetTransfer",parameters);
    }

    public async Task<object> privatePostApiV1AccountAuthAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1AccountAuthAddress",parameters);
    }

    public async Task<object> privatePostApiV1AccountWithdraw (object parameters = null)
    {
        return await this.callAsync ("privatePostApiV1AccountWithdraw",parameters);
    }

    public async Task<object> privatePutApiV1UserDataStream (object parameters = null)
    {
        return await this.callAsync ("privatePutApiV1UserDataStream",parameters);
    }

    public async Task<object> privateDeleteApiV1SpotOrder (object parameters = null)
    {
        return await this.callAsync ("privateDeleteApiV1SpotOrder",parameters);
    }

    public async Task<object> privateDeleteApiV1SpotOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteApiV1SpotOpenOrders",parameters);
    }

    public async Task<object> privateDeleteApiV1SpotCancelOrderByIds (object parameters = null)
    {
        return await this.callAsync ("privateDeleteApiV1SpotCancelOrderByIds",parameters);
    }

    public async Task<object> privateDeleteApiV1FuturesOrder (object parameters = null)
    {
        return await this.callAsync ("privateDeleteApiV1FuturesOrder",parameters);
    }

    public async Task<object> privateDeleteApiV1FuturesBatchOrders (object parameters = null)
    {
        return await this.callAsync ("privateDeleteApiV1FuturesBatchOrders",parameters);
    }

    public async Task<object> privateDeleteApiV1FuturesCancelOrderByIds (object parameters = null)
    {
        return await this.callAsync ("privateDeleteApiV1FuturesCancelOrderByIds",parameters);
    }

    public async Task<object> privateDeleteApiV1UserDataStream (object parameters = null)
    {
        return await this.callAsync ("privateDeleteApiV1UserDataStream",parameters);
    }

}