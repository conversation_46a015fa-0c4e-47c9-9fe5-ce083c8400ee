#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CCXT Python 示例
演示如何使用 CCXT 库获取交易所信息和市场数据
"""

import ccxt
import asyncio
import sys
import os

# 添加当前目录到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'python'))

def print_separator(title):
    """打印分隔符"""
    print(f"\n=== {title} ===")

async def main():
    print_separator("CCXT Python 示例")
    
    # 显示 CCXT 版本和支持的交易所数量
    print(f"CCXT 版本: {ccxt.__version__}")
    print(f"支持的交易所数量: {len(ccxt.exchanges)}")
    
    # 显示前10个交易所
    print("\n前10个支持的交易所:")
    exchange_list = list(ccxt.exchanges)
    for i, exchange_id in enumerate(exchange_list[:10], 1):
        print(f"{i}. {exchange_id}")
    
    # 创建一个交易所实例（使用 Binance 作为示例）
    exchange = ccxt.binance({
        'sandbox': True,  # 使用测试环境
        'enableRateLimit': True,
        'timeout': 30000,
    })
    
    try:
        print_separator(f"{exchange.name} 交易所信息")
        print(f"交易所 ID: {exchange.id}")
        print(f"API 版本: {getattr(exchange, 'version', 'N/A')}")
        print(f"是否支持 CORS: {'是' if getattr(exchange, 'cors', False) else '否'}")
        print(f"速率限制: {exchange.rateLimit}ms")
        
        # 获取市场信息
        print("\n正在获取市场信息...")
        markets = await exchange.load_markets()
        market_count = len(markets)
        print(f"可用交易对数量: {market_count}")
        
        # 显示前5个交易对
        print("\n前5个交易对:")
        market_symbols = list(markets.keys())
        for i, symbol in enumerate(market_symbols[:5], 1):
            market = markets[symbol]
            print(f"{i}. {symbol} ({market['base']}/{market['quote']})")
        
        # 获取 BTC/USDT 的行情数据（如果可用）
        if 'BTC/USDT' in markets:
            print_separator("BTC/USDT 行情数据")
            ticker = await exchange.fetch_ticker('BTC/USDT')
            print(f"最新价格: ${ticker['last']}")
            print(f"24h 最高价: ${ticker['high']}")
            print(f"24h 最低价: ${ticker['low']}")
            print(f"24h 成交量: {ticker['baseVolume']} BTC")
            if ticker['percentage']:
                print(f"24h 涨跌幅: {ticker['percentage']:.2f}%")
            else:
                print("24h 涨跌幅: N/A")
        
        # 获取订单簿数据
        if 'BTC/USDT' in markets:
            print_separator("BTC/USDT 订单簿 (前5档)")
            orderbook = await exchange.fetch_order_book('BTC/USDT', 5)
            
            print("买单 (Bids):")
            for i, (price, amount) in enumerate(orderbook['bids'][:5], 1):
                print(f"  {i}. 价格: ${price}, 数量: {amount}")
            
            print("卖单 (Asks):")
            for i, (price, amount) in enumerate(orderbook['asks'][:5], 1):
                print(f"  {i}. 价格: ${price}, 数量: {amount}")
        
    except Exception as error:
        print(f"获取数据时出错: {error}")
        print("这可能是因为网络连接问题或API限制")
    
    finally:
        await exchange.close()
    
    print_separator("示例完成")
    print("更多示例请查看 examples/ 目录")

def sync_main():
    """同步版本的主函数"""
    print_separator("CCXT Python 同步示例")
    
    # 显示 CCXT 版本和支持的交易所数量
    print(f"CCXT 版本: {ccxt.__version__}")
    print(f"支持的交易所数量: {len(ccxt.exchanges)}")
    
    # 创建同步交易所实例
    exchange = ccxt.binance({
        'sandbox': True,
        'enableRateLimit': True,
    })
    
    try:
        print_separator(f"{exchange.name} 基本信息")
        print(f"交易所 ID: {exchange.id}")
        print(f"支持的功能:")
        
        # 检查支持的功能
        capabilities = exchange.has
        important_features = [
            'fetchTicker', 'fetchTickers', 'fetchOrderBook', 
            'fetchTrades', 'fetchOHLCV', 'fetchBalance'
        ]
        
        for feature in important_features:
            status = "✓" if capabilities.get(feature) else "✗"
            print(f"  {status} {feature}")
        
    except Exception as error:
        print(f"获取信息时出错: {error}")
    
    print_separator("同步示例完成")

if __name__ == '__main__':
    print("选择运行模式:")
    print("1. 异步模式 (推荐)")
    print("2. 同步模式")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        if choice == '2':
            sync_main()
        else:
            asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
