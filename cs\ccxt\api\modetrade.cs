// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class modetrade : Exchange
{
    public modetrade (object args = null): base(args) {}

    public async Task<object> v1PublicGetPublicVolumeStats (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicVolumeStats",parameters);
    }

    public async Task<object> v1PublicGetPublicBrokerName (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicBrokerName",parameters);
    }

    public async Task<object> v1PublicGetPublicChainInfoBrokerId (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicChainInfoBrokerId",parameters);
    }

    public async Task<object> v1PublicGetPublicSystemInfo (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicSystemInfo",parameters);
    }

    public async Task<object> v1PublicGetPublicVaultBalance (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicVaultBalance",parameters);
    }

    public async Task<object> v1PublicGetPublicInsurancefund (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicInsurancefund",parameters);
    }

    public async Task<object> v1PublicGetPublicChainInfo (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicChainInfo",parameters);
    }

    public async Task<object> v1PublicGetFaucetUsdc (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetFaucetUsdc",parameters);
    }

    public async Task<object> v1PublicGetPublicAccount (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicAccount",parameters);
    }

    public async Task<object> v1PublicGetGetAccount (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetGetAccount",parameters);
    }

    public async Task<object> v1PublicGetRegistrationNonce (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetRegistrationNonce",parameters);
    }

    public async Task<object> v1PublicGetGetOrderlyKey (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetGetOrderlyKey",parameters);
    }

    public async Task<object> v1PublicGetPublicLiquidation (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicLiquidation",parameters);
    }

    public async Task<object> v1PublicGetPublicLiquidatedPositions (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicLiquidatedPositions",parameters);
    }

    public async Task<object> v1PublicGetPublicConfig (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicConfig",parameters);
    }

    public async Task<object> v1PublicGetPublicCampaignRanking (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicCampaignRanking",parameters);
    }

    public async Task<object> v1PublicGetPublicCampaignStats (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicCampaignStats",parameters);
    }

    public async Task<object> v1PublicGetPublicCampaignUser (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicCampaignUser",parameters);
    }

    public async Task<object> v1PublicGetPublicCampaignStatsDetails (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicCampaignStatsDetails",parameters);
    }

    public async Task<object> v1PublicGetPublicCampaigns (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicCampaigns",parameters);
    }

    public async Task<object> v1PublicGetPublicPointsLeaderboard (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicPointsLeaderboard",parameters);
    }

    public async Task<object> v1PublicGetClientPoints (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetClientPoints",parameters);
    }

    public async Task<object> v1PublicGetPublicPointsEpoch (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicPointsEpoch",parameters);
    }

    public async Task<object> v1PublicGetPublicPointsEpochDates (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicPointsEpochDates",parameters);
    }

    public async Task<object> v1PublicGetPublicReferralCheckRefCode (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicReferralCheckRefCode",parameters);
    }

    public async Task<object> v1PublicGetPublicReferralVerifyRefCode (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicReferralVerifyRefCode",parameters);
    }

    public async Task<object> v1PublicGetReferralAdminInfo (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetReferralAdminInfo",parameters);
    }

    public async Task<object> v1PublicGetReferralInfo (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetReferralInfo",parameters);
    }

    public async Task<object> v1PublicGetReferralRefereeInfo (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetReferralRefereeInfo",parameters);
    }

    public async Task<object> v1PublicGetReferralRefereeRebateSummary (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetReferralRefereeRebateSummary",parameters);
    }

    public async Task<object> v1PublicGetReferralRefereeHistory (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetReferralRefereeHistory",parameters);
    }

    public async Task<object> v1PublicGetReferralReferralHistory (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetReferralReferralHistory",parameters);
    }

    public async Task<object> v1PublicGetReferralRebateSummary (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetReferralRebateSummary",parameters);
    }

    public async Task<object> v1PublicGetClientDistributionHistory (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetClientDistributionHistory",parameters);
    }

    public async Task<object> v1PublicGetTvConfig (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetTvConfig",parameters);
    }

    public async Task<object> v1PublicGetTvHistory (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetTvHistory",parameters);
    }

    public async Task<object> v1PublicGetTvSymbolInfo (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetTvSymbolInfo",parameters);
    }

    public async Task<object> v1PublicGetPublicFundingRateHistory (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicFundingRateHistory",parameters);
    }

    public async Task<object> v1PublicGetPublicFundingRateSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicFundingRateSymbol",parameters);
    }

    public async Task<object> v1PublicGetPublicFundingRates (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicFundingRates",parameters);
    }

    public async Task<object> v1PublicGetPublicInfo (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicInfo",parameters);
    }

    public async Task<object> v1PublicGetPublicInfoSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicInfoSymbol",parameters);
    }

    public async Task<object> v1PublicGetPublicMarketTrades (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicMarketTrades",parameters);
    }

    public async Task<object> v1PublicGetPublicToken (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicToken",parameters);
    }

    public async Task<object> v1PublicGetPublicFutures (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicFutures",parameters);
    }

    public async Task<object> v1PublicGetPublicFuturesSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PublicGetPublicFuturesSymbol",parameters);
    }

    public async Task<object> v1PublicPostRegisterAccount (object parameters = null)
    {
        return await this.callAsync ("v1PublicPostRegisterAccount",parameters);
    }

    public async Task<object> v1PrivateGetClientKeyInfo (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientKeyInfo",parameters);
    }

    public async Task<object> v1PrivateGetClientOrderlyKeyIpRestriction (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientOrderlyKeyIpRestriction",parameters);
    }

    public async Task<object> v1PrivateGetOrderOid (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetOrderOid",parameters);
    }

    public async Task<object> v1PrivateGetClientOrderClientOrderId (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientOrderClientOrderId",parameters);
    }

    public async Task<object> v1PrivateGetAlgoOrderOid (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetAlgoOrderOid",parameters);
    }

    public async Task<object> v1PrivateGetAlgoClientOrderClientOrderId (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetAlgoClientOrderClientOrderId",parameters);
    }

    public async Task<object> v1PrivateGetOrders (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetOrders",parameters);
    }

    public async Task<object> v1PrivateGetAlgoOrders (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetAlgoOrders",parameters);
    }

    public async Task<object> v1PrivateGetTradeTid (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetTradeTid",parameters);
    }

    public async Task<object> v1PrivateGetTrades (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetTrades",parameters);
    }

    public async Task<object> v1PrivateGetOrderOidTrades (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetOrderOidTrades",parameters);
    }

    public async Task<object> v1PrivateGetClientLiquidatorLiquidations (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientLiquidatorLiquidations",parameters);
    }

    public async Task<object> v1PrivateGetLiquidations (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetLiquidations",parameters);
    }

    public async Task<object> v1PrivateGetAssetHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetAssetHistory",parameters);
    }

    public async Task<object> v1PrivateGetClientHolding (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientHolding",parameters);
    }

    public async Task<object> v1PrivateGetWithdrawNonce (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetWithdrawNonce",parameters);
    }

    public async Task<object> v1PrivateGetSettleNonce (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetSettleNonce",parameters);
    }

    public async Task<object> v1PrivateGetPnlSettlementHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetPnlSettlementHistory",parameters);
    }

    public async Task<object> v1PrivateGetVolumeUserDaily (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetVolumeUserDaily",parameters);
    }

    public async Task<object> v1PrivateGetVolumeUserStats (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetVolumeUserStats",parameters);
    }

    public async Task<object> v1PrivateGetClientStatistics (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientStatistics",parameters);
    }

    public async Task<object> v1PrivateGetClientInfo (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientInfo",parameters);
    }

    public async Task<object> v1PrivateGetClientStatisticsDaily (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetClientStatisticsDaily",parameters);
    }

    public async Task<object> v1PrivateGetPositions (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetPositions",parameters);
    }

    public async Task<object> v1PrivateGetPositionSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetPositionSymbol",parameters);
    }

    public async Task<object> v1PrivateGetFundingFeeHistory (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetFundingFeeHistory",parameters);
    }

    public async Task<object> v1PrivateGetNotificationInboxNotifications (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetNotificationInboxNotifications",parameters);
    }

    public async Task<object> v1PrivateGetNotificationInboxUnread (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetNotificationInboxUnread",parameters);
    }

    public async Task<object> v1PrivateGetVolumeBrokerDaily (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetVolumeBrokerDaily",parameters);
    }

    public async Task<object> v1PrivateGetBrokerFeeRateDefault (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetBrokerFeeRateDefault",parameters);
    }

    public async Task<object> v1PrivateGetBrokerUserInfo (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetBrokerUserInfo",parameters);
    }

    public async Task<object> v1PrivateGetOrderbookSymbol (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetOrderbookSymbol",parameters);
    }

    public async Task<object> v1PrivateGetKline (object parameters = null)
    {
        return await this.callAsync ("v1PrivateGetKline",parameters);
    }

    public async Task<object> v1PrivatePostOrderlyKey (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostOrderlyKey",parameters);
    }

    public async Task<object> v1PrivatePostClientSetOrderlyKeyIpRestriction (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClientSetOrderlyKeyIpRestriction",parameters);
    }

    public async Task<object> v1PrivatePostClientResetOrderlyKeyIpRestriction (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClientResetOrderlyKeyIpRestriction",parameters);
    }

    public async Task<object> v1PrivatePostOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostOrder",parameters);
    }

    public async Task<object> v1PrivatePostBatchOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostBatchOrder",parameters);
    }

    public async Task<object> v1PrivatePostAlgoOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostAlgoOrder",parameters);
    }

    public async Task<object> v1PrivatePostLiquidation (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostLiquidation",parameters);
    }

    public async Task<object> v1PrivatePostClaimInsuranceFund (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClaimInsuranceFund",parameters);
    }

    public async Task<object> v1PrivatePostWithdrawRequest (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostWithdrawRequest",parameters);
    }

    public async Task<object> v1PrivatePostSettlePnl (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostSettlePnl",parameters);
    }

    public async Task<object> v1PrivatePostNotificationInboxMarkRead (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostNotificationInboxMarkRead",parameters);
    }

    public async Task<object> v1PrivatePostNotificationInboxMarkReadAll (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostNotificationInboxMarkReadAll",parameters);
    }

    public async Task<object> v1PrivatePostClientLeverage (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClientLeverage",parameters);
    }

    public async Task<object> v1PrivatePostClientMaintenanceConfig (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostClientMaintenanceConfig",parameters);
    }

    public async Task<object> v1PrivatePostDelegateSigner (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostDelegateSigner",parameters);
    }

    public async Task<object> v1PrivatePostDelegateOrderlyKey (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostDelegateOrderlyKey",parameters);
    }

    public async Task<object> v1PrivatePostDelegateSettlePnl (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostDelegateSettlePnl",parameters);
    }

    public async Task<object> v1PrivatePostDelegateWithdrawRequest (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostDelegateWithdrawRequest",parameters);
    }

    public async Task<object> v1PrivatePostBrokerFeeRateSet (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostBrokerFeeRateSet",parameters);
    }

    public async Task<object> v1PrivatePostBrokerFeeRateSetDefault (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostBrokerFeeRateSetDefault",parameters);
    }

    public async Task<object> v1PrivatePostBrokerFeeRateDefault (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostBrokerFeeRateDefault",parameters);
    }

    public async Task<object> v1PrivatePostReferralCreate (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostReferralCreate",parameters);
    }

    public async Task<object> v1PrivatePostReferralUpdate (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostReferralUpdate",parameters);
    }

    public async Task<object> v1PrivatePostReferralBind (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostReferralBind",parameters);
    }

    public async Task<object> v1PrivatePostReferralEditSplit (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePostReferralEditSplit",parameters);
    }

    public async Task<object> v1PrivatePutOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePutOrder",parameters);
    }

    public async Task<object> v1PrivatePutAlgoOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivatePutAlgoOrder",parameters);
    }

    public async Task<object> v1PrivateDeleteOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteOrder",parameters);
    }

    public async Task<object> v1PrivateDeleteAlgoOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteAlgoOrder",parameters);
    }

    public async Task<object> v1PrivateDeleteClientOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteClientOrder",parameters);
    }

    public async Task<object> v1PrivateDeleteAlgoClientOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteAlgoClientOrder",parameters);
    }

    public async Task<object> v1PrivateDeleteAlgoOrders (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteAlgoOrders",parameters);
    }

    public async Task<object> v1PrivateDeleteOrders (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteOrders",parameters);
    }

    public async Task<object> v1PrivateDeleteBatchOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteBatchOrder",parameters);
    }

    public async Task<object> v1PrivateDeleteClientBatchOrder (object parameters = null)
    {
        return await this.callAsync ("v1PrivateDeleteClientBatchOrder",parameters);
    }

}