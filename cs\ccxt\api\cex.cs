// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class cex : Exchange
{
    public cex (object args = null): base(args) {}

    public async Task<object> publicPostGetServerTime (object parameters = null)
    {
        return await this.callAsync ("publicPostGetServerTime",parameters);
    }

    public async Task<object> publicPostGetPairsInfo (object parameters = null)
    {
        return await this.callAsync ("publicPostGetPairsInfo",parameters);
    }

    public async Task<object> publicPostGetCurrenciesInfo (object parameters = null)
    {
        return await this.callAsync ("publicPostGetCurrenciesInfo",parameters);
    }

    public async Task<object> publicPostGetProcessingInfo (object parameters = null)
    {
        return await this.callAsync ("publicPostGetProcessingInfo",parameters);
    }

    public async Task<object> publicPostGetTicker (object parameters = null)
    {
        return await this.callAsync ("publicPostGetTicker",parameters);
    }

    public async Task<object> publicPostGetTradeHistory (object parameters = null)
    {
        return await this.callAsync ("publicPostGetTradeHistory",parameters);
    }

    public async Task<object> publicPostGetOrderBook (object parameters = null)
    {
        return await this.callAsync ("publicPostGetOrderBook",parameters);
    }

    public async Task<object> publicPostGetCandles (object parameters = null)
    {
        return await this.callAsync ("publicPostGetCandles",parameters);
    }

    public async Task<object> privatePostGetMyCurrentFee (object parameters = null)
    {
        return await this.callAsync ("privatePostGetMyCurrentFee",parameters);
    }

    public async Task<object> privatePostGetFeeStrategy (object parameters = null)
    {
        return await this.callAsync ("privatePostGetFeeStrategy",parameters);
    }

    public async Task<object> privatePostGetMyVolume (object parameters = null)
    {
        return await this.callAsync ("privatePostGetMyVolume",parameters);
    }

    public async Task<object> privatePostDoCreateAccount (object parameters = null)
    {
        return await this.callAsync ("privatePostDoCreateAccount",parameters);
    }

    public async Task<object> privatePostGetMyAccountStatusV3 (object parameters = null)
    {
        return await this.callAsync ("privatePostGetMyAccountStatusV3",parameters);
    }

    public async Task<object> privatePostGetMyWalletBalance (object parameters = null)
    {
        return await this.callAsync ("privatePostGetMyWalletBalance",parameters);
    }

    public async Task<object> privatePostGetMyOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostGetMyOrders",parameters);
    }

    public async Task<object> privatePostDoMyNewOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostDoMyNewOrder",parameters);
    }

    public async Task<object> privatePostDoCancelMyOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostDoCancelMyOrder",parameters);
    }

    public async Task<object> privatePostDoCancelAllOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostDoCancelAllOrders",parameters);
    }

    public async Task<object> privatePostGetOrderBook (object parameters = null)
    {
        return await this.callAsync ("privatePostGetOrderBook",parameters);
    }

    public async Task<object> privatePostGetCandles (object parameters = null)
    {
        return await this.callAsync ("privatePostGetCandles",parameters);
    }

    public async Task<object> privatePostGetTradeHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetTradeHistory",parameters);
    }

    public async Task<object> privatePostGetMyTransactionHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetMyTransactionHistory",parameters);
    }

    public async Task<object> privatePostGetMyFundingHistory (object parameters = null)
    {
        return await this.callAsync ("privatePostGetMyFundingHistory",parameters);
    }

    public async Task<object> privatePostDoMyInternalTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostDoMyInternalTransfer",parameters);
    }

    public async Task<object> privatePostGetProcessingInfo (object parameters = null)
    {
        return await this.callAsync ("privatePostGetProcessingInfo",parameters);
    }

    public async Task<object> privatePostGetDepositAddress (object parameters = null)
    {
        return await this.callAsync ("privatePostGetDepositAddress",parameters);
    }

    public async Task<object> privatePostDoDepositFundsFromWallet (object parameters = null)
    {
        return await this.callAsync ("privatePostDoDepositFundsFromWallet",parameters);
    }

    public async Task<object> privatePostDoWithdrawalFundsToWallet (object parameters = null)
    {
        return await this.callAsync ("privatePostDoWithdrawalFundsToWallet",parameters);
    }

}